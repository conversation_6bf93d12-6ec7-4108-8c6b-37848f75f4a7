namespace Forms.ktech.Shared.ViewModels
{
    /// <summary>
    /// Base interface for all form ViewModels
    /// Provides common properties and methods that all forms should implement
    /// </summary>
    public interface IFormViewModel
    {
        /// <summary>
        /// Unique identifier for the form submission
        /// Used for organizing uploaded files and tracking submissions
        /// </summary>
        Guid SubmissionGuid { get; set; }

        /// <summary>
        /// Validates the form's business rules
        /// </summary>
        /// <returns>True if the form passes all business rules, false otherwise</returns>
        bool IsEligible();

        /// <summary>
        /// Gets a user-friendly description of why the form is not eligible
        /// </summary>
        /// <returns>Error message explaining ineligibility, or null if eligible</returns>
        string? GetIneligibilityReason();
    }
}
