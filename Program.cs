using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Authorization;
using Microsoft.Identity.Web;
using Microsoft.Identity.Web.UI;
using Microsoft.EntityFrameworkCore;
using Forms.ktech.Data;
using Forms.ktech.Services;
using Forms.ktech.Services.SIS;
using Forms.ktech.Configuration;
using Forms.ktech.Features.KuwaitiStudentInfo.Models;
using Forms.ktech.Features.KuwaitiStudentInfo.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.

// Add Entity Framework with database provider selection
builder.Services.AddDbContext<FormsKTechContext>(options =>
{
    var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");
    var databaseProvider = builder.Configuration["DatabaseProvider"];
    var isDevelopment = builder.Environment.IsDevelopment();
    var isMacOS = OperatingSystem.IsMacOS();

    // Use SQLite for development on macOS or when explicitly configured
    if ((isDevelopment && isMacOS) || string.Equals(databaseProvider, "SQLite", StringComparison.OrdinalIgnoreCase))
    {
        options.UseSqlite(connectionString);
        Console.WriteLine($"Using SQLite database: {connectionString}");
    }
    else
    {
        // Use SQL Server for production or Windows development
        options.UseSqlServer(connectionString);
        Console.WriteLine($"Using SQL Server database: {connectionString}");
    }
});

// Add shared services
builder.Services.AddScoped<IFileUploadService, FileUploadService>();
builder.Services.AddScoped<IExportService, ExportService>();
builder.Services.AddHttpContextAccessor();

// Add form handlers
builder.Services.AddScoped<IFormHandler<StudentFormViewModel, StudentInfo>, KuwaitiStudentInfoHandler>();

// Configure SIS API options
builder.Services.Configure<SisApiOptions>(builder.Configuration.GetSection(SisApiOptions.SectionName));

// Add SIS API client with HttpClient
builder.Services.AddHttpClient<ISisApiClient, SisApiClient>()
    .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler()
    {
        // Configure any additional HTTP client settings here
    });

// Add SIS services
builder.Services.AddScoped<ISisSyncService, SisSyncService>();
builder.Services.AddScoped<IStudentLookupService, StudentLookupService>();

// Add document approval and email notification services
builder.Services.AddScoped<IDocumentApprovalService, DocumentApprovalService>();
builder.Services.AddScoped<IEmailNotificationService, EmailNotificationService>();
builder.Services.AddScoped<IEmailTemplateService, EmailTemplateService>();
builder.Services.AddScoped<IEmailPreviewService, EmailPreviewService>();

// Add submission approval and notification services
builder.Services.AddScoped<ISubmissionApprovalService, SubmissionApprovalService>();
builder.Services.AddScoped<ISubmissionNotificationService, SubmissionNotificationService>();

// Add SignalR for real-time sync updates
builder.Services.AddSignalR();

// Add antiforgery services
builder.Services.AddAntiforgery(options =>
{
    options.HeaderName = "X-CSRF-TOKEN";
    options.SuppressXFrameOptionsHeader = false;
});

// Add authentication
builder.Services.AddAuthentication(OpenIdConnectDefaults.AuthenticationScheme)
    .AddMicrosoftIdentityWebApp(builder.Configuration.GetSection("AzureAd"));

builder.Services.AddControllersWithViews(options =>
{
    var policy = new AuthorizationPolicyBuilder()
        .RequireAuthenticatedUser()
        .Build();
    options.Filters.Add(new AuthorizeFilter(policy));
})
.AddRazorOptions(options =>
{
    // Add feature folder view locations
    options.ViewLocationFormats.Clear();
    options.ViewLocationFormats.Add("/Views/{1}/{0}.cshtml");
    options.ViewLocationFormats.Add("/Views/Shared/{0}.cshtml");
    options.ViewLocationFormats.Add("/Features/{1}/Views/{1}/{0}.cshtml");
    options.ViewLocationFormats.Add("/Features/{1}/Views/Shared/{0}.cshtml");
    options.ViewLocationFormats.Add("/Features/Shared/Views/{0}.cshtml");

    // Add area view locations (for future use)
    options.AreaViewLocationFormats.Clear();
    options.AreaViewLocationFormats.Add("/Areas/{2}/Views/{1}/{0}.cshtml");
    options.AreaViewLocationFormats.Add("/Areas/{2}/Views/Shared/{0}.cshtml");
    options.AreaViewLocationFormats.Add("/Views/Shared/{0}.cshtml");
    options.AreaViewLocationFormats.Add("/Features/{2}/Views/{1}/{0}.cshtml");
    options.AreaViewLocationFormats.Add("/Features/{2}/Views/Shared/{0}.cshtml");
});
builder.Services.AddRazorPages()
    .AddMicrosoftIdentityUI();

var app = builder.Build();

// Initialize database for development
if (app.Environment.IsDevelopment())
{
    using (var scope = app.Services.CreateScope())
    {
        var context = scope.ServiceProvider.GetRequiredService<FormsKTechContext>();
        var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

        try
        {
            // Ensure database is created and migrations are applied
            context.Database.EnsureCreated();
            logger.LogInformation("Database initialized successfully");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An error occurred while initializing the database");
        }
    }
}

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");
app.MapRazorPages();

// Map SignalR hubs
app.MapHub<Forms.ktech.Hubs.SisSyncHub>("/hubs/sissync");

app.Run();
