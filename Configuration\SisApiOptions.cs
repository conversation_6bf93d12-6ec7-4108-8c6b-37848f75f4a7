using System.ComponentModel.DataAnnotations;

namespace Forms.ktech.Configuration
{
    /// <summary>
    /// Configuration options for SIS (Student Information System) API integration
    /// Contains settings for API endpoints, authentication, and synchronization behavior
    /// </summary>
    public class SisApiOptions
    {
        /// <summary>
        /// Configuration section name in appsettings.json
        /// </summary>
        public const string SectionName = "Sis<PERSON><PERSON>";

        #region API Connection Settings

        /// <summary>
        /// Base URL for the SIS API
        /// Example: "https://sisapi.ktech.edu.kw/api"
        /// </summary>
        [Required]
        [Url]
        public string BaseUrl { get; set; } = string.Empty;

        /// <summary>
        /// Username for Basic Authentication with SIS API
        /// </summary>
        [Required]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// Password for Basic Authentication with SIS API
        /// </summary>
        [Required]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// HTTP request timeout in seconds
        /// Default: 30 seconds
        /// </summary>
        [Range(5, 300)]
        public int TimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Number of retry attempts for failed API calls
        /// Default: 3 attempts
        /// </summary>
        [Range(0, 10)]
        public int RetryAttempts { get; set; } = 3;

        /// <summary>
        /// Delay between retry attempts in milliseconds
        /// Default: 1000ms (1 second)
        /// </summary>
        [Range(100, 10000)]
        public int RetryDelayMs { get; set; } = 1000;

        #endregion

        #region Sync Configuration

        /// <summary>
        /// Number of records to process in each batch during synchronization
        /// Default: 100 records per batch
        /// </summary>
        [Range(10, 1000)]
        public int BatchSize { get; set; } = 100;

        /// <summary>
        /// Maximum number of concurrent HTTP requests during sync
        /// Default: 5 concurrent requests
        /// </summary>
        [Range(1, 20)]
        public int MaxConcurrentRequests { get; set; } = 5;

        /// <summary>
        /// Indicates if automatic scheduled synchronization is enabled
        /// Default: true
        /// </summary>
        public bool AutoSyncEnabled { get; set; } = true;

        /// <summary>
        /// Cron expression for automatic sync schedule
        /// Default: "0 2 * * *" (daily at 2:00 AM)
        /// </summary>
        public string SyncSchedule { get; set; } = "0 2 * * *";

        /// <summary>
        /// Maximum age of data before it's considered stale (in hours)
        /// Default: 24 hours
        /// </summary>
        [Range(1, 168)] // 1 hour to 1 week
        public int DataFreshnessHours { get; set; } = 24;

        /// <summary>
        /// Number of days to retain sync history records
        /// Default: 90 days
        /// </summary>
        [Range(7, 365)]
        public int MaxSyncHistoryDays { get; set; } = 90;

        /// <summary>
        /// Indicates if incremental sync is enabled
        /// If false, only full sync will be performed
        /// Default: true
        /// </summary>
        public bool EnableIncrementalSync { get; set; } = true;

        #endregion

        #region Circuit Breaker Settings

        /// <summary>
        /// Number of consecutive failures before circuit breaker opens
        /// Default: 5 failures
        /// </summary>
        [Range(3, 20)]
        public int CircuitBreakerFailureThreshold { get; set; } = 5;

        /// <summary>
        /// Duration in seconds to keep circuit breaker open before attempting to close
        /// Default: 60 seconds
        /// </summary>
        [Range(30, 600)]
        public int CircuitBreakerTimeoutSeconds { get; set; } = 60;

        /// <summary>
        /// Number of successful calls required to close the circuit breaker
        /// Default: 3 successful calls
        /// </summary>
        [Range(1, 10)]
        public int CircuitBreakerSuccessThreshold { get; set; } = 3;

        #endregion

        #region API Endpoints

        /// <summary>
        /// Endpoint for fetching all students
        /// Default: "/Students"
        /// </summary>
        public string StudentsEndpoint { get; set; } = "/Students";

        /// <summary>
        /// Endpoint for filtering students
        /// Default: "/Students/filter"
        /// </summary>
        public string StudentsFilterEndpoint { get; set; } = "/Students/filter";

        /// <summary>
        /// Endpoint for fetching enrolled users
        /// Default: "/EnrolledUsers"
        /// </summary>
        public string EnrolledUsersEndpoint { get; set; } = "/EnrolledUsers";

        /// <summary>
        /// Endpoint for filtering enrolled users
        /// Default: "/EnrolledUsers/filter"
        /// </summary>
        public string EnrolledUsersFilterEndpoint { get; set; } = "/EnrolledUsers/filter";

        #endregion

        #region Helper Properties

        /// <summary>
        /// Gets the complete URL for the students endpoint
        /// </summary>
        public string StudentsUrl => $"{BaseUrl.TrimEnd('/')}{StudentsEndpoint}";

        /// <summary>
        /// Gets the complete URL for the students filter endpoint
        /// </summary>
        public string StudentsFilterUrl => $"{BaseUrl.TrimEnd('/')}{StudentsFilterEndpoint}";

        /// <summary>
        /// Gets the complete URL for the enrolled users endpoint
        /// </summary>
        public string EnrolledUsersUrl => $"{BaseUrl.TrimEnd('/')}{EnrolledUsersEndpoint}";

        /// <summary>
        /// Gets the complete URL for the enrolled users filter endpoint
        /// </summary>
        public string EnrolledUsersFilterUrl => $"{BaseUrl.TrimEnd('/')}{EnrolledUsersFilterEndpoint}";

        /// <summary>
        /// Gets the HTTP timeout as a TimeSpan
        /// </summary>
        public TimeSpan Timeout => TimeSpan.FromSeconds(TimeoutSeconds);

        /// <summary>
        /// Gets the retry delay as a TimeSpan
        /// </summary>
        public TimeSpan RetryDelay => TimeSpan.FromMilliseconds(RetryDelayMs);

        /// <summary>
        /// Gets the data freshness threshold as a TimeSpan
        /// </summary>
        public TimeSpan DataFreshnessThreshold => TimeSpan.FromHours(DataFreshnessHours);

        /// <summary>
        /// Gets the circuit breaker timeout as a TimeSpan
        /// </summary>
        public TimeSpan CircuitBreakerTimeout => TimeSpan.FromSeconds(CircuitBreakerTimeoutSeconds);

        #endregion

        #region Validation Methods

        /// <summary>
        /// Validates the configuration settings
        /// </summary>
        /// <returns>List of validation errors, empty if valid</returns>
        public List<string> Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(BaseUrl))
                errors.Add("BaseUrl is required");
            else if (!Uri.TryCreate(BaseUrl, UriKind.Absolute, out _))
                errors.Add("BaseUrl must be a valid URL");

            if (string.IsNullOrWhiteSpace(Username))
                errors.Add("Username is required");

            if (string.IsNullOrWhiteSpace(Password))
                errors.Add("Password is required");

            if (TimeoutSeconds < 5 || TimeoutSeconds > 300)
                errors.Add("TimeoutSeconds must be between 5 and 300");

            if (RetryAttempts < 0 || RetryAttempts > 10)
                errors.Add("RetryAttempts must be between 0 and 10");

            if (BatchSize < 10 || BatchSize > 1000)
                errors.Add("BatchSize must be between 10 and 1000");

            if (MaxConcurrentRequests < 1 || MaxConcurrentRequests > 20)
                errors.Add("MaxConcurrentRequests must be between 1 and 20");

            return errors;
        }

        /// <summary>
        /// Checks if the configuration is valid
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid => Validate().Count == 0;

        #endregion
    }
}
