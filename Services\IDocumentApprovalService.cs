using Forms.ktech.Models;

namespace Forms.ktech.Services
{
    /// <summary>
    /// Interface for document approval service operations
    /// </summary>
    public interface IDocumentApprovalService
    {
        /// <summary>
        /// Approves a document for a submission
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="documentType">The document type</param>
        /// <param name="approvedByUserId">The admin user ID who approved the document</param>
        /// <param name="comments">Optional comments</param>
        /// <returns>The approval record</returns>
        Task<DocumentApproval> ApproveDocumentAsync(int submissionId, string documentType, string approvedByUserId, string? comments = null);

        /// <summary>
        /// Disapproves a document for a submission
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="documentType">The document type</param>
        /// <param name="disapprovedByUserId">The admin user ID who disapproved the document</param>
        /// <param name="reason">The reason for disapproval</param>
        /// <param name="comments">Optional additional comments</param>
        /// <returns>The approval record</returns>
        Task<DocumentApproval> DisapproveDocumentAsync(int submissionId, string documentType, string disapprovedByUserId, string reason, string? comments = null);

        /// <summary>
        /// Gets the approval status for a document
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="documentType">The document type</param>
        /// <returns>The approval record or null if not found</returns>
        Task<DocumentApproval?> GetDocumentApprovalAsync(int submissionId, string documentType);

        /// <summary>
        /// Gets all approval records for a submission
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <returns>List of approval records</returns>
        Task<List<DocumentApproval>> GetSubmissionApprovalsAsync(int submissionId);

        /// <summary>
        /// Gets approval history for a specific document
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="documentType">The document type</param>
        /// <returns>List of approval history records</returns>
        Task<List<DocumentApproval>> GetDocumentApprovalHistoryAsync(int submissionId, string documentType);

        /// <summary>
        /// Bulk approve multiple documents for a submission
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="documentTypes">The document types to approve</param>
        /// <param name="approvedByUserId">The admin user ID who approved the documents</param>
        /// <param name="comments">Optional comments</param>
        /// <returns>List of approval records</returns>
        Task<List<DocumentApproval>> BulkApproveDocumentsAsync(int submissionId, string[] documentTypes, string approvedByUserId, string? comments = null);

        /// <summary>
        /// Gets approval statistics for the admin dashboard
        /// </summary>
        /// <returns>Approval statistics</returns>
        Task<DocumentApprovalStatistics> GetApprovalStatisticsAsync();
    }
}
