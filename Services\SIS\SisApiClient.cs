using Forms.ktech.Configuration;
using Microsoft.Extensions.Options;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Diagnostics;
using System.Globalization;
using Polly;
using Polly.CircuitBreaker;
using Polly.Extensions.Http;

namespace Forms.ktech.Services.SIS
{
    /// <summary>
    /// Implementation of SIS API client with retry policies, circuit breaker, and comprehensive error handling
    /// </summary>
    public class SisApiClient : ISisApiClient, IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly SisApiOptions _options;
        private readonly ILogger<SisApiClient> _logger;
        private readonly IAsyncPolicy<HttpResponseMessage> _retryPolicy;
        private readonly JsonSerializerOptions _jsonOptions;
        private bool _disposed = false;

        public SisApiClient(
            HttpClient httpClient,
            IOptions<SisApiOptions> options,
            ILogger<SisApiClient> logger)
        {
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // Validate configuration
            var validationErrors = _options.Validate();
            if (validationErrors.Any())
            {
                var errors = string.Join(", ", validationErrors);
                throw new InvalidOperationException($"SIS API configuration is invalid: {errors}");
            }

            ConfigureHttpClient();
            _retryPolicy = CreateRetryPolicy();
            _jsonOptions = CreateJsonOptions();

            _logger.LogInformation("SIS API Client initialized with base URL: {BaseUrl}", _options.BaseUrl);
        }

        #region Configuration

        /// <summary>
        /// Configures the HTTP client with authentication and timeout settings
        /// </summary>
        private void ConfigureHttpClient()
        {
            // Set base address - ensure it ends with a slash for proper URL resolution
            var baseUrl = _options.BaseUrl.TrimEnd('/') + "/";
            _httpClient.BaseAddress = new Uri(baseUrl);

            // Configure timeout
            _httpClient.Timeout = _options.Timeout;

            // Set up Basic Authentication
            var credentials = Convert.ToBase64String(
                Encoding.ASCII.GetBytes($"{_options.Username}:{_options.Password}"));
            _httpClient.DefaultRequestHeaders.Authorization =
                new AuthenticationHeaderValue("Basic", credentials);

            // Set common headers
            _httpClient.DefaultRequestHeaders.Accept.Clear();
            _httpClient.DefaultRequestHeaders.Accept.Add(
                new MediaTypeWithQualityHeaderValue("application/json"));
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "Forms.ktech-SIS-Client/1.0");

            _logger.LogDebug("HTTP client configured with base URL: {BaseUrl}, timeout: {Timeout}s", baseUrl, _options.TimeoutSeconds);
        }

        /// <summary>
        /// Creates retry policy with exponential backoff and circuit breaker
        /// </summary>
        private IAsyncPolicy<HttpResponseMessage> CreateRetryPolicy()
        {
            var retryPolicy = HttpPolicyExtensions
                .HandleTransientHttpError()
                .WaitAndRetryAsync(
                    retryCount: _options.RetryAttempts,
                    sleepDurationProvider: retryAttempt =>
                        TimeSpan.FromMilliseconds(_options.RetryDelayMs * Math.Pow(2, retryAttempt - 1)),
                    onRetry: (outcome, timespan, retryCount, context) =>
                    {
                        var reason = outcome.Exception?.Message ?? outcome.Result?.ReasonPhrase ?? "Unknown error";
                        _logger.LogWarning("SIS API retry attempt {RetryCount} after {Delay}ms. Reason: {Reason}",
                            retryCount, timespan.TotalMilliseconds, reason);
                    });

            var circuitBreakerPolicy = HttpPolicyExtensions
                .HandleTransientHttpError()
                .CircuitBreakerAsync(
                    handledEventsAllowedBeforeBreaking: _options.CircuitBreakerFailureThreshold,
                    durationOfBreak: _options.CircuitBreakerTimeout,
                    onBreak: (delegateResult, duration) =>
                    {
                        var reason = delegateResult.Exception?.Message ?? delegateResult.Result?.ReasonPhrase ?? "Unknown error";
                        _logger.LogError("SIS API circuit breaker opened for {Duration}s. Reason: {Reason}",
                            duration.TotalSeconds, reason);
                    },
                    onReset: () =>
                    {
                        _logger.LogInformation("SIS API circuit breaker closed - service recovered");
                    },
                    onHalfOpen: () =>
                    {
                        _logger.LogInformation("SIS API circuit breaker half-open - testing service");
                    });

            return Policy.WrapAsync(retryPolicy, circuitBreakerPolicy);
        }

        /// <summary>
        /// Creates JSON serialization options
        /// </summary>
        private static JsonSerializerOptions CreateJsonOptions()
        {
            return new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            };
        }

        #endregion

        #region Student Data Retrieval

        /// <summary>
        /// Fetches all students from the SIS API
        /// </summary>
        public async Task<IEnumerable<SisStudentApiResponse>> GetAllStudentsAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Fetching all students from SIS API");
                var stopwatch = Stopwatch.StartNew();

                var response = await _retryPolicy.ExecuteAsync(async () =>
                {
                    var endpoint = _options.StudentsEndpoint.TrimStart('/');
                    var httpResponse = await _httpClient.GetAsync(endpoint, cancellationToken);
                    httpResponse.EnsureSuccessStatusCode();
                    return httpResponse;
                });

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogDebug("SIS API response content: {Content}", content);

                var wrappedResponse = JsonSerializer.Deserialize<SisApiWrapperResponse<IEnumerable<SisStudentApiResponse>>>(content, _jsonOptions);

                if (wrappedResponse == null || !wrappedResponse.Status)
                {
                    var errorMessage = wrappedResponse?.Message ?? "Unknown error from SIS API";
                    _logger.LogError("SIS API returned error: {ErrorMessage}", errorMessage);
                    throw new SisApiException($"SIS API error: {errorMessage}");
                }

                var students = wrappedResponse.Data ?? Enumerable.Empty<SisStudentApiResponse>();

                stopwatch.Stop();
                _logger.LogInformation("Successfully fetched {Count} students from SIS API in {ElapsedMs}ms",
                    students.Count(), stopwatch.ElapsedMilliseconds);

                return students;
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "HTTP error while fetching students from SIS API");
                throw new SisApiException("Failed to fetch students from SIS API due to network error", ex);
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogError(ex, "Request timeout while fetching students from SIS API");
                throw new SisApiException("Request to SIS API timed out", ex);
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "JSON deserialization error while processing SIS API response");
                throw new SisApiException("Failed to parse SIS API response", ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error while fetching students from SIS API");
                throw new SisApiException("Unexpected error occurred while communicating with SIS API", ex);
            }
        }

        /// <summary>
        /// Fetches students with filtering criteria from the SIS API
        /// </summary>
        public async Task<IEnumerable<SisStudentApiResponse>> GetFilteredStudentsAsync(
            SisStudentFilterCriteria filterCriteria, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Fetching filtered students from SIS API with criteria: {@FilterCriteria}", filterCriteria);
                var stopwatch = Stopwatch.StartNew();

                var json = JsonSerializer.Serialize(filterCriteria, _jsonOptions);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _retryPolicy.ExecuteAsync(async () =>
                {
                    var endpoint = _options.StudentsFilterEndpoint.TrimStart('/');
                    var httpResponse = await _httpClient.PostAsync(endpoint, content, cancellationToken);
                    httpResponse.EnsureSuccessStatusCode();
                    return httpResponse;
                });

                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogDebug("SIS API filtered response content: {Content}", responseContent);

                var wrappedResponse = JsonSerializer.Deserialize<SisApiWrapperResponse<IEnumerable<SisStudentApiResponse>>>(responseContent, _jsonOptions);

                if (wrappedResponse == null || !wrappedResponse.Status)
                {
                    var errorMessage = wrappedResponse?.Message ?? "Unknown error from SIS API";
                    _logger.LogError("SIS API returned error for filtered request: {ErrorMessage}", errorMessage);
                    throw new SisApiException($"SIS API error: {errorMessage}");
                }

                var students = wrappedResponse.Data ?? Enumerable.Empty<SisStudentApiResponse>();

                stopwatch.Stop();
                _logger.LogInformation("Successfully fetched {Count} filtered students from SIS API in {ElapsedMs}ms",
                    students.Count(), stopwatch.ElapsedMilliseconds);

                return students;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while fetching filtered students from SIS API");
                throw new SisApiException("Failed to fetch filtered students from SIS API", ex);
            }
        }

        /// <summary>
        /// Fetches a specific student by their student ID
        /// </summary>
        public async Task<SisStudentApiResponse?> GetStudentByIdAsync(string studentId, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(studentId))
                throw new ArgumentException("Student ID cannot be null or empty", nameof(studentId));

            var filterCriteria = new SisStudentFilterCriteria { StudentId = studentId };
            var students = await GetFilteredStudentsAsync(filterCriteria, cancellationToken);
            return students.FirstOrDefault();
        }

        /// <summary>
        /// Fetches students by email address
        /// </summary>
        public async Task<SisStudentApiResponse?> GetStudentByEmailAsync(string email, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(email))
                throw new ArgumentException("Email cannot be null or empty", nameof(email));

            var filterCriteria = new SisStudentFilterCriteria { Email = email };
            var students = await GetFilteredStudentsAsync(filterCriteria, cancellationToken);
            return students.FirstOrDefault();
        }

        #endregion

        #region Enrolled Users

        /// <summary>
        /// Fetches all enrolled users from the SIS API
        /// </summary>
        public async Task<IEnumerable<SisEnrolledUserApiResponse>> GetAllEnrolledUsersAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Fetching all enrolled users from SIS API");
                var stopwatch = Stopwatch.StartNew();

                var response = await _retryPolicy.ExecuteAsync(async () =>
                {
                    var endpoint = _options.EnrolledUsersEndpoint.TrimStart('/');
                    var httpResponse = await _httpClient.GetAsync(endpoint, cancellationToken);
                    httpResponse.EnsureSuccessStatusCode();
                    return httpResponse;
                });

                var content = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogDebug("SIS API enrolled users response content: {Content}", content);

                var wrappedResponse = JsonSerializer.Deserialize<SisApiWrapperResponse<IEnumerable<SisEnrolledUserApiResponse>>>(content, _jsonOptions);

                if (wrappedResponse == null || !wrappedResponse.Status)
                {
                    var errorMessage = wrappedResponse?.Message ?? "Unknown error from SIS API";
                    _logger.LogError("SIS API returned error for enrolled users: {ErrorMessage}", errorMessage);
                    throw new SisApiException($"SIS API error: {errorMessage}");
                }

                var users = wrappedResponse.Data ?? Enumerable.Empty<SisEnrolledUserApiResponse>();

                stopwatch.Stop();
                _logger.LogInformation("Successfully fetched {Count} enrolled users from SIS API in {ElapsedMs}ms",
                    users.Count(), stopwatch.ElapsedMilliseconds);

                return users;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while fetching enrolled users from SIS API");
                throw new SisApiException("Failed to fetch enrolled users from SIS API", ex);
            }
        }

        /// <summary>
        /// Fetches enrolled users with filtering criteria from the SIS API
        /// </summary>
        public async Task<IEnumerable<SisEnrolledUserApiResponse>> GetFilteredEnrolledUsersAsync(
            SisEnrolledUserFilterCriteria filterCriteria, 
            CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogInformation("Fetching filtered enrolled users from SIS API");

                var json = JsonSerializer.Serialize(filterCriteria, _jsonOptions);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var response = await _retryPolicy.ExecuteAsync(async () =>
                {
                    var endpoint = _options.EnrolledUsersFilterEndpoint.TrimStart('/');
                    var httpResponse = await _httpClient.PostAsync(endpoint, content, cancellationToken);
                    httpResponse.EnsureSuccessStatusCode();
                    return httpResponse;
                });

                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                _logger.LogDebug("SIS API filtered enrolled users response content: {Content}", responseContent);

                var wrappedResponse = JsonSerializer.Deserialize<SisApiWrapperResponse<IEnumerable<SisEnrolledUserApiResponse>>>(responseContent, _jsonOptions);

                if (wrappedResponse == null || !wrappedResponse.Status)
                {
                    var errorMessage = wrappedResponse?.Message ?? "Unknown error from SIS API";
                    _logger.LogError("SIS API returned error for filtered enrolled users: {ErrorMessage}", errorMessage);
                    throw new SisApiException($"SIS API error: {errorMessage}");
                }

                var users = wrappedResponse.Data ?? Enumerable.Empty<SisEnrolledUserApiResponse>();

                _logger.LogInformation("Successfully fetched {Count} filtered enrolled users from SIS API", users.Count());
                return users;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error while fetching filtered enrolled users from SIS API");
                throw new SisApiException("Failed to fetch filtered enrolled users from SIS API", ex);
            }
        }

        #endregion

        #region Health and Connectivity

        /// <summary>
        /// Tests connectivity to the SIS API
        /// </summary>
        public async Task<bool> TestConnectivityAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                _logger.LogDebug("Testing SIS API connectivity");

                var endpoint = _options.StudentsEndpoint.TrimStart('/') + "?$top=1";
                var response = await _httpClient.GetAsync(endpoint, cancellationToken);
                var isHealthy = response.IsSuccessStatusCode;

                _logger.LogInformation("SIS API connectivity test result: {IsHealthy}", isHealthy);
                return isHealthy;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "SIS API connectivity test failed");
                return false;
            }
        }

        /// <summary>
        /// Gets the current health status of the SIS API connection
        /// </summary>
        public async Task<SisApiHealthStatus> GetHealthStatusAsync()
        {
            var stopwatch = Stopwatch.StartNew();
            var healthStatus = new SisApiHealthStatus
            {
                LastChecked = DateTime.UtcNow
            };

            try
            {
                var isConnected = await TestConnectivityAsync();
                stopwatch.Stop();

                healthStatus.IsHealthy = isConnected;
                healthStatus.Status = isConnected ? "Healthy" : "Unhealthy";
                healthStatus.ResponseTime = stopwatch.Elapsed;
                healthStatus.AdditionalInfo["BaseUrl"] = _options.BaseUrl;
                healthStatus.AdditionalInfo["Timeout"] = _options.TimeoutSeconds;
                healthStatus.AdditionalInfo["RetryAttempts"] = _options.RetryAttempts;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                healthStatus.IsHealthy = false;
                healthStatus.Status = "Error";
                healthStatus.ResponseTime = stopwatch.Elapsed;
                healthStatus.ErrorMessage = ex.Message;
            }

            return healthStatus;
        }

        #endregion

        #region IDisposable

        /// <summary>
        /// Disposes the HTTP client and other resources
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed && disposing)
            {
                _httpClient?.Dispose();
                _disposed = true;
            }
        }

        #endregion
    }

    #region Custom Exceptions

    /// <summary>
    /// Exception thrown when SIS API operations fail
    /// </summary>
    public class SisApiException : Exception
    {
        public SisApiException(string message) : base(message) { }
        public SisApiException(string message, Exception innerException) : base(message, innerException) { }
    }

    #endregion
}
