using Forms.ktech.Models;

namespace Forms.ktech.Services
{
    /// <summary>
    /// Interface for email notification service operations
    /// </summary>
    public interface IEmailNotificationService
    {
        /// <summary>
        /// Sends document disapproval notification to student
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="disapprovedDocuments">Array of disapproved document types</param>
        /// <returns>The email notification record</returns>
        Task<EmailNotification> SendDocumentDisapprovalNotificationAsync(int submissionId, string[] disapprovedDocuments);

        /// <summary>
        /// Sends email notification
        /// </summary>
        /// <param name="emailNotification">The email notification to send</param>
        /// <returns>True if sent successfully</returns>
        Task<bool> SendEmailAsync(EmailNotification emailNotification);



        /// <summary>
        /// Retries failed email notifications
        /// </summary>
        /// <param name="maxRetries">Maximum number of retry attempts</param>
        /// <returns>Number of emails processed</returns>
        Task<int> RetryFailedEmailsAsync(int maxRetries = 3);

        /// <summary>
        /// Gets email notification history for a submission
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <returns>List of email notifications</returns>
        Task<List<EmailNotification>> GetEmailHistoryAsync(int submissionId);
    }
}
