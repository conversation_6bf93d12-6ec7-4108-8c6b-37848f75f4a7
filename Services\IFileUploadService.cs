namespace Forms.ktech.Services
{
    /// <summary>
    /// Service interface for handling file uploads across all forms
    /// </summary>
    public interface IFileUploadService
    {
        /// <summary>
        /// Uploads a single file to the specified form's directory
        /// </summary>
        /// <param name="file">The file to upload</param>
        /// <param name="formName">The name of the form (used for directory structure)</param>
        /// <param name="submissionGuid">Unique identifier for the submission</param>
        /// <param name="fileName">Custom file name (optional, will use original if not provided)</param>
        /// <returns>The relative path to the uploaded file</returns>
        Task<string> UploadFileAsync(IFormFile file, string formName, Guid submissionGuid, string? fileName = null);

        /// <summary>
        /// Uploads multiple files to the specified form's directory
        /// </summary>
        /// <param name="files">Dictionary of files with their custom names</param>
        /// <param name="formName">The name of the form (used for directory structure)</param>
        /// <param name="submissionGuid">Unique identifier for the submission</param>
        /// <returns>Dictionary of file names and their relative paths</returns>
        Task<Dictionary<string, string>> UploadFilesAsync(Dictionary<string, IFormFile> files, string formName, Guid submissionGuid);

        /// <summary>
        /// Validates a file before upload
        /// </summary>
        /// <param name="file">The file to validate</param>
        /// <param name="allowedExtensions">Allowed file extensions</param>
        /// <param name="maxSizeInMB">Maximum file size in MB</param>
        /// <returns>Validation result with error message if invalid</returns>
        (bool IsValid, string? ErrorMessage) ValidateFile(IFormFile file, string[] allowedExtensions, int maxSizeInMB = 5);

        /// <summary>
        /// Deletes all files in a submission directory
        /// </summary>
        /// <param name="formName">The name of the form</param>
        /// <param name="submissionGuid">Unique identifier for the submission</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> DeleteSubmissionFilesAsync(string formName, Guid submissionGuid);

        /// <summary>
        /// Gets the physical path for a file
        /// </summary>
        /// <param name="relativePath">The relative path of the file</param>
        /// <returns>The full physical path</returns>
        string GetPhysicalPath(string relativePath);

        /// <summary>
        /// Checks if a file exists
        /// </summary>
        /// <param name="relativePath">The relative path of the file</param>
        /// <returns>True if the file exists, false otherwise</returns>
        bool FileExists(string relativePath);

        /// <summary>
        /// Gets file information
        /// </summary>
        /// <param name="relativePath">The relative path of the file</param>
        /// <returns>File information or null if file doesn't exist</returns>
        FileInfo? GetFileInfo(string relativePath);

        /// <summary>
        /// Creates a secure download stream for a file
        /// </summary>
        /// <param name="relativePath">The relative path of the file</param>
        /// <returns>File stream for download</returns>
        Task<Stream?> GetFileStreamAsync(string relativePath);

        /// <summary>
        /// Gets the MIME type for a file
        /// </summary>
        /// <param name="fileName">The file name</param>
        /// <returns>MIME type string</returns>
        string GetMimeType(string fileName);
    }
}
