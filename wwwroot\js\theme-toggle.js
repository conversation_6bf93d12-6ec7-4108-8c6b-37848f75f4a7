/**
 * Theme Toggle Component - forms.ktech
 * Provides light/dark mode switching functionality with proper accessibility
 */

const ThemeToggle = {
  /**
   * Initialize the theme toggle functionality
   */
  init: function () {
    this.loadSavedTheme();
    this.setupEventListeners();
    this.updateToggleState();
  },

  /**
   * Initialize theme toggle for existing button in header
   */
  initHeaderToggle: function () {
    this.loadSavedTheme();
    this.setupEventListeners();
    this.updateToggleState();
  },

  /**
   * Load the saved theme from localStorage
   */
  loadSavedTheme: function () {
    const savedTheme = localStorage.getItem("theme");
    const systemPrefersDark = window.matchMedia(
      "(prefers-color-scheme: dark)"
    ).matches;

    if (savedTheme === "dark" || (!savedTheme && systemPrefersDark)) {
      this.setDarkMode();
    } else {
      this.setLightMode();
    }
  },

  /**
   * Set up event listeners
   */
  setupEventListeners: function () {
    // Find theme toggle button (could be in header or elsewhere)
    const toggleButton = document.getElementById("theme-toggle");

    if (toggleButton) {
      // Remove any existing listeners to prevent duplicates
      toggleButton.removeEventListener("click", this.handleToggleClick);
      toggleButton.addEventListener("click", this.handleToggleClick.bind(this));
    } else {
      console.error(
        '🎨 ERROR: Theme toggle button with ID "theme-toggle" not found!'
      );
    }

    // Listen for system theme changes
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    mediaQuery.removeEventListener("change", this.handleSystemThemeChange);
    mediaQuery.addEventListener(
      "change",
      this.handleSystemThemeChange.bind(this)
    );

    // Keyboard shortcut: Ctrl/Cmd + Shift + T
    document.removeEventListener("keydown", this.handleKeyboardShortcut);
    document.addEventListener(
      "keydown",
      this.handleKeyboardShortcut.bind(this)
    );
  },

  /**
   * Handle toggle button click
   */
  handleToggleClick: function (e) {
    e.preventDefault();
    this.toggleTheme();
  },

  /**
   * Handle system theme change
   */
  handleSystemThemeChange: function (e) {
    if (!localStorage.getItem("theme")) {
      if (e.matches) {
        this.setDarkMode();
      } else {
        this.setLightMode();
      }
    }
  },

  /**
   * Handle keyboard shortcut
   */
  handleKeyboardShortcut: function (e) {
    if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === "T") {
      e.preventDefault();
      this.toggleTheme();
    }
  },

  /**
   * Toggle between light and dark themes
   */
  toggleTheme: function () {
    const currentTheme = document.documentElement.classList.contains("dark")
      ? "dark"
      : "light";

    if (document.documentElement.classList.contains("dark")) {
      this.setLightMode();
    } else {
      this.setDarkMode();
    }
  },

  /**
   * Set dark mode
   */
  setDarkMode: function () {
    document.documentElement.classList.add("dark");

    localStorage.setItem("theme", "dark");

    this.updateToggleState();
    this.announceThemeChange("Dark mode enabled / تم تفعيل الوضع الداكن");
  },

  /**
   * Set light mode
   */
  setLightMode: function () {
    document.documentElement.classList.remove("dark");

    localStorage.setItem("theme", "light");

    this.updateToggleState();
    this.announceThemeChange("Light mode enabled / تم تفعيل الوضع الفاتح");
  },

  /**
   * Update the toggle button state
   */
  updateToggleState: function () {
    const darkIcon = document.getElementById("theme-toggle-dark-icon");
    const lightIcon = document.getElementById("theme-toggle-light-icon");
    const toggleButton = document.getElementById("theme-toggle");

    if (!darkIcon || !lightIcon || !toggleButton) {
      console.error(
        "🎨 ERROR: Missing required DOM elements for theme toggle!"
      );
      if (!darkIcon)
        console.error(
          '🎨   - Missing element with ID "theme-toggle-dark-icon"'
        );
      if (!lightIcon)
        console.error(
          '🎨   - Missing element with ID "theme-toggle-light-icon"'
        );
      if (!toggleButton)
        console.error('🎨   - Missing element with ID "theme-toggle"');
      return;
    }

    const isDark = document.documentElement.classList.contains("dark");

    if (isDark) {
      darkIcon.classList.add("hidden");
      lightIcon.classList.remove("hidden");
      toggleButton.setAttribute(
        "aria-label",
        "Switch to light mode / التبديل للوضع الفاتح"
      );
      toggleButton.setAttribute(
        "title",
        "Switch to light mode / التبديل للوضع الفاتح"
      );
    } else {
      darkIcon.classList.remove("hidden");
      lightIcon.classList.add("hidden");
      toggleButton.setAttribute(
        "aria-label",
        "Switch to dark mode / التبديل للوضع الداكن"
      );
      toggleButton.setAttribute(
        "title",
        "Switch to dark mode / التبديل للوضع الداكن"
      );
    }

    // Update brand logos (they use Tailwind's dark: classes, so no manual JS needed)
    // But we can log their state for debugging
    const lightLogo = document.getElementById("brand-logo-light");
    const darkLogo = document.getElementById("brand-logo-dark");
  },

  /**
   * Announce theme change for screen readers
   */
  announceThemeChange: function (message) {
    const announcement = document.createElement("div");
    announcement.setAttribute("aria-live", "polite");
    announcement.setAttribute("aria-atomic", "true");
    announcement.className = "sr-only";
    announcement.textContent = message;

    document.body.appendChild(announcement);

    // Remove the announcement after a short delay
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  },

  /**
   * Get current theme
   */
  getCurrentTheme: function () {
    return document.documentElement.classList.contains("dark")
      ? "dark"
      : "light";
  },

  /**
   * Check if dark mode is enabled
   */
  isDarkMode: function () {
    return document.documentElement.classList.contains("dark");
  },
};

// Auto-initialize when DOM is ready (only load theme, don't create buttons)
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", () => {
    ThemeToggle.loadSavedTheme();
    // Setup listeners will be called when header toggle is initialized
  });
} else {
  ThemeToggle.loadSavedTheme();
}

// Export for use in other scripts
window.ThemeToggle = ThemeToggle;
