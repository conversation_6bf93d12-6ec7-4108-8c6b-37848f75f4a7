<!-- Flowbite Header/Navbar -->
<nav class="fixed top-0 z-50 w-full bg-white border-b border-gray-200 dark:bg-gray-800 dark:border-gray-700">
    <div class="px-3 py-3 lg:px-5 lg:pl-3">
        <div class="flex items-center justify-between">
            <div class="flex items-center justify-start rtl:justify-end">
                <!-- Sidebar Toggle Button -->
                <button data-drawer-target="logo-sidebar" data-drawer-toggle="logo-sidebar" aria-controls="logo-sidebar" type="button" class="inline-flex items-center p-2 text-sm text-gray-500 rounded-lg sm:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600">
                    <span class="sr-only">Open sidebar</span>
                    <svg class="w-6 h-6" aria-hidden="true" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path clip-rule="evenodd" fill-rule="evenodd" d="M2 4.75A.75.75 0 012.75 4h14.5a.75.75 0 010 1.5H2.75A.75.75 0 012 4.75zm0 10.5a.75.75 0 01.75-.75h7.5a.75.75 0 010 1.5h-7.5a.75.75 0 01-.75-.75zM2 10a.75.75 0 01.75-.75h14.5a.75.75 0 010 1.5H2.75A.75.75 0 012 10z"></path>
                    </svg>
                </button>
                
                <!-- Brand Logo -->
                <a asp-area="" asp-controller="Home" asp-action="Index" class="flex ms-2 md:me-24 items-center">
                    <!-- Light Mode Logo -->
                    <img id="brand-logo-light"
                         src="~/images/logos/Light/PNG/2351_KTECH_Logos_RGB_Primary_Wordmark.png"
                         alt="KTECH Logo"
                         class="h-8 sm:h-12 w-auto transition-opacity duration-200 dark:hidden"
                         loading="lazy" />

                    <!-- Dark Mode Logo -->
                    <img id="brand-logo-dark"
                         src="~/images/logos/Dark/PNG/2351_KTECH_Logos_RGB_Primary_Wordmark_Dark_withBG_Wordmark_Dark_withBG.png"
                         alt="KTECH Logo"
                         class="h-8 md:h-12 w-auto transition-opacity duration-200 hidden dark:block"
                         loading="lazy" />
                </a>
            </div>
            
            <!-- Right Side Navigation -->
            <div class="flex items-center">
                <div class="flex items-center ms-3">
                    <!-- Forms Dropdown -->
                    <button type="button" class="flex text-sm bg-gray-800 rounded-full focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600 me-4" id="forms-menu-button" aria-expanded="false" data-dropdown-toggle="forms-dropdown" data-dropdown-placement="bottom">
                        <span class="sr-only">Open forms menu</span>
                        <div class="flex items-center px-3 py-2 text-sm text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                            <i class="fas fa-file-alt me-2"></i>
                            Forms
                            <svg class="w-2.5 h-2.5 ms-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
                            </svg>
                        </div>
                    </button>

                    <!-- Theme Toggle Button -->
                    <button id="theme-toggle"
                            type="button"
                            class="p-2 text-gray-500 rounded-lg hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-700 me-2 sm:me-4 transition-all duration-200"
                            aria-label="Toggle theme / تبديل المظهر"
                            title="Toggle light/dark mode / تبديل الوضع الفاتح/الداكن">
                        <svg id="theme-toggle-dark-icon" class="w-5 h-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
                        </svg>
                        <svg id="theme-toggle-light-icon" class="w-5 h-5 hidden" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" fill-rule="evenodd" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                    
                    <!-- Forms Dropdown Menu -->
                    <div class="z-50 hidden my-4 text-base list-none bg-white divide-y divide-gray-100 rounded shadow dark:bg-gray-700 dark:divide-gray-600" id="forms-dropdown">
                        <ul class="py-1" role="none">
                            <li>
                                <a asp-controller="KuwaitiStudentInfo" asp-action="CollectInfo" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white" role="menuitem">
                                    <i class="fas fa-graduation-cap me-2 text-blue-600"></i>
                                    Kuwaiti Student Information / معلومات الطالب الكويتي
                                </a>
                            </li>
                            <li>
                                <hr class="my-1 border-gray-200 dark:border-gray-600">
                            </li>
                            <li>
                                <span class="block px-4 py-2 text-sm text-gray-400 dark:text-gray-500" role="menuitem">
                                    <i class="fas fa-plus me-2"></i>
                                    More forms coming soon... / المزيد من النماذج قريباً...
                                </span>
                            </li>
                        </ul>
                    </div>

                    <!-- User Account Dropdown -->
                    <button type="button" class="flex text-sm bg-gray-800 rounded-full focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600" id="user-menu-button" aria-expanded="false" data-dropdown-toggle="user-dropdown" data-dropdown-placement="bottom">
                        <span class="sr-only">Open user menu / فتح قائمة المستخدم</span>
                        <div class="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                    </button>
                    
                    <!-- User Dropdown Menu -->
                    <div class="z-50 hidden my-4 text-base list-none bg-white divide-y divide-gray-100 rounded shadow dark:bg-gray-700 dark:divide-gray-600" id="user-dropdown">
                        <div class="px-4 py-3" role="none">
                            <p class="text-sm text-gray-900 dark:text-white" role="none">
                                @User.Identity?.Name
                            </p>
                            <p class="text-sm font-medium text-gray-900 truncate dark:text-gray-300" role="none">
                                @User.FindFirst("preferred_username")?.Value
                            </p>
                        </div>
                        <ul class="py-1" role="none">
                            <li>
                                <a asp-area="" asp-controller="Home" asp-action="Index" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white" role="menuitem">
                                    <i class="fas fa-home me-2"></i>
                                    Dashboard / لوحة التحكم
                                </a>
                            </li>
                            <li>
                                <a asp-area="" asp-controller="Home" asp-action="Privacy" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white" role="menuitem">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    Privacy / الخصوصية
                                </a>
                            </li>
                            <li>
                                <hr class="my-1 border-gray-200 dark:border-gray-600">
                            </li>
                            <li>
                                <a asp-area="MicrosoftIdentity" asp-controller="Account" asp-action="SignOut" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-600 dark:hover:text-white" role="menuitem">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    Sign out / تسجيل الخروج
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</nav>
