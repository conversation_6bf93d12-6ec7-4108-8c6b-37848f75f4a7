<!DOCTYPE html>
<html lang="en" class="scroll-smooth">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - Forms.ktech</title>
    <!-- Tailwind CSS and Flowbite -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        // Configure Tailwind CSS for class-based dark mode
        tailwind.config = {
            darkMode: 'class'
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/forms.css" asp-append-version="true" />
    @await RenderSectionAsync("Styles", required: false)
</head>

<body class="bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
    <!-- Header -->
    @await Html.PartialAsync("_Header")

    <!-- Sidebar -->
    @await Html.PartialAsync("_Sidebar")

    <!-- Main Content -->
    <div class="p-4 bg-gray-50 dark:bg-gray-900 lg:ml-64 transition-colors duration-200">
        <div class="p-4 max-w-8xl mx-auto mt-14">
            <!-- Breadcrumb Navigation -->
            @if (IsSectionDefined("Breadcrumb"))
            {
                <nav class="flex mb-4" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        @await RenderSectionAsync("Breadcrumb", required: false)
                    </ol>
                </nav>
            }

            <main role="main">
                @RenderBody()
            </main>

            @* @await Html.PartialAsync("_Footer") *@
        </div>
    </div>
    <!-- Footer -->

    <script src="~/lib/jquery/dist/jquery.js" asp-append-version="true"></script>
    <!-- Flowbite JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/js/forms-navigation.js" asp-append-version="true"></script>
    <script src="~/js/theme-toggle.js" asp-append-version="true"></script>
    <script>
        // Initialize theme toggle when DOM is ready
        $(document).ready(function() {
            if (typeof ThemeToggle !== 'undefined') {
                ThemeToggle.initHeaderToggle();
            } else {
                console.error('🎨 Layout: ERROR - ThemeToggle is not available!');
            }

            // Additional debugging - check if button exists
            const themeButton = document.getElementById('theme-toggle');
            
            // Test manual click handler
            if (themeButton) {
                themeButton.addEventListener('click', function(e) {
                    console.log('🎨 Layout: Manual click handler triggered!', e);
                });
            }
        });
    </script>
    @await RenderSectionAsync("Scripts", required: false)
</body>

</html>
