/**
 * Kuwaiti Student Information Form - AJAX Multi-Step Wizard
 * Handles step navigation, validation, and AJAX form submission
 */

// Wizard state management
const KuwaitiStudentWizard = {
  currentStep: 1,
  totalSteps: 3,
  formData: new FormData(),
  eligibilityCase: null,
  isEligible: true,

  // Initialize the wizard
  init: function () {
    this.bindEvents();
    this.updateUI();
    this.checkEligibility();
    this.initializeSisIntegration();
  },

  // Bind event handlers
  bindEvents: function () {
    // Navigation buttons
    $("#btn-next").on("click", () => this.nextStep());
    $("#btn-back").on("click", () => this.previousStep());
    $("#btn-cancel").on("click", () => this.cancel());
    $("#btn-submit").on("click", (e) => this.submitForm(e));

    // Nationality checkboxes for real-time eligibility checking
    $(".nationality-checkbox").on("change", () => {
      this.checkEligibility();
      this.updateConditionalFields();
    });

    // Deceased parent checkboxes for conditional document display
    $(".deceased-checkbox").on("change", () => {
      this.updateConditionalFields();
    });

    // Form field changes for validation
    $("#wizard-form input, #wizard-form select").on("blur", (e) => {
      this.validateField(e.target);
    });

    // File input changes
    $('#wizard-form input[type="file"]').on("change", (e) => {
      this.handleFileChange(e.target);
    });

    // Prevent default form submission
    $("#wizard-form").on("submit", (e) => {
      e.preventDefault();
      this.submitForm(e);
    });
  },

  // Navigate to next step
  nextStep: function () {
    if (this.currentStep >= this.totalSteps) return;

    // Validate current step before proceeding
    this.validateCurrentStep().then((isValid) => {
      if (isValid) {
        this.currentStep++;
        this.updateUI();
        this.scrollToTop();

        // Special handling for step 2 (documents)
        if (this.currentStep === 2) {
          this.updateConditionalFields();
        }
        // Special handling for step 3 (review)
        else if (this.currentStep === 3) {
          this.populateReviewStep();
        }
      }
    });
  },

  // Navigate to previous step
  previousStep: function () {
    if (this.currentStep <= 1) return;

    this.currentStep--;
    this.updateUI();
    this.scrollToTop();
  },

  // Cancel form and return to home
  cancel: function () {
    if (
      confirm("Are you sure you want to cancel? All entered data will be lost.")
    ) {
      window.location.href = "/";
    }
  },

  // Update UI based on current step
  updateUI: function () {
    // Hide all steps
    $(".wizard-step").addClass("hidden");

    // Show current step
    $(`#step-${this.currentStep}`).removeClass("hidden");

    // Update progress bar
    const progressPercent = (this.currentStep / this.totalSteps) * 100;
    $("#progress-bar").css("width", `${progressPercent}%`);
    $("#step-indicator").text(`Step ${this.currentStep} of ${this.totalSteps}`);

    // Update navigation buttons
    this.updateNavigationButtons();

    // Clear validation summary
    this.clearValidationSummary();
  },

  // Update navigation button states
  updateNavigationButtons: function () {
    // Back button
    if (this.currentStep === 1) {
      $("#btn-back").addClass("hidden");
    } else {
      $("#btn-back").removeClass("hidden");
    }

    // Next/Submit buttons
    if (this.currentStep === this.totalSteps) {
      $("#btn-next").addClass("hidden");
      $("#btn-submit").removeClass("hidden");
    } else {
      $("#btn-next").removeClass("hidden");
      $("#btn-submit").addClass("hidden");

      // Update next button text
      if (this.currentStep === 2 && !this.isEligible) {
        $("#btn-next-text").text("Skip to Review");
      } else {
        $("#btn-next-text").text("Next");
      }
    }
  },

  // Check eligibility based on nationality selections
  checkEligibility: function () {
    const studentKuwaiti = $("#studentKuwaiti").is(":checked");
    const fatherKuwaiti = $("#fatherKuwaiti").is(":checked");
    const motherKuwaiti = $("#motherKuwaiti").is(":checked");

    // Determine eligibility case based on exact business rules matching server-side logic
    if (studentKuwaiti && fatherKuwaiti && motherKuwaiti) {
      this.eligibilityCase = "A"; // All three Kuwaiti
      this.isEligible = true;
    } else if (studentKuwaiti && fatherKuwaiti && !motherKuwaiti) {
      this.eligibilityCase = "B"; // Father & Student Kuwaiti (Mother is not)
      this.isEligible = true;
    } else if (!studentKuwaiti && !fatherKuwaiti && motherKuwaiti) {
      this.eligibilityCase = "C"; // Mother Kuwaiti only (Father and Student are not)
      this.isEligible = true;
    } else {
      this.eligibilityCase = "D"; // None Kuwaiti or any other combination - Ineligible
      this.isEligible = false;
    }

    // Show/hide eligibility alert
    if (!this.isEligible) {
      $("#eligibility-alert").removeClass("hidden");
    } else {
      $("#eligibility-alert").addClass("hidden");
    }
  },

  // Update conditional document fields based on eligibility case and deceased parent status
  updateConditionalFields: function () {
    // Hide all conditional sections first
    $("[data-condition]").addClass("hidden");

    // Get current nationality and deceased status
    const studentKuwaiti = $("#studentKuwaiti").is(":checked");
    const fatherKuwaiti = $("#fatherKuwaiti").is(":checked");
    const motherKuwaiti = $("#motherKuwaiti").is(":checked");
    const fatherDeceased = $("#fatherDeceased").is(":checked");
    const motherDeceased = $("#motherDeceased").is(":checked");

    // Debug logging for deceased parent logic
    console.log("Deceased Parent Debug:", {
      eligibilityCase: this.eligibilityCase,
      fatherKuwaiti,
      motherKuwaiti,
      fatherDeceased,
      motherDeceased,
    });

    // Show deceased checkboxes only when corresponding parent is Kuwaiti
    if (fatherKuwaiti) {
      $('[data-condition="father-kuwaiti"]').removeClass("hidden");
    }
    if (motherKuwaiti) {
      $('[data-condition="mother-kuwaiti"]').removeClass("hidden");
    }

    // Show document sections based on eligibility case and deceased status
    // Note: Student Civil ID and Birth Certificate are always shown (no data-condition attribute)
    switch (this.eligibilityCase) {
      case "A": // All Kuwaiti
        // Student: Civil ID (always shown) + Nationality Certificate + Birth Certificate (always shown)
        $('[data-condition="student-not-kuwaiti"]').removeClass("hidden");

        // Father documents: EXCLUSIVE logic - either standard docs OR death certificate, never both
        if (fatherKuwaiti) {
          if (fatherDeceased) {
            // Father deceased: ONLY death certificate (no standard documents)
            $('[data-condition="father-deceased"]').removeClass("hidden");
            // Explicitly hide father standard document uploads (not checkboxes)
            $('.mb-4[data-condition="father-kuwaiti"]').addClass("hidden");
            $('.mb-4[data-condition="father-not-kuwaiti"]').addClass("hidden");
          } else {
            // Father alive: ONLY standard documents (Civil ID + Nationality Certificate)
            $('.mb-4[data-condition="father-kuwaiti"]').removeClass("hidden");
            $('.mb-4[data-condition="father-not-kuwaiti"]').removeClass(
              "hidden"
            );
            // Explicitly hide death certificate
            $('[data-condition="father-deceased"]').addClass("hidden");
          }
        }

        // Mother documents: EXCLUSIVE logic - either standard docs OR death certificate, never both
        if (motherKuwaiti) {
          if (motherDeceased) {
            // Mother deceased: ONLY death certificate (no standard documents)
            $('[data-condition="mother-deceased"]').removeClass("hidden");
            // Explicitly hide mother standard document uploads (not checkboxes)
            $('.mb-4[data-condition="mother-kuwaiti"]').addClass("hidden");
            $('.mb-4[data-condition="mother-not-kuwaiti"]').addClass("hidden");
          } else {
            // Mother alive: ONLY standard documents (Civil ID + Nationality Certificate)
            $('.mb-4[data-condition="mother-kuwaiti"]').removeClass("hidden");
            $('.mb-4[data-condition="mother-not-kuwaiti"]').removeClass(
              "hidden"
            );
            // Explicitly hide death certificate
            $('[data-condition="mother-deceased"]').addClass("hidden");
          }
        }
        break;

      case "B": // Father & Student Kuwaiti
        // Student: Civil ID (always shown) + Nationality Certificate + Birth Certificate (always shown)
        $('[data-condition="student-not-kuwaiti"]').removeClass("hidden");

        // Father documents: EXCLUSIVE logic - either standard docs OR death certificate, never both
        if (fatherKuwaiti) {
          if (fatherDeceased) {
            // Father deceased: ONLY death certificate (no standard documents)
            $('[data-condition="father-deceased"]').removeClass("hidden");
            // Explicitly hide father standard document uploads (not checkboxes)
            $('.mb-4[data-condition="father-kuwaiti"]').addClass("hidden");
            $('.mb-4[data-condition="father-not-kuwaiti"]').addClass("hidden");
          } else {
            // Father alive: ONLY standard documents (Civil ID + Nationality Certificate)
            $('.mb-4[data-condition="father-kuwaiti"]').removeClass("hidden");
            $('.mb-4[data-condition="father-not-kuwaiti"]').removeClass(
              "hidden"
            );
            // Explicitly hide death certificate
            $('[data-condition="father-deceased"]').addClass("hidden");
          }
        }
        // Mother: No documents required (not Kuwaiti in this case)
        break;

      case "C": // Mother Kuwaiti only
        // Student: Civil ID + Birth Certificate only (always shown, no nationality certificate needed)
        // Father: No documents required (not Kuwaiti in this case)

        // Mother documents: EXCLUSIVE logic - either standard docs OR death certificate, never both
        if (motherKuwaiti) {
          if (motherDeceased) {
            // Mother deceased: ONLY death certificate (no standard documents)
            $('[data-condition="mother-deceased"]').removeClass("hidden");
            // Explicitly hide mother standard document uploads (not checkboxes)
            $('.mb-4[data-condition="mother-kuwaiti"]').addClass("hidden");
            $('.mb-4[data-condition="mother-not-kuwaiti"]').addClass("hidden");
          } else {
            // Mother alive: ONLY standard documents (Civil ID + Nationality Certificate)
            $('.mb-4[data-condition="mother-kuwaiti"]').removeClass("hidden");
            $('.mb-4[data-condition="mother-not-kuwaiti"]').removeClass(
              "hidden"
            );
            // Explicitly hide death certificate
            $('[data-condition="mother-deceased"]').addClass("hidden");
          }
        }
        break;

      case "D": // None Kuwaiti - Hide all conditional documents
        // All conditional documents already hidden above
        break;
    }

    // If ineligible (Case D), skip document step
    if (!this.isEligible && this.currentStep === 2) {
      this.nextStep(); // Skip to review step
    }
  },

  // Collect form data with proper checkbox handling
  collectFormData: function () {
    const formData = new FormData();
    const form = $("#wizard-form")[0];

    // Track which checkbox names we've already processed
    const processedCheckboxes = new Set();

    // Get all form elements
    const formElements = form.elements;

    for (let i = 0; i < formElements.length; i++) {
      const element = formElements[i];
      const name = element.name;

      if (!name) continue;

      // Handle different input types
      switch (element.type) {
        case "checkbox":
          // Skip if we've already processed this checkbox name
          if (processedCheckboxes.has(name)) continue;

          // Mark as processed
          processedCheckboxes.add(name);

          // Only add checkbox value if it's checked
          if (element.checked) {
            formData.append(name, element.value || "true");
          } else {
            formData.append(name, "false");
          }
          break;
        case "hidden":
          // Skip hidden fields that are for checkboxes (ASP.NET Core generates these)
          if (
            processedCheckboxes.has(name) ||
            form.querySelector(`input[type="checkbox"][name="${name}"]`)
          ) {
            continue;
          }
          // Skip __RequestVerificationToken as we'll add it manually later
          if (name === "__RequestVerificationToken") {
            continue;
          }
          // Add other hidden fields
          formData.append(name, element.value);
          break;
        case "radio":
          // Only add radio value if it's checked
          if (element.checked) {
            formData.append(name, element.value);
          }
          break;
        case "file":
          // Add all selected files
          for (let j = 0; j < element.files.length; j++) {
            formData.append(name, element.files[j]);
          }
          break;
        case "select-multiple":
          // Add all selected options
          for (let j = 0; j < element.options.length; j++) {
            if (element.options[j].selected) {
              formData.append(name, element.options[j].value);
            }
          }
          break;
        default:
          // Handle text, email, number, etc.
          if (element.value !== undefined && element.value !== null) {
            formData.append(name, element.value);
          }
          break;
      }
    }

    return formData;
  },

  // Validate current step via AJAX
  validateCurrentStep: function () {
    return new Promise((resolve) => {
      // Phase 5: Enhanced client-side validation before AJAX call
      if (this.currentStep === 2) {
        const clientValidationResult = this.validateDocumentsClientSide();
        if (!clientValidationResult.isValid) {
          this.displayValidationErrors(clientValidationResult.errors);
          resolve(false);
          return;
        }
      }

      // Collect form data with proper checkbox handling
      const formData = this.collectFormData();
      formData.append("step", this.currentStep);

      // Add anti-forgery token
      const token = $('input[name="__RequestVerificationToken"]').val();
      if (token) {
        formData.append("__RequestVerificationToken", token);
      } else {
        console.warn("Antiforgery token not found in form");
      }

      // Debug: Log form data
      console.log("Form data being sent:");
      for (let [key, value] of formData.entries()) {
        console.log(key, value);
      }

      $.ajax({
        url: "/KuwaitiStudentInfo/ValidateStep",
        type: "POST",
        data: formData,
        processData: false,
        contentType: false,
        success: (response) => {
          if (response.isValid) {
            this.clearValidationSummary();
            resolve(true);
          } else {
            this.displayValidationErrors(response.errors);
            resolve(false);
          }
        },
        error: (xhr, status, error) => {
          console.error("Validation error:", error);
          SharedForms.Toast.show(
            "An error occurred during validation. Please try again.",
            "error"
          );
          resolve(false);
        },
      });
    });
  },

  // Validate individual field
  validateField: function (field) {
    const $field = $(field);
    const value = $field.val().trim();

    // Clear previous validation state
    $field.removeClass("border-red-500 border-green-500");

    // Basic validation
    if ($field.prop("required") && !value) {
      $field.addClass("border-red-500");
      return false;
    }

    // Civil ID validation
    if ($field.attr("name") === "StudentCivilId" && value) {
      if (value.length !== 12 || !/^\d{12}$/.test(value)) {
        $field.addClass("border-red-500");
        return false;
      }
    }

    // Mobile number validation
    if ($field.attr("name") === "StudentMobileNumber" && value) {
      const mobileNumber = value.replace(/[\s\-]/g, ""); // Remove spaces and dashes
      if (!/^(\+965|965)?[569]\d{7}$/.test(mobileNumber)) {
        $field.addClass("border-red-500");
        return false;
      }
    }

    if (value) {
      $field.addClass("border-green-500");
    }

    return true;
  },

  // Handle file input changes
  handleFileChange: function (input) {
    const $input = $(input);
    const file = input.files[0];

    if (file) {
      // Validate file size (5MB limit)
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      if (file.size > maxSize) {
        SharedForms.Toast.show(
          "File size exceeds 5MB limit. Please choose a smaller file.",
          "error"
        );
        $input.val("");
        return;
      }

      // Validate file type
      const allowedTypes = [
        "application/pdf",
        "image/jpeg",
        "image/jpg",
        "image/png",
      ];
      if (!allowedTypes.includes(file.type)) {
        SharedForms.Toast.show(
          "Invalid file type. Please upload PDF, JPG, or PNG files only.",
          "error"
        );
        $input.val("");
        return;
      }

      $input.removeClass("border-red-500").addClass("border-green-500");
    }
  },

  // Phase 5: Enhanced client-side document validation
  validateDocumentsClientSide: function () {
    const errors = {};
    const eligibilityCase = this.getEligibilityCase();
    const requiredFiles = this.getRequiredFileUploads(eligibilityCase);

    // Check if we can determine the case
    if (!eligibilityCase || eligibilityCase === "D") {
      if (eligibilityCase === "D") {
        errors.general = [
          "You are not eligible for this program as no family members are Kuwaiti citizens.",
        ];
      } else {
        errors.general = [
          "Unable to determine eligibility case. Please check your nationality selections.",
        ];
      }
      return { isValid: false, errors: errors };
    }

    let hasErrors = false;
    const caseDescription = this.getCaseDescription(eligibilityCase);

    // Validate each required file
    requiredFiles.forEach((fileProperty) => {
      const $fileInput = $(`input[name="${fileProperty}"]`);
      const file = $fileInput[0]?.files[0];

      if (!file || file.size === 0) {
        const displayName = this.getFileDisplayName(fileProperty);
        const caseSpecificMessage = this.getCaseSpecificErrorMessage(
          fileProperty,
          eligibilityCase,
          displayName
        );

        if (!errors[fileProperty]) {
          errors[fileProperty] = [];
        }
        errors[fileProperty].push(caseSpecificMessage);
        hasErrors = true;
      }
    });

    // Add summary message if there are missing documents
    if (hasErrors) {
      if (!errors.general) {
        errors.general = [];
      }
      errors.general.push(
        `Your application is for ${caseDescription}. Please upload all required documents for Case ${eligibilityCase} before proceeding.`
      );
    }

    return { isValid: !hasErrors, errors: errors };
  },

  // Populate review step with form data
  populateReviewStep: function () {
    // Personal information
    $("#review-student-name").text($("#StudentName").val() || "-");
    $("#review-student-civil-id").text($("#StudentCivilId").val() || "-");
    $("#review-student-mobile").text($("#StudentMobileNumber").val() || "-");
    // Parent names are no longer collected

    // Nationality status badges
    this.populateNationalityBadges();

    // Eligibility case
    this.populateEligibilityCase();

    // Case-specific requirements
    this.populateCaseRequirements();

    // Documents
    this.populateDocumentsList();
  },

  // Populate nationality status badges
  populateNationalityBadges: function () {
    const $container = $("#review-nationality-status");
    $container.empty();

    if ($("#studentKuwaiti").is(":checked")) {
      $container.append(
        '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">Student: Kuwaiti</span>'
      );
    }
    if ($("#fatherKuwaiti").is(":checked")) {
      $container.append(
        '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">Father: Kuwaiti</span>'
      );
    }
    if ($("#motherKuwaiti").is(":checked")) {
      $container.append(
        '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300">Mother: Kuwaiti</span>'
      );
    }
  },

  // Populate eligibility case information
  populateEligibilityCase: function () {
    const $container = $("#review-eligibility-case");
    const caseDescriptions = {
      A: "Case A: All three family members are Kuwaiti citizens",
      B: "Case B: Father and Student are Kuwaiti citizens (Mother is not)",
      C: "Case C: Mother is Kuwaiti citizen only (Father and Student are not)",
      D: "Case D: No family members are Kuwaiti citizens (Ineligible)",
    };

    const caseColors = {
      A: "text-green-600",
      B: "text-blue-600",
      C: "text-purple-600",
      D: "text-red-600",
    };

    $container.html(
      `<span class="${caseColors[this.eligibilityCase]} font-medium">${
        caseDescriptions[this.eligibilityCase]
      }</span>`
    );
  },

  // Populate case-specific requirements with deceased parent logic
  populateCaseRequirements: function () {
    const $container = $("#review-case-requirements");
    const fatherDeceased = $("#fatherDeceased").is(":checked");
    const motherDeceased = $("#motherDeceased").is(":checked");

    const caseRequirements = {
      A: {
        description: "All three family members are Kuwaiti citizens",
        documents: this.getCaseADocuments(fatherDeceased, motherDeceased),
        total: this.getCaseADocuments(fatherDeceased, motherDeceased).length,
      },
      B: {
        description: "Father and Student are Kuwaiti citizens (Mother is not)",
        documents: this.getCaseBDocuments(fatherDeceased),
        total: this.getCaseBDocuments(fatherDeceased).length,
      },
      C: {
        description:
          "Mother is Kuwaiti citizen only (Father and Student are not)",
        documents: this.getCaseCDocuments(motherDeceased),
        total: this.getCaseCDocuments(motherDeceased).length,
      },
      D: {
        description: "No family members are Kuwaiti citizens (Ineligible)",
        documents: [],
        total: 0,
      },
    };

    const requirements = caseRequirements[this.eligibilityCase];

    if (requirements.total === 0) {
      $container.html(
        '<span class="text-red-600 font-medium">No documents required - Application is ineligible</span>'
      );
    } else {
      let html = `<div class="mb-2 text-gray-700 dark:text-gray-300">${requirements.description}</div>`;
      html += `<div class="mb-2"><strong>Total Documents Required: ${requirements.total}</strong></div>`;
      html +=
        '<ul class="list-disc list-inside space-y-1 text-sm text-gray-600 dark:text-gray-400">';

      requirements.documents.forEach((doc) => {
        html += `<li>${doc}</li>`;
      });

      html += "</ul>";
      $container.html(html);
    }
  },

  // Populate documents list
  populateDocumentsList: function () {
    const $container = $("#review-documents");
    $container.empty();

    // Check each file input
    $('input[type="file"]').each(function () {
      const $input = $(this);
      const file = this.files[0];

      if (file) {
        const fileName = file.name;
        const fileSize = (file.size / 1024 / 1024).toFixed(2) + " MB";
        const label = $input
          .closest(".mb-6")
          .find("label")
          .text()
          .replace("*", "")
          .trim();

        $container.append(`
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg dark:bg-gray-700">
                        <div class="flex items-center">
                            <i class="fas fa-file-alt text-blue-600 me-3"></i>
                            <div>
                                <div class="text-sm font-medium text-gray-900 dark:text-white">${label}</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">${fileName} (${fileSize})</div>
                            </div>
                        </div>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                            <i class="fas fa-check me-1"></i>
                            Uploaded
                        </span>
                    </div>
                `);
      }
    });

    if ($container.children().length === 0) {
      $container.append(
        '<p class="text-sm text-gray-500 dark:text-gray-400">No documents uploaded</p>'
      );
    }
  },

  // Submit form via AJAX
  submitForm: function (e) {
    e.preventDefault();

    // Phase 5: Enhanced validation before submission
    const clientValidationResult = this.validateDocumentsClientSide();
    if (!clientValidationResult.isValid) {
      this.displayValidationErrors(clientValidationResult.errors);
      SharedForms.Toast.show(
        "Please correct the validation errors before submitting.",
        "error"
      );
      return;
    }

    // Show loading spinner
    LoadingSpinner.show("form-loading", "Processing your application...");

    // Disable submit button
    $("#btn-submit").prop("disabled", true);

    // Collect form data with proper checkbox handling
    const formData = this.collectFormData();

    // Add anti-forgery token
    const token = $('input[name="__RequestVerificationToken"]').val();
    if (token) {
      formData.append("__RequestVerificationToken", token);
    } else {
      console.warn("Antiforgery token not found in form");
    }

    $.ajax({
      url: "/KuwaitiStudentInfo/SubmitForm",
      type: "POST",
      data: formData,
      processData: false,
      contentType: false,
      success: (response) => {
        LoadingSpinner.hide("form-loading");

        if (response.success) {
          SharedForms.Toast.show(
            response.message || "Application submitted successfully!",
            "success"
          );

          // Redirect to summary page
          setTimeout(() => {
            window.location.href = response.redirectUrl;
          }, 1500);
        } else {
          $("#btn-submit").prop("disabled", false);

          if (response.isEligible === false) {
            // Redirect to not eligible page
            SharedForms.Toast.show(
              response.message || "You are not eligible for this program.",
              "error"
            );
            setTimeout(() => {
              window.location.href = response.redirectUrl;
            }, 2000);
          } else {
            // Display validation errors
            this.displayValidationErrors(response.errors);
          }
        }
      },
      error: (xhr, status, error) => {
        LoadingSpinner.hide("form-loading");
        $("#btn-submit").prop("disabled", false);

        console.error("Submission error:", error);
        SharedForms.Toast.show(
          "An error occurred while submitting your application. Please try again.",
          "error"
        );
      },
    });
  },

  // Initialize SIS integration features
  initializeSisIntegration: function () {
    // Check if form has SIS pre-filled data
    const isPreFilled = $(".sis-prefill-alert").length > 0;

    if (isPreFilled) {
      // Get pre-filled and read-only fields from the form
      const readOnlyFields = [];
      const preFilledFields = [];

      $(".sis-readonly").each(function () {
        const fieldName = $(this).attr("name") || $(this).attr("id");
        if (fieldName) {
          readOnlyFields.push(fieldName);
        }
      });

      $(".sis-badge").each(function () {
        const fieldContainer = $(this).closest(".sis-field-container");
        const field = fieldContainer.find("input, select, textarea").first();
        const fieldName = field.attr("name") || field.attr("id");
        if (fieldName) {
          preFilledFields.push(fieldName);
        }
      });

      // Initialize SIS integration with detected fields
      if (typeof SharedForms !== "undefined" && SharedForms.SisIntegration) {
        SharedForms.SisIntegration.initialize({
          readOnlyFields: readOnlyFields,
          preFilledFields: preFilledFields,
          dataFreshnessThreshold: 24,
          enableTooltips: true,
        });
      }

      // Add custom validation for SIS fields
      this.setupSisValidation();
    }
  }, // Setup validation for SIS fields
  setupSisValidation: function () {
    // Check if validateAllFields method exists before overriding
    if (typeof this.validateAllFields === "function") {
      // Override existing validateAllFields method to include SIS field validation
      const originalValidateAllFields = this.validateAllFields;
      this.validateAllFields = function () {
        let isValid = originalValidateAllFields.call(this);

        // Validate SIS read-only fields haven't been tampered with
        if (typeof SharedForms !== "undefined" && SharedForms.SisIntegration) {
          const sisValid = SharedForms.SisIntegration.validateReadOnlyFields();
          if (!sisValid) {
            SharedForms.Toast.show(
              "Some pre-filled fields have been modified. Please refresh the page.",
              "error"
            );
            isValid = false;
          }
        }

        return isValid;
      }.bind(this);
    } else {
      // Create validateAllFields method since it doesn't exist
      this.validateAllFields = function () {
        let isValid = true;

        // Validate SIS read-only fields haven't been tampered with
        if (typeof SharedForms !== "undefined" && SharedForms.SisIntegration) {
          const sisValid = SharedForms.SisIntegration.validateReadOnlyFields();
          if (!sisValid) {
            SharedForms.Toast.show(
              "Some pre-filled fields have been modified. Please refresh the page.",
              "error"
            );
            isValid = false;
          }
        }

        return isValid;
      }.bind(this);
    }

    // Also enhance validateCurrentStep to include SIS validation
    const originalValidateCurrentStep = this.validateCurrentStep;
    this.validateCurrentStep = function () {
      return new Promise((resolve) => {
        // First run SIS validation
        const sisValid = this.validateAllFields();
        if (!sisValid) {
          resolve(false);
          return;
        }

        // Then run the original validation logic
        originalValidateCurrentStep.call(this).then(resolve);
      });
    }.bind(this);
  },

  // Display validation errors
  displayValidationErrors: function (errors) {
    if (!errors || Object.keys(errors).length === 0) return;

    // Clear previous validation states
    $(".border-red-500").removeClass("border-red-500");
    $(".text-red-600").text("");

    // Phase 5: Check for document-related errors
    let hasDocumentErrors = false;
    let generalMessage = "";

    // Display errors for each field
    Object.keys(errors).forEach((fieldName) => {
      const errorMessages = errors[fieldName];
      if (errorMessages && errorMessages.length > 0) {
        // Check if this is a document-related error
        if (fieldName.includes("File") || fieldName === "general") {
          hasDocumentErrors = true;
          if (fieldName === "general") {
            generalMessage = errorMessages[0];
          }
        }

        // Find the input field
        const $field = $(`[name="${fieldName}"]`);
        if ($field.length > 0) {
          $field.addClass("border-red-500");

          // Find the validation span
          const $validationSpan = $field
            .closest(".mb-6, .mb-4")
            .find(`[data-valmsg-for="${fieldName}"], .text-red-600`);
          if ($validationSpan.length > 0) {
            $validationSpan.text(errorMessages[0]);
          }
        }
      }
    });

    // Phase 5: Show/hide document validation alert
    if (hasDocumentErrors) {
      this.showDocumentValidationAlert(generalMessage);
    } else {
      this.hideDocumentValidationAlert();
    }

    // Show validation summary
    this.showValidationSummary(errors);

    // Scroll to first error
    this.scrollToFirstError();
  },

  // Show validation summary
  showValidationSummary: function (errors) {
    const $summary = $("#validation-summary");
    const $errorList = $summary.find("ul");

    // Clear existing errors
    $errorList.empty();

    // Add new errors
    Object.keys(errors).forEach((fieldName) => {
      const errorMessages = errors[fieldName];
      errorMessages.forEach((message) => {
        $errorList.append(`<li>${message}</li>`);
      });
    });

    // Show summary
    $summary.removeClass("hidden");
  },

  // Clear validation summary
  clearValidationSummary: function () {
    $("#validation-summary").addClass("hidden");
    $(".border-red-500").removeClass("border-red-500");
    $(".text-red-600").text("");
    // Phase 5: Also hide document validation alert
    this.hideDocumentValidationAlert();
  },

  // Scroll to first error
  scrollToFirstError: function () {
    const $firstError = $(".border-red-500").first();
    if ($firstError.length > 0) {
      $("html, body").animate(
        {
          scrollTop: $firstError.offset().top - 100,
        },
        500
      );
    }
  },

  // Scroll to top of page
  scrollToTop: function () {
    $("html, body").animate(
      {
        scrollTop: 0,
      },
      300
    );
  },

  // Phase 5: Helper methods for case-specific validation

  // Get eligibility case based on current form state
  getEligibilityCase: function () {
    const studentIsKuwaiti = $("#studentKuwaiti").is(":checked");
    const fatherIsKuwaiti = $("#fatherKuwaiti").is(":checked");
    const motherIsKuwaiti = $("#motherKuwaiti").is(":checked");

    // Case A: All three are Kuwaiti
    if (studentIsKuwaiti && fatherIsKuwaiti && motherIsKuwaiti) {
      return "A";
    }
    // Case B: Father & Student Kuwaiti (Mother is not)
    else if (studentIsKuwaiti && fatherIsKuwaiti && !motherIsKuwaiti) {
      return "B";
    }
    // Case C: Mother Kuwaiti only (Father and Student are not)
    else if (!studentIsKuwaiti && !fatherIsKuwaiti && motherIsKuwaiti) {
      return "C";
    }
    // Case D: None are Kuwaiti or any other combination - Ineligible
    else {
      return "D";
    }
  },

  // Get required file uploads for a specific case with deceased parent logic
  getRequiredFileUploads: function (eligibilityCase) {
    const requiredFiles = [];
    const fatherDeceased = $("#fatherDeceased").is(":checked");
    const motherDeceased = $("#motherDeceased").is(":checked");

    switch (eligibilityCase) {
      case "A": // All Kuwaiti
        // Student documents (always required)
        requiredFiles.push("StudentCivilIdFile");
        requiredFiles.push("StudentNationalityCertificateFile");
        requiredFiles.push("StudentBirthCertificateFile");

        // Father documents (conditional on deceased status)
        if (fatherDeceased) {
          requiredFiles.push("FatherDeathCertificateFile");
        } else {
          requiredFiles.push("FatherCivilIdFile");
          requiredFiles.push("FatherNationalityCertificateFile");
        }

        // Mother documents (conditional on deceased status)
        if (motherDeceased) {
          requiredFiles.push("MotherDeathCertificateFile");
        } else {
          requiredFiles.push("MotherCivilIdFile");
          requiredFiles.push("MotherNationalityCertificateFile");
        }
        break;

      case "B": // Father & Student Kuwaiti
        // Student documents (always required)
        requiredFiles.push("StudentCivilIdFile");
        requiredFiles.push("StudentNationalityCertificateFile");
        requiredFiles.push("StudentBirthCertificateFile");

        // Father documents (conditional on deceased status)
        if (fatherDeceased) {
          requiredFiles.push("FatherDeathCertificateFile");
        } else {
          requiredFiles.push("FatherCivilIdFile");
          requiredFiles.push("FatherNationalityCertificateFile");
        }
        break;

      case "C": // Mother Kuwaiti only
        // Student documents (Civil ID + Birth Cert only, no nationality cert needed)
        requiredFiles.push("StudentCivilIdFile");
        requiredFiles.push("StudentBirthCertificateFile");

        // Mother documents (conditional on deceased status)
        if (motherDeceased) {
          requiredFiles.push("MotherDeathCertificateFile");
        } else {
          requiredFiles.push("MotherCivilIdFile");
          requiredFiles.push("MotherNationalityCertificateFile");
        }
        break;

      case "D": // None Kuwaiti - No documents required (submission blocked anyway)
      default:
        break;
    }

    return requiredFiles;
  },

  // Helper methods for case-specific document lists
  getCaseADocuments: function (fatherDeceased, motherDeceased) {
    const documents = [
      "Student Civil ID Document",
      "Student Nationality Certificate",
      "Student Birth Certificate",
    ];

    if (fatherDeceased) {
      documents.push("Father Death Certificate");
    } else {
      documents.push("Father Civil ID Document");
      documents.push("Father Nationality Certificate");
    }

    if (motherDeceased) {
      documents.push("Mother Death Certificate");
    } else {
      documents.push("Mother Civil ID Document");
      documents.push("Mother Nationality Certificate");
    }

    return documents;
  },

  getCaseBDocuments: function (fatherDeceased) {
    const documents = [
      "Student Civil ID Document",
      "Student Nationality Certificate",
      "Student Birth Certificate",
    ];

    if (fatherDeceased) {
      documents.push("Father Death Certificate");
    } else {
      documents.push("Father Civil ID Document");
      documents.push("Father Nationality Certificate");
    }

    return documents;
  },

  getCaseCDocuments: function (motherDeceased) {
    const documents = [
      "Student Civil ID Document",
      "Student Birth Certificate",
    ];

    if (motherDeceased) {
      documents.push("Mother Death Certificate");
    } else {
      documents.push("Mother Civil ID Document");
      documents.push("Mother Nationality Certificate");
    }

    return documents;
  },

  // Get case description
  getCaseDescription: function (eligibilityCase) {
    switch (eligibilityCase) {
      case "A":
        return "All family members are Kuwaiti citizens - Student, Father, and Mother";
      case "B":
        return "Father and Student are Kuwaiti citizens - Mother is not Kuwaiti";
      case "C":
        return "Only Mother is a Kuwaiti citizen - Student and Father are not Kuwaiti";
      case "D":
        return "No family members are Kuwaiti citizens - Application ineligible";
      default:
        return "Unknown eligibility case";
    }
  },

  // Get display name for file properties
  getFileDisplayName: function (propertyName) {
    switch (propertyName) {
      case "StudentCivilIdFile":
        return "Student Civil ID Document";
      case "StudentNationalityCertificateFile":
        return "Student Nationality Certificate";
      case "StudentBirthCertificateFile":
        return "Student Birth Certificate";
      case "FatherCivilIdFile":
        return "Father's Civil ID Document";
      case "FatherNationalityCertificateFile":
        return "Father's Nationality Certificate";
      case "FatherDeathCertificateFile":
        return "Father's Death Certificate";
      case "MotherCivilIdFile":
        return "Mother's Civil ID Document";
      case "MotherNationalityCertificateFile":
        return "Mother's Nationality Certificate";
      case "MotherDeathCertificateFile":
        return "Mother's Death Certificate";
      default:
        return propertyName;
    }
  },

  // Get case-specific error message for missing documents
  getCaseSpecificErrorMessage: function (
    fileProperty,
    eligibilityCase,
    displayName
  ) {
    const caseContext = this.getCaseDescription(eligibilityCase);

    switch (fileProperty) {
      case "StudentCivilIdFile":
        return `${displayName} is required for Case ${eligibilityCase} (${caseContext}). Please upload the student's Kuwait Civil ID.`;
      case "StudentNationalityCertificateFile":
        return `${displayName} is required for Case ${eligibilityCase} (${caseContext}). Please upload the student's nationality certificate for verification.`;
      case "FatherCivilIdFile":
        return `${displayName} is required for Case ${eligibilityCase} (${caseContext}). Please upload the father's Kuwait Civil ID.`;
      case "FatherNationalityCertificateFile":
        return `${displayName} is required for Case ${eligibilityCase} (${caseContext}). Please upload the father's nationality certificate for verification.`;
      case "MotherCivilIdFile":
        return `${displayName} is required for Case ${eligibilityCase} (${caseContext}). Please upload the mother's Kuwait Civil ID.`;
      case "MotherNationalityCertificateFile":
        return `${displayName} is required for Case ${eligibilityCase} (${caseContext}). Please upload the mother's nationality certificate for verification.`;
      default:
        return `${displayName} is required for Case ${eligibilityCase}. Please upload this document to proceed.`;
    }
  },

  // Phase 5: Show document validation alert
  showDocumentValidationAlert: function (message) {
    const $alert = $("#document-validation-alert");
    const $messageDiv = $("#document-validation-message");

    if (message) {
      $messageDiv.text(message);
    } else {
      $messageDiv.text(
        "Please upload all required documents for your eligibility case before proceeding."
      );
    }

    $alert.removeClass("hidden");
  },

  // Phase 5: Hide document validation alert
  hideDocumentValidationAlert: function () {
    const $alert = $("#document-validation-alert");
    $alert.addClass("hidden");
  },
};

// Initialize wizard when document is ready
$(document).ready(function () {
  KuwaitiStudentWizard.init();
});
