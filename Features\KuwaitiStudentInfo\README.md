# Kuwaiti Student Information Form Module

This module implements the Kuwaiti Student Information Form following the modular MVC pattern established in the forms.ktech solution.

## 📁 Module Structure

```
/Features/KuwaitiStudentInfo/
├── Controllers/
│   └── KuwaitiStudentInfoController.cs    # Main controller with CollectInfo, NotEligible, Summary actions
├── Models/
│   ├── StudentInfo.cs                     # Entity class (inherits from BaseEntity)
│   └── StudentFormViewModel.cs            # ViewModel (implements IFormViewModel, IFileUploadViewModel)
├── Views/
│   └── KuwaitiStudentInfo/
│       ├── CollectInfo.cshtml             # Main form with conditional sections
│       ├── NotEligible.cshtml             # Rejection page for Case D
│       └── Summary.cshtml                 # Success page with file downloads
└── README.md                              # This file
```

## 🎯 Form Purpose

Collects and verifies documents from students who are:
- Kuwaiti nationals (through father or both parents)
- Non-Kuwaiti students with a Kuwaiti mother

## 🧠 Eligibility Logic (4 Cases)

| Case | Father Kuwaiti | Mother Kuwaiti | Student Kuwaiti | Eligible? | Required Documents |
|------|----------------|----------------|------------------|-----------|-------------------|
| A    | ✅ Yes         | ✅ Yes         | ✅ Yes          | ✅ Yes    | All: Civil ID + Nationality Certificate |
| B    | ✅ Yes         | ❌ No          | ✅ Yes          | ✅ Yes    | Student & Father: Civil ID + Nationality Certificate |
| C    | ❌ No          | ✅ Yes         | ❌ No           | ✅ Yes    | Student: Civil ID; Mother: Civil ID + Nationality Certificate |
| D    | ❌ No          | ❌ No          | ❌ No           | ❌ No     | Redirect to NotEligible page |

## 📋 Implementation Checklist

### Phase 2: Data Layer
- [ ] Create `StudentInfo.cs` entity (inherits from BaseEntity)
- [ ] Create `StudentFormViewModel.cs` with shared validation attributes
- [ ] Add DbSet to FormsKTechContext
- [ ] Run database migration

### Phase 3: Service Layer
- [ ] Create `KuwaitiStudentInfoHandler.cs` implementing IFormHandler
- [ ] Implement eligibility logic for 4 cases
- [ ] Register handler in DI container

### Phase 4: Controller
- [ ] Create `KuwaitiStudentInfoController.cs`
- [ ] Implement GET/POST CollectInfo actions
- [ ] Implement NotEligible and Summary actions
- [ ] Add [Authorize] and routing attributes

### Phase 5: Views
- [ ] Create CollectInfo.cshtml with conditional sections
- [ ] Create NotEligible.cshtml with clear explanation
- [ ] Create Summary.cshtml with file download links
- [ ] Use shared components (_FileUpload, _ValidationSummary, etc.)

## 🔗 Routing

- `/KuwaitiStudentInfo/CollectInfo` - Main form
- `/KuwaitiStudentInfo/NotEligible` - Rejection page
- `/KuwaitiStudentInfo/Summary/{id}` - Success page

## 📁 File Storage

Files are stored in: `/wwwroot/uploads/KuwaitiStudentInfo/{SubmissionGuid}/`

## 🔧 Shared Components Used

- **BaseEntity** - Common entity properties
- **IFormViewModel** - Base form interface
- **IFileUploadViewModel** - File upload interface
- **IFormHandler** - Generic form handler
- **Validation Attributes** - FileSizeAttribute, ConditionalRequiredAttribute, etc.
- **File Upload Service** - Secure file handling
- **Razor Components** - _FileUpload, _ValidationSummary, _LoadingSpinner

## 📚 References

- [Shared Components Guide](../../../SHARED_COMPONENTS_GUIDE.md)
- [Task List](../../../KuwaitiStudentInfo_TaskList.md)
- [PRD Documentation](../../../README.md)
