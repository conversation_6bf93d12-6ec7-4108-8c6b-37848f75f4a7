{
  "ConnectionStrings": {
    // "DefaultConnection": "Data Source=FormsKTechDb.sqlite",
    "DefaultConnection": "Server=.;Database=FormsKTechDb;User Id=dbuser; Password=********; TrustServerCertificate=True;",
    "SqlServerConnection": "Server=.;Database=FormsKTechDb;User Id=dbuser; Password=********; TrustServerCertificate=True;"
  },
  "AzureAd": {
    "Instance": "https://login.microsoftonline.com/",
    "Domain": "ktech.edu.kw",
    "TenantId": "e536d3ca-d4dd-4dcc-9f83-58add1d31431",
    "ClientId": "4ec9a798-2ba2-4f75-96ee-e7ec4c0887b8",
    "CallbackPath": "/signin-oidc"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore.Database.Command": "Warning",
      "Forms.ktech.Services.SIS": "Debug"
    }
  },
  "AllowedHosts": "*",
  "DatabaseProvider": "SqlServer",
  "Email": {
    "SmtpHost": "smtp.office365.com",
    "SmtpPort": "587",
    "SmtpUsername": "<EMAIL>",
    "SmtpPassword": "Later2Know@ktech",
    "FromEmail": "<EMAIL>",
    "FromName": "KTECH Forms System",
    "TemplatePath": "wwwroot/Template/Email-Template.html",
    "MaxEmailsPerHour": 10,
    "EnableEmailQueue": true
  },
  "Application": {
    "BaseUrl": "https://localhost:7045"
  },
  "SisApi": {
    "BaseUrl": "https://sisapi.ktech.edu.kw/api",
    "Username": "UbHmHOf5MCiEv1sbimDE",
    "Password": "puSU7xxOBYV6CaIINSY1",
    "TimeoutSeconds": 60,
    "RetryAttempts": 2,
    "RetryDelayMs": 500,
    "BatchSize": 50,
    "MaxConcurrentRequests": 3,
    "AutoSyncEnabled": false,
    "SyncSchedule": "0 */6 * * *",
    "DataFreshnessHours": 12,
    "MaxSyncHistoryDays": 30,
    "EnableIncrementalSync": true,
    "CircuitBreakerFailureThreshold": 3,
    "CircuitBreakerTimeoutSeconds": 30,
    "CircuitBreakerSuccessThreshold": 2,
    "StudentsEndpoint": "/Students",
    "StudentsFilterEndpoint": "/Students/filter",
    "EnrolledUsersEndpoint": "/EnrolledUsers",
    "EnrolledUsersFilterEndpoint": "/EnrolledUsers/filter"
  }
}
