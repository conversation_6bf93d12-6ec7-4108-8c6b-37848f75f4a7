/* Forms.ktech - Shared Form Styles */

/* Navigation Enhancements */
.navbar-nav .dropdown-menu {
    border: 1px solid #dee2e6;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border-radius: 0.375rem;
}

.navbar-nav .dropdown-item {
    padding: 0.5rem 1rem;
    transition: background-color 0.15s ease-in-out;
}

.navbar-nav .dropdown-item:hover {
    background-color: #f8f9fa;
}

.navbar-nav .dropdown-item i {
    width: 1.2rem;
    text-align: center;
}

/* Breadcrumb Styling */
.breadcrumb {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #6c757d;
}

.breadcrumb-item.active {
    color: #495057;
    font-weight: 500;
}

/* Form Navigation Buttons */
.form-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
}

.form-navigation .btn-group {
    display: flex;
    gap: 0.5rem;
}

.btn-back {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.btn-back:hover {
    background-color: #5a6268;
    border-color: #545b62;
    color: white;
}

.btn-cancel {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.btn-cancel:hover {
    background-color: #c82333;
    border-color: #bd2130;
    color: white;
}

/* Form Progress Indicator */
.form-progress {
    margin-bottom: 2rem;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.progress-step {
    flex: 1;
    text-align: center;
    position: relative;
    padding: 0.5rem;
}

.progress-step::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #dee2e6;
    z-index: 1;
}

.progress-step:first-child::before {
    left: 50%;
}

.progress-step:last-child::before {
    right: 50%;
}

.progress-step.active::before {
    background-color: #007bff;
}

.progress-step.completed::before {
    background-color: #28a745;
}

.step-indicator {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background-color: #dee2e6;
    color: #6c757d;
    line-height: 2rem;
    text-align: center;
    font-weight: bold;
    position: relative;
    z-index: 2;
    margin-bottom: 0.5rem;
}

.progress-step.active .step-indicator {
    background-color: #007bff;
    color: white;
}

.progress-step.completed .step-indicator {
    background-color: #28a745;
    color: white;
}

.step-label {
    font-size: 0.875rem;
    color: #6c757d;
}

.progress-step.active .step-label {
    color: #007bff;
    font-weight: 500;
}

.progress-step.completed .step-label {
    color: #28a745;
    font-weight: 500;
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
    .form-navigation {
        flex-direction: column;
        gap: 1rem;
    }
    
    .form-navigation .btn-group {
        width: 100%;
        justify-content: center;
    }
    
    .progress-steps {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .progress-step::before {
        display: none;
    }
    
    .step-indicator {
        margin-right: 0.5rem;
        margin-bottom: 0;
    }
    
    .progress-step {
        display: flex;
        align-items: center;
        text-align: left;
        padding: 0.25rem;
    }
}

/* Accessibility Enhancements */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus indicators for keyboard navigation */
.btn:focus,
.nav-link:focus,
.dropdown-item:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .breadcrumb {
        border: 1px solid #000;
    }
    
    .progress-step::before {
        background-color: #000;
    }
    
    .step-indicator {
        border: 2px solid #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .dropdown-item,
    .btn {
        transition: none;
    }
}
