using System.ComponentModel.DataAnnotations;
using System.Reflection;

namespace Forms.ktech.Shared.Validation
{
    /// <summary>
    /// Validation attribute that makes a property required based on the value of another property
    /// </summary>
    public class ConditionalRequiredAttribute : ValidationAttribute
    {
        private readonly string _dependentProperty;
        private readonly object _targetValue;

        /// <summary>
        /// Initializes a new instance of ConditionalRequiredAttribute
        /// </summary>
        /// <param name="dependentProperty">The name of the property to check</param>
        /// <param name="targetValue">The value that makes this property required</param>
        public ConditionalRequiredAttribute(string dependentProperty, object targetValue)
        {
            _dependentProperty = dependentProperty;
            _targetValue = targetValue;
            ErrorMessage = "This field is required.";
        }

        /// <summary>
        /// Validates the property based on the dependent property value
        /// </summary>
        /// <param name="value">The value to validate</param>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation result</returns>
        protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
        {
            // Get the dependent property value
            var dependentProperty = validationContext.ObjectType.GetProperty(_dependentProperty);
            if (dependentProperty == null)
            {
                return new ValidationResult($"Property '{_dependentProperty}' not found.");
            }

            var dependentValue = dependentProperty.GetValue(validationContext.ObjectInstance);

            // Check if the dependent property has the target value
            bool isRequired = false;
            if (_targetValue is bool targetBool && dependentValue is bool dependentBool)
            {
                isRequired = dependentBool == targetBool;
            }
            else
            {
                isRequired = Equals(dependentValue, _targetValue);
            }

            // If the field is required, validate it
            if (isRequired)
            {
                if (value == null)
                {
                    return new ValidationResult(ErrorMessage, new[] { validationContext.MemberName });
                }

                // Special handling for IFormFile
                if (value is IFormFile file)
                {
                    if (file.Length == 0)
                    {
                        return new ValidationResult(ErrorMessage, new[] { validationContext.MemberName });
                    }
                }
                // Special handling for strings
                else if (value is string stringValue)
                {
                    if (string.IsNullOrWhiteSpace(stringValue))
                    {
                        return new ValidationResult(ErrorMessage, new[] { validationContext.MemberName });
                    }
                }
            }

            return ValidationResult.Success;
        }
    }
}
