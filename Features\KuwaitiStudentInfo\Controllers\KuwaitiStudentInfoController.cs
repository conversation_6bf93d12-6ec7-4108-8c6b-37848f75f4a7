using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using Forms.ktech.Data;
using Forms.ktech.Features.KuwaitiStudentInfo.Models;
using Forms.ktech.Services;
using Forms.ktech.Services.SIS;

namespace Forms.ktech.Features.KuwaitiStudentInfo.Controllers
{
    /// <summary>
    /// Controller for handling Kuwaiti Student Information form submissions
    /// Implements the complete user flow: form display → validation → eligibility check → save/redirect → summary display
    /// </summary>
    [Authorize]
    [Route("KuwaitiStudentInfo")]
    public class KuwaitiStudentInfoController : Controller
    {
        private readonly IFormHandler<StudentFormViewModel, StudentInfo> _formHandler;
        private readonly FormsKTechContext _context;
        private readonly ILogger<KuwaitiStudentInfoController> _logger;
        private readonly IStudentLookupService _studentLookupService;

        public KuwaitiStudentInfoController(
            IFormHandler<StudentFormViewModel, StudentInfo> formHand<PERSON>,
            FormsKTechContext context,
            ILogger<KuwaitiStudentInfoController> logger,
            IStudentLookupService studentLookupService)
        {
            _formHandler = formHandler;
            _context = context;
            _logger = logger;
            _studentLookupService = studentLookupService;
        }

        #region Helper Methods

        /// <summary>
        /// Gets the current user's ID from Azure AD claims
        /// </summary>
        /// <returns>User ID string or null if not found</returns>
        private string? GetCurrentUserId()
        {
            return User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        }

        /// <summary>
        /// Gets the current user's display name from Azure AD claims
        /// </summary>
        /// <returns>User display name or "Unknown User" if not found</returns>
        private string GetCurrentUserDisplayName()
        {
            return User.FindFirst("name")?.Value ??
                   User.FindFirst(ClaimTypes.Name)?.Value ??
                   "Unknown User";
        }

        /// <summary>
        /// Gets the current user's email from Azure AD claims
        /// </summary>
        /// <returns>User email or null if not found</returns>
        private string? GetCurrentUserEmail()
        {
            return User.FindFirst(ClaimTypes.Email)?.Value ??
                   User.FindFirst("preferred_username")?.Value ??
                   User.FindFirst("email")?.Value;
        }

        #endregion

        #region CollectInfo Actions

        /// <summary>
        /// GET: /KuwaitiStudentInfo/CollectInfo
        /// Displays the form for collecting student information with SIS pre-filling
        /// Implements edit mode if user has already submitted for their Civil ID
        /// </summary>
        /// <returns>View with StudentFormViewModel (pre-filled if SIS data available or edit mode)</returns>
        [HttpGet("CollectInfo")]
        public async Task<IActionResult> CollectInfo()
        {
            try
            {
                var userId = GetCurrentUserId();
                var userDisplayName = GetCurrentUserDisplayName();
                var userEmail = GetCurrentUserEmail();

                _logger.LogInformation("User {UserDisplayName} ({UserId}) accessed Kuwaiti Student Info form",
                    userDisplayName, userId);

                // First, try to pre-fill from SIS data
                var viewModel = new StudentFormViewModel();

                // Attempt SIS pre-filling if user email is available
                if (!string.IsNullOrWhiteSpace(userEmail))
                {
                    try
                    {
                        _logger.LogInformation("Attempting SIS pre-fill for user {UserDisplayName} ({UserId}) with email: {UserEmail}",
                            userDisplayName, userId, userEmail);

                        viewModel = await _studentLookupService.PreFillFormAsync(viewModel, userEmail);
                        if (viewModel.IsPreFilled)
                        {
                            _logger.LogInformation("Form successfully pre-filled from SIS for user {UserId} with student ID: {StudentId}",
                                userId, viewModel.PreFilledFromStudentId);
                        }
                        else
                        {
                            _logger.LogInformation("No SIS data found for user {UserId} with email: {UserEmail}",
                                userId, userEmail);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "SIS pre-fill failed for user {UserId} with email {UserEmail}, continuing with empty form",
                            userId, userEmail);
                    }
                }
                else
                {
                    _logger.LogWarning("No email available for user {UserId}, cannot attempt SIS pre-fill", userId);
                }

                // Check for existing submission if we have a Civil ID (from SIS or manual entry)
                if (!string.IsNullOrWhiteSpace(viewModel.StudentCivilId))
                {
                    var existingSubmission = await _formHandler.GetExistingSubmissionByCivilIdAsync(
                        viewModel.StudentCivilId, userId);

                    if (existingSubmission != null)
                    {
                        _logger.LogInformation("Found existing submission {EntityId} for user {UserId}, loading edit mode",
                            existingSubmission.Id, userId);

                        // Load existing data for edit mode
                        viewModel = _formHandler.MapToViewModel(existingSubmission);
                        ViewBag.IsEditMode = true;
                        ViewBag.ExistingSubmissionId = existingSubmission.Id;
                        ViewBag.OriginalSubmissionDate = existingSubmission.CreatedDate;

                        TempData["InfoMessage"] = "You have already submitted this form. You can review and update your information below.";
                    }
                }

                // If not in edit mode, set appropriate user context messages
                if (ViewBag.IsEditMode != true)
                {
                    if (viewModel.IsPreFilled)
                    {
                        TempData["SuccessMessage"] = $"Welcome! Your information has been pre-filled from our student records. " +
                                                    $"Please review and update any details as needed.";
                        ViewBag.PreFillDescription = viewModel.GetPreFillDescription();
                    }
                    else
                    {
                        TempData["InfoMessage"] = "Please fill in your information manually. " +
                                                 "If you are a current student and this information should be available, " +
                                                 "please contact support.";
                    }
                }

                // Set user context for the form
                ViewBag.UserDisplayName = userDisplayName;
                ViewBag.UserEmail = userEmail;

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading Kuwaiti Student Info form for user {UserId}", GetCurrentUserId());
                TempData["ErrorMessage"] = "An error occurred while loading the form. Please try again.";
                return RedirectToAction("Index", "Home");
            }
        }

        /// <summary>
        /// POST: /KuwaitiStudentInfo/ValidateStep
        /// AJAX endpoint for validating individual wizard steps
        /// </summary>
        /// <param name="step">The step number to validate</param>
        /// <param name="viewModel">The form data to validate</param>
        /// <returns>JSON response with validation results</returns>
        [HttpPost("ValidateStep")]
        [ValidateAntiForgeryToken]
        public IActionResult ValidateStep(int step, StudentFormViewModel viewModel)
        {
            try
            {
                _logger.LogInformation("ValidateStep called - Step: {Step}, User: {UserId}", step, GetCurrentUserId());
                _logger.LogInformation("ViewModel - StudentName: {StudentName}, StudentIsKuwaiti: {StudentIsKuwaiti}, FatherIsKuwaiti: {FatherIsKuwaiti}, MotherIsKuwaiti: {MotherIsKuwaiti}",
                    viewModel.StudentName, viewModel.StudentIsKuwaiti, viewModel.FatherIsKuwaiti, viewModel.MotherIsKuwaiti);
                var validationResult = new
                {
                    isValid = true,
                    errors = new Dictionary<string, List<string>>(),
                    eligibilityCase = viewModel.GetEligibilityCase(),
                    isEligible = viewModel.IsEligible(),
                    requiredFiles = viewModel.GetRequiredFileUploads()
                };

                // Validate specific step
                switch (step)
                {
                    case 1:
                        ValidateStep1(viewModel, validationResult.errors);
                        break;
                    case 2:
                        ValidateStep2(viewModel, validationResult.errors);
                        break;
                    case 3:
                        ValidateStep3(viewModel, validationResult.errors);
                        break;
                }

                return Json(new
                {
                    isValid = !validationResult.errors.Any(),
                    errors = validationResult.errors,
                    eligibilityCase = validationResult.eligibilityCase,
                    isEligible = validationResult.isEligible,
                    requiredFiles = validationResult.requiredFiles
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating step {Step} for user {UserId}", step, GetCurrentUserId());
                return Json(new { isValid = false, errors = new { general = new[] { "An error occurred during validation." } } });
            }
        }

        /// <summary>
        /// POST: /KuwaitiStudentInfo/CollectInfo
        /// Processes the submitted form data, validates eligibility, and saves or redirects
        /// </summary>
        /// <param name="viewModel">The submitted form data</param>
        /// <returns>Redirect to Summary on success, NotEligible on Case D, or back to form on validation errors</returns>
        [HttpPost("CollectInfo")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CollectInfo(StudentFormViewModel viewModel)
        {
            var userId = GetCurrentUserId();
            var userDisplayName = GetCurrentUserDisplayName();

            try
            {
                _logger.LogInformation("REGULAR POST: Processing form submission from user {UserDisplayName} ({UserId}). " +
                    "Eligibility Case: {EligibilityCase}. This indicates the form was submitted via regular POST, not AJAX.",
                    userDisplayName, userId, viewModel.GetEligibilityCase());

                // Phase 5: Enhanced validation with case-specific document requirements
                var validationErrors = new Dictionary<string, List<string>>();
                ValidateStep1(viewModel, validationErrors);
                ValidateStep2(viewModel, validationErrors);

                // Add validation errors to ModelState
                foreach (var error in validationErrors)
                {
                    foreach (var message in error.Value)
                    {
                        ModelState.AddModelError(error.Key, message);
                    }
                }

                // Validate model state
                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("Form validation failed for user {UserId}. Case: {EligibilityCase}, Errors: {ValidationErrors}",
                        userId, viewModel.GetEligibilityCase(), string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));

                    var eligibilityCase = viewModel.GetEligibilityCase();
                    var caseDescription = GetCaseDescription(eligibilityCase);
                    TempData["ErrorMessage"] = $"Please correct the errors below for Case {eligibilityCase} ({caseDescription}) and try again.";
                    return View(viewModel);
                }

                // Check eligibility using the form handler
                if (_formHandler.IsNotEligible(viewModel))
                {
                    var ineligibilityReason = _formHandler.GetIneligibilityReason(viewModel);
                    _logger.LogInformation("User {UserId} is not eligible for the program. Reason: {Reason}",
                        userId, ineligibilityReason);

                    // Store the reason in TempData for the NotEligible view
                    TempData["IneligibilityReason"] = ineligibilityReason;
                    return RedirectToAction("NotEligible");
                }

                // Save or update the form submission (implements single submission rule)
                var entityId = await _formHandler.SaveOrUpdateAsync(viewModel, "KuwaitiStudentInfo", userId);

                _logger.LogInformation("REGULAR POST: Form submission saved/updated successfully for user {UserId}. Entity ID: {EntityId}. " +
                    "Redirecting to Summary page with ID: {EntityId}",
                    userId, entityId, entityId);

                TempData["SuccessMessage"] = "Your application has been submitted successfully!";

                _logger.LogInformation("REGULAR POST: Executing RedirectToAction to Summary with ID: {EntityId}", entityId);
                return RedirectToAction("Summary", new { id = entityId });
            }
            catch (InvalidOperationException ex)
            {
                // Handle business logic errors (e.g., missing required files)
                _logger.LogWarning(ex, "Business logic validation failed for user {UserId}: {ErrorMessage}",
                    userId, ex.Message);

                ModelState.AddModelError("", ex.Message);
                TempData["ErrorMessage"] = ex.Message;
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error processing form submission for user {UserId}", userId);

                ModelState.AddModelError("", "An unexpected error occurred while processing your submission. Please try again.");
                TempData["ErrorMessage"] = "An unexpected error occurred. Please try again or contact support if the problem persists.";
                return View(viewModel);
            }
        }

        /// <summary>
        /// POST: /KuwaitiStudentInfo/SubmitForm
        /// AJAX endpoint for final form submission
        /// </summary>
        /// <param name="viewModel">The complete form data</param>
        /// <returns>JSON response with submission results</returns>
        [HttpPost("SubmitForm")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SubmitForm(StudentFormViewModel viewModel)
        {
            var userId = GetCurrentUserId();
            var userDisplayName = GetCurrentUserDisplayName();

            try
            {
                _logger.LogInformation("AJAX POST: Processing AJAX form submission from user {UserDisplayName} ({UserId}). " +
                    "Eligibility Case: {EligibilityCase}. This indicates the form was submitted via AJAX as expected.",
                    userDisplayName, userId, viewModel.GetEligibilityCase());

                // Phase 5: Enhanced validation with case-specific document requirements
                var validationErrors = new Dictionary<string, List<string>>();
                ValidateStep1(viewModel, validationErrors);
                ValidateStep2(viewModel, validationErrors);

                // Add validation errors to ModelState
                foreach (var error in validationErrors)
                {
                    foreach (var message in error.Value)
                    {
                        ModelState.AddModelError(error.Key, message);
                    }
                }

                // Validate model state
                if (!ModelState.IsValid)
                {
                    _logger.LogWarning("AJAX form validation failed for user {UserId}. Case: {EligibilityCase}, Errors: {ValidationErrors}",
                        userId, viewModel.GetEligibilityCase(), string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)));

                    var errors = ModelState.ToDictionary(
                        kvp => kvp.Key,
                        kvp => kvp.Value?.Errors.Select(e => e.ErrorMessage).ToList() ?? new List<string>()
                    );

                    return Json(new { success = false, errors = errors });
                }

                // Check eligibility using the form handler
                if (_formHandler.IsNotEligible(viewModel))
                {
                    var ineligibilityReason = _formHandler.GetIneligibilityReason(viewModel);
                    _logger.LogInformation("User {UserId} is not eligible for the program. Reason: {Reason}",
                        userId, ineligibilityReason);

                    return Json(new
                    {
                        success = false,
                        isEligible = false,
                        redirectUrl = Url.Action("NotEligible"),
                        message = ineligibilityReason
                    });
                }

                // Save or update the form submission (implements single submission rule)
                var entityId = await _formHandler.SaveOrUpdateAsync(viewModel, "KuwaitiStudentInfo", userId);

                var redirectUrl = Url.Action("Summary", new { id = entityId });
                _logger.LogInformation("AJAX POST: Form submission saved/updated successfully for user {UserId}. Entity ID: {EntityId}. " +
                    "Generated redirect URL: {RedirectUrl}",
                    userId, entityId, redirectUrl);

                return Json(new
                {
                    success = true,
                    entityId = entityId,
                    redirectUrl = redirectUrl,
                    message = "Your application has been submitted successfully!"
                });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Business logic validation failed for user {UserId}: {ErrorMessage}",
                    userId, ex.Message);

                return Json(new { success = false, errors = new { general = new[] { ex.Message } } });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error processing AJAX form submission for user {UserId}", userId);

                return Json(new { success = false, errors = new { general = new[] { "An unexpected error occurred while processing your submission. Please try again." } } });
            }
        }

        #endregion

        #region Step Validation Methods

        /// <summary>
        /// Validates Step 1: Basic Information
        /// </summary>
        private void ValidateStep1(StudentFormViewModel viewModel, Dictionary<string, List<string>> errors)
        {
            if (string.IsNullOrWhiteSpace(viewModel.StudentName))
                AddValidationError(errors, nameof(viewModel.StudentName), "Student name is required.");

            if (string.IsNullOrWhiteSpace(viewModel.StudentCivilId))
                AddValidationError(errors, nameof(viewModel.StudentCivilId), "Student Civil ID is required.");
            else if (viewModel.StudentCivilId.Length != 12 || !viewModel.StudentCivilId.All(char.IsDigit))
                AddValidationError(errors, nameof(viewModel.StudentCivilId), "Student Civil ID must be exactly 12 digits.");

            if (string.IsNullOrWhiteSpace(viewModel.StudentMobileNumber))
                AddValidationError(errors, nameof(viewModel.StudentMobileNumber), "Mobile number is required.");
            else
            {
                var mobileNumber = viewModel.StudentMobileNumber.Trim().Replace(" ", "").Replace("-", "");
                if (!System.Text.RegularExpressions.Regex.IsMatch(mobileNumber, @"^(\+965|965)?[569]\d{7}$"))
                    AddValidationError(errors, nameof(viewModel.StudentMobileNumber), "Please enter a valid Kuwait mobile number (e.g., +965XXXXXXXX or XXXXXXXX starting with 5, 6, or 9).");
            }

            // Parent name validation removed - names are no longer collected
        }

        /// <summary>
        /// Validates Step 2: Document Upload with case-specific requirements
        /// </summary>
        private void ValidateStep2(StudentFormViewModel viewModel, Dictionary<string, List<string>> errors)
        {
            var eligibilityCase = viewModel.GetEligibilityCase();
            var requiredFiles = viewModel.GetRequiredFileUploads();

            // Phase 5: Enhanced case-specific validation
            if (requiredFiles.Count == 0 && eligibilityCase != "D")
            {
                AddValidationError(errors, "general", "Unable to determine required documents for your eligibility case. Please check your nationality selections.");
                return;
            }

            foreach (var fileProperty in requiredFiles)
            {
                var property = typeof(StudentFormViewModel).GetProperty(fileProperty);
                var file = property?.GetValue(viewModel) as IFormFile;

                if (file == null || file.Length == 0)
                {
                    var displayName = GetFileDisplayName(fileProperty);
                    var caseSpecificMessage = GetCaseSpecificErrorMessage(fileProperty, eligibilityCase, displayName);
                    AddValidationError(errors, fileProperty, caseSpecificMessage);
                }
                else
                {
                    // Validate file size and type
                    ValidateFileProperties(file, fileProperty, errors);
                }
            }

            // Add summary message if there are missing documents
            if (errors.Any(e => e.Key != "general"))
            {
                var caseDescription = GetCaseDescription(eligibilityCase);
                AddValidationError(errors, "general",
                    $"Your application is for {caseDescription}. Please upload all required documents for Case {eligibilityCase} before proceeding.");
            }
        }

        /// <summary>
        /// Validates Step 3: Review & Submit (final validation)
        /// </summary>
        private void ValidateStep3(StudentFormViewModel viewModel, Dictionary<string, List<string>> errors)
        {
            // Run all validations for final check
            ValidateStep1(viewModel, errors);
            ValidateStep2(viewModel, errors);
        }

        /// <summary>
        /// Helper method to add validation errors
        /// </summary>
        private void AddValidationError(Dictionary<string, List<string>> errors, string key, string message)
        {
            if (!errors.ContainsKey(key))
                errors[key] = new List<string>();

            errors[key].Add(message);
        }

        /// <summary>
        /// Gets display name for file properties
        /// </summary>
        private static string GetFileDisplayName(string propertyName)
        {
            return propertyName switch
            {
                nameof(StudentFormViewModel.StudentCivilIdFile) => "Student Civil ID Document",
                nameof(StudentFormViewModel.StudentNationalityCertificateFile) => "Student Nationality Certificate",
                nameof(StudentFormViewModel.FatherCivilIdFile) => "Father's Civil ID Document",
                nameof(StudentFormViewModel.FatherNationalityCertificateFile) => "Father's Nationality Certificate",
                nameof(StudentFormViewModel.MotherCivilIdFile) => "Mother's Civil ID Document",
                nameof(StudentFormViewModel.MotherNationalityCertificateFile) => "Mother's Nationality Certificate",
                _ => propertyName
            };
        }

        /// <summary>
        /// Phase 5: Gets case-specific error message for missing documents
        /// </summary>
        private static string GetCaseSpecificErrorMessage(string fileProperty, string eligibilityCase, string displayName)
        {
            var caseContext = eligibilityCase switch
            {
                "A" => "All family members are Kuwaiti citizens",
                "B" => "Father and Student are Kuwaiti citizens",
                "C" => "Only Mother is a Kuwaiti citizen",
                _ => "your eligibility case"
            };

            return fileProperty switch
            {
                nameof(StudentFormViewModel.StudentCivilIdFile) =>
                    $"{displayName} is required for Case {eligibilityCase} ({caseContext}). Please upload the student's Kuwait Civil ID.",

                nameof(StudentFormViewModel.StudentNationalityCertificateFile) =>
                    $"{displayName} is required for Case {eligibilityCase} ({caseContext}). Please upload the student's nationality certificate for verification.",

                nameof(StudentFormViewModel.FatherCivilIdFile) =>
                    $"{displayName} is required for Case {eligibilityCase} ({caseContext}). Please upload the father's Kuwait Civil ID.",

                nameof(StudentFormViewModel.FatherNationalityCertificateFile) =>
                    $"{displayName} is required for Case {eligibilityCase} ({caseContext}). Please upload the father's nationality certificate for verification.",

                nameof(StudentFormViewModel.MotherCivilIdFile) =>
                    $"{displayName} is required for Case {eligibilityCase} ({caseContext}). Please upload the mother's Kuwait Civil ID.",

                nameof(StudentFormViewModel.MotherNationalityCertificateFile) =>
                    $"{displayName} is required for Case {eligibilityCase} ({caseContext}). Please upload the mother's nationality certificate for verification.",

                _ => $"{displayName} is required for Case {eligibilityCase}. Please upload this document to proceed."
            };
        }

        /// <summary>
        /// Phase 5: Validates file properties (size, type, etc.)
        /// </summary>
        private static void ValidateFileProperties(IFormFile file, string fileProperty, Dictionary<string, List<string>> errors)
        {
            var displayName = GetFileDisplayName(fileProperty);

            // Check file size (5MB limit)
            const long maxFileSize = 5 * 1024 * 1024; // 5MB
            if (file.Length > maxFileSize)
            {
                AddValidationErrorStatic(errors, fileProperty, $"{displayName} exceeds the 5MB file size limit. Please upload a smaller file.");
                return;
            }

            // Check file type
            var allowedTypes = new[] { "application/pdf", "image/jpeg", "image/jpg", "image/png" };
            if (!allowedTypes.Contains(file.ContentType.ToLowerInvariant()))
            {
                AddValidationErrorStatic(errors, fileProperty, $"{displayName} must be a PDF, JPG, or PNG file. Please upload a valid file type.");
                return;
            }

            // Check if file is empty
            if (file.Length == 0)
            {
                AddValidationErrorStatic(errors, fileProperty, $"{displayName} appears to be empty. Please upload a valid file.");
            }
        }

        /// <summary>
        /// Static helper method to add validation errors
        /// </summary>
        private static void AddValidationErrorStatic(Dictionary<string, List<string>> errors, string key, string message)
        {
            if (!errors.ContainsKey(key))
                errors[key] = new List<string>();

            errors[key].Add(message);
        }

        #endregion

        #region NotEligible Action

        /// <summary>
        /// GET: /KuwaitiStudentInfo/NotEligible
        /// Displays the ineligibility page for Case D (no Kuwaiti parent/student)
        /// </summary>
        /// <returns>View explaining why the application is not eligible</returns>
        [HttpGet("NotEligible")]
        public IActionResult NotEligible()
        {
            try
            {
                var userId = GetCurrentUserId();
                var userDisplayName = GetCurrentUserDisplayName();

                _logger.LogInformation("User {UserDisplayName} ({UserId}) accessed NotEligible page",
                    userDisplayName, userId);

                // Get the ineligibility reason from TempData if available
                ViewBag.IneligibilityReason = TempData["IneligibilityReason"] as string ??
                    "To be eligible for this program, at least one person (student, father, or mother) must be a Kuwaiti citizen.";

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading NotEligible page for user {UserId}", GetCurrentUserId());
                TempData["ErrorMessage"] = "An error occurred while loading the page. Please try again.";
                return RedirectToAction("Index", "Home");
            }
        }

        #endregion

        #region Summary Action

        /// <summary>
        /// GET: /KuwaitiStudentInfo/Summary/{id}
        /// Displays the summary of a submitted form with download links for uploaded files
        /// </summary>
        /// <param name="id">The ID of the StudentInfo entity to display</param>
        /// <returns>View with the submitted data and file download links</returns>
        [HttpGet("Summary/{id:int}")]
        public async Task<IActionResult> Summary(int id)
        {
            var userId = GetCurrentUserId();
            var userDisplayName = GetCurrentUserDisplayName();

            try
            {
                _logger.LogInformation("User {UserDisplayName} ({UserId}) accessing summary for submission {SubmissionId}",
                    userDisplayName, userId, id);

                // Retrieve the submission with enhanced security checks
                var studentInfo = await _context.StudentInfos
                    .Include(si => si.SisStudent) // Include SIS data for complete information
                    .FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);

                if (studentInfo == null)
                {
                    _logger.LogWarning("Submission {SubmissionId} not found for user {UserId}", id, userId);
                    TempData["ErrorMessage"] = "The requested submission was not found.";
                    return RedirectToAction("Index", "Home");
                }

                // Critical Security Check: Ensure user can only access their own submissions
                if (studentInfo.CreatedByUserId != userId)
                {
                    _logger.LogWarning("SECURITY VIOLATION: User {UserId} attempted to access submission {SubmissionId} belonging to user {OwnerUserId}. " +
                        "This could be an attempt to access unauthorized data by URL manipulation.",
                        userId, id, studentInfo.CreatedByUserId);

                    TempData["ErrorMessage"] = "You are not authorized to view this submission.";
                    return RedirectToAction("Index", "Home");
                }

                // Map entity to ViewModel for display
                var viewModel = _formHandler.MapToViewModel(studentInfo);
                var eligibilityCase = studentInfo.GetEligibilityCase();

                // Add additional data for the view
                ViewBag.SubmissionId = id;
                ViewBag.SubmissionDate = studentInfo.CreatedDate;
                ViewBag.EligibilityCase = eligibilityCase;
                ViewBag.SubmissionGuid = studentInfo.SubmissionGuid;

                // Phase 4: Enhanced summary data for case-specific display
                ViewBag.CaseDescription = GetCaseDescription(eligibilityCase);
                ViewBag.CaseBadgeClass = GetCaseBadgeClass(eligibilityCase);
                ViewBag.RequiredDocuments = GetRequiredDocumentsList(viewModel);

                // Get uploaded documents and add debugging
                var uploadedDocuments = GetUploadedDocumentsList(studentInfo);

                // Convert to format expected by view (backward compatibility)
                var documentsForView = uploadedDocuments.Select(d => (d.Name, d.IsUploaded, d.FilePath, d.FileType)).ToList();
                ViewBag.UploadedDocuments = documentsForView;

                // Debug logging for document paths
                _logger.LogInformation("Summary Debug - StudentInfo paths: StudentCivilId={StudentCivilId}, StudentNationality={StudentNationality}, " +
                    "StudentBirth={StudentBirth}, FatherCivil={FatherCivil}, FatherNationality={FatherNationality}, FatherDeath={FatherDeath}, " +
                    "MotherCivil={MotherCivil}, MotherNationality={MotherNationality}, MotherDeath={MotherDeath}",
                    studentInfo.StudentCivilIdPath, studentInfo.StudentNationalityCertificatePath, studentInfo.StudentBirthCertificatePath,
                    studentInfo.FatherCivilIdPath, studentInfo.FatherNationalityCertificatePath, studentInfo.FatherDeathCertificatePath,
                    studentInfo.MotherCivilIdPath, studentInfo.MotherNationalityCertificatePath, studentInfo.MotherDeathCertificatePath);

                _logger.LogInformation("Summary Debug - Generated {DocumentCount} documents for display", uploadedDocuments.Count);
                var uploadedCount = uploadedDocuments.Count(d => d.IsUploaded);
                _logger.LogInformation("Summary Debug - {UploadedCount} documents are uploaded out of {TotalCount}", uploadedCount, uploadedDocuments.Count);

                foreach (var doc in uploadedDocuments)
                {
                    _logger.LogInformation("Summary Debug - Document: {Name}, Uploaded: {IsUploaded}, Path: {FilePath}",
                        doc.Name, doc.IsUploaded, doc.FilePath);
                }

                _logger.LogInformation("Successfully loaded summary for submission {SubmissionId} for user {UserId}",
                    id, userId);

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading summary for submission {SubmissionId} for user {UserId}", id, userId);
                TempData["ErrorMessage"] = "An error occurred while loading the submission summary. Please try again.";
                return RedirectToAction("Index", "Home");
            }
        }

        #endregion

        #region File Download Action

        /// <summary>
        /// GET: /KuwaitiStudentInfo/DownloadFile/{id}/{fileType}
        /// Provides secure file download functionality for uploaded documents
        /// </summary>
        /// <param name="id">The ID of the StudentInfo entity</param>
        /// <param name="fileType">The type of file to download (e.g., "StudentCivilId", "FatherNationalityCertificate")</param>
        /// <returns>File download or error message</returns>
        [HttpGet("DownloadFile/{id:int}/{fileType}")]
        public async Task<IActionResult> DownloadFile(int id, string fileType)
        {
            var userId = GetCurrentUserId();
            var userDisplayName = GetCurrentUserDisplayName();

            try
            {
                _logger.LogInformation("User {UserDisplayName} ({UserId}) requesting file download. " +
                    "Submission: {SubmissionId}, FileType: {FileType}",
                    userDisplayName, userId, id, fileType);

                // Retrieve the submission from the database with security checks
                var studentInfo = await _context.StudentInfos
                    .FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);

                if (studentInfo == null)
                {
                    _logger.LogWarning("Submission {SubmissionId} not found for file download request by user {UserId}",
                        id, userId);
                    return NotFound("Submission not found.");
                }

                // Critical Security Check: Ensure user can only download their own files
                if (studentInfo.CreatedByUserId != userId)
                {
                    _logger.LogWarning("SECURITY VIOLATION: User {UserId} attempted to download file from submission {SubmissionId} belonging to user {OwnerUserId}. " +
                        "FileType: {FileType}. This could be an attempt to access unauthorized files by URL manipulation.",
                        userId, id, studentInfo.CreatedByUserId, fileType);

                    return Forbid("You are not authorized to download this file.");
                }

                // Get the file path based on the file type
                string? filePath = GetFilePathByType(studentInfo, fileType);

                if (string.IsNullOrEmpty(filePath))
                {
                    _logger.LogWarning("File type {FileType} not found or empty for submission {SubmissionId}",
                        fileType, id);
                    return NotFound("File not found.");
                }

                // Construct the full file path
                var fullPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", filePath.TrimStart('/'));

                if (!System.IO.File.Exists(fullPath))
                {
                    _logger.LogWarning("Physical file not found at path {FilePath} for submission {SubmissionId}",
                        fullPath, id);
                    return NotFound("File not found on disk.");
                }

                // Get file info for proper content type and name
                var fileInfo = new FileInfo(fullPath);
                var contentType = GetContentType(fileInfo.Extension);
                var fileName = $"{fileType}_{studentInfo.SubmissionGuid}{fileInfo.Extension}";

                _logger.LogInformation("File download successful for user {UserId}. File: {FileName}",
                    userId, fileName);

                // Return the file
                var fileBytes = await System.IO.File.ReadAllBytesAsync(fullPath);
                return File(fileBytes, contentType, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading file {FileType} for submission {SubmissionId} for user {UserId}",
                    fileType, id, userId);
                return StatusCode(500, "An error occurred while downloading the file.");
            }
        }

        #endregion

        #region File Helper Methods

        /// <summary>
        /// Gets the file path for a specific file type from the StudentInfo entity
        /// </summary>
        /// <param name="studentInfo">The StudentInfo entity</param>
        /// <param name="fileType">The type of file to retrieve</param>
        /// <returns>The file path or null if not found</returns>
        private static string? GetFilePathByType(StudentInfo studentInfo, string fileType)
        {
            return fileType switch
            {
                "StudentCivilId" => studentInfo.StudentCivilIdPath,
                "StudentNationalityCertificate" => studentInfo.StudentNationalityCertificatePath,
                "StudentBirthCertificate" => studentInfo.StudentBirthCertificatePath,
                "FatherCivilId" => studentInfo.FatherCivilIdPath,
                "FatherNationalityCertificate" => studentInfo.FatherNationalityCertificatePath,
                "FatherDeathCertificate" => studentInfo.FatherDeathCertificatePath,
                "MotherCivilId" => studentInfo.MotherCivilIdPath,
                "MotherNationalityCertificate" => studentInfo.MotherNationalityCertificatePath,
                "MotherDeathCertificate" => studentInfo.MotherDeathCertificatePath,
                _ => null
            };
        }

        /// <summary>
        /// Gets the appropriate MIME content type for a file extension
        /// </summary>
        /// <param name="extension">The file extension (including the dot)</param>
        /// <returns>The MIME content type</returns>
        private static string GetContentType(string extension)
        {
            return extension.ToLowerInvariant() switch
            {
                ".pdf" => "application/pdf",
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".tiff" or ".tif" => "image/tiff",
                _ => "application/octet-stream"
            };
        }

        #endregion

        #region Phase 4: Summary Helper Methods

        /// <summary>
        /// Gets a detailed description of the eligibility case for display in the summary
        /// </summary>
        /// <param name="eligibilityCase">The eligibility case (A, B, C, or D)</param>
        /// <returns>Detailed description of the eligibility case</returns>
        private static string GetCaseDescription(string eligibilityCase)
        {
            return eligibilityCase switch
            {
                "A" => "All family members are Kuwaiti citizens - Student, Father, and Mother",
                "B" => "Father and Student are Kuwaiti citizens - Mother is not Kuwaiti",
                "C" => "Only Mother is a Kuwaiti citizen - Student and Father are not Kuwaiti",
                "D" => "No family members are Kuwaiti citizens - Application ineligible",
                _ => "Unknown eligibility case"
            };
        }

        /// <summary>
        /// Gets the CSS badge class for styling the eligibility case display
        /// </summary>
        /// <param name="eligibilityCase">The eligibility case (A, B, C, or D)</param>
        /// <returns>CSS class string for badge styling</returns>
        private static string GetCaseBadgeClass(string eligibilityCase)
        {
            return eligibilityCase switch
            {
                "A" => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
                "B" => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
                "C" => "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
                "D" => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
                _ => "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
            };
        }

        /// <summary>
        /// Gets a list of required documents with human-readable names for the specific case
        /// </summary>
        /// <param name="viewModel">The form ViewModel</param>
        /// <returns>List of required document names</returns>
        private static List<string> GetRequiredDocumentsList(StudentFormViewModel viewModel)
        {
            var requiredFiles = viewModel.GetRequiredFileUploads();
            var documentNames = new List<string>();

            foreach (var fileProperty in requiredFiles)
            {
                var displayName = fileProperty switch
                {
                    nameof(StudentFormViewModel.StudentCivilIdFile) => "Student Civil ID Document",
                    nameof(StudentFormViewModel.StudentNationalityCertificateFile) => "Student Nationality Certificate",
                    nameof(StudentFormViewModel.StudentBirthCertificateFile) => "Student Birth Certificate",
                    nameof(StudentFormViewModel.FatherCivilIdFile) => "Father's Civil ID Document",
                    nameof(StudentFormViewModel.FatherNationalityCertificateFile) => "Father's Nationality Certificate",
                    nameof(StudentFormViewModel.FatherDeathCertificateFile) => "Father's Death Certificate",
                    nameof(StudentFormViewModel.MotherCivilIdFile) => "Mother's Civil ID Document",
                    nameof(StudentFormViewModel.MotherNationalityCertificateFile) => "Mother's Nationality Certificate",
                    nameof(StudentFormViewModel.MotherDeathCertificateFile) => "Mother's Death Certificate",
                    _ => fileProperty
                };
                documentNames.Add(displayName);
            }

            return documentNames;
        }

        /// <summary>
        /// Gets a list of all available documents with their upload status for display in the summary
        /// Shows both uploaded and missing documents to give complete overview
        /// </summary>
        /// <param name="studentInfo">The StudentInfo entity</param>
        /// <returns>List of all document information with upload status</returns>
        private static List<(string Name, bool IsUploaded, string? FilePath, string FileType)> GetUploadedDocumentsList(StudentInfo studentInfo)
        {
            var documents = new List<(string Name, bool IsUploaded, string? FilePath, string FileType)>
            {
                ("Student Civil ID Document", !string.IsNullOrEmpty(studentInfo.StudentCivilIdPath), studentInfo.StudentCivilIdPath, "StudentCivilId"),
                ("Student Nationality Certificate", !string.IsNullOrEmpty(studentInfo.StudentNationalityCertificatePath), studentInfo.StudentNationalityCertificatePath, "StudentNationalityCertificate"),
                ("Student Birth Certificate", !string.IsNullOrEmpty(studentInfo.StudentBirthCertificatePath), studentInfo.StudentBirthCertificatePath, "StudentBirthCertificate"),
                ("Father's Civil ID Document", !string.IsNullOrEmpty(studentInfo.FatherCivilIdPath), studentInfo.FatherCivilIdPath, "FatherCivilId"),
                ("Father's Nationality Certificate", !string.IsNullOrEmpty(studentInfo.FatherNationalityCertificatePath), studentInfo.FatherNationalityCertificatePath, "FatherNationalityCertificate"),
                ("Father's Death Certificate", !string.IsNullOrEmpty(studentInfo.FatherDeathCertificatePath), studentInfo.FatherDeathCertificatePath, "FatherDeathCertificate"),
                ("Mother's Civil ID Document", !string.IsNullOrEmpty(studentInfo.MotherCivilIdPath), studentInfo.MotherCivilIdPath, "MotherCivilId"),
                ("Mother's Nationality Certificate", !string.IsNullOrEmpty(studentInfo.MotherNationalityCertificatePath), studentInfo.MotherNationalityCertificatePath, "MotherNationalityCertificate"),
                ("Mother's Death Certificate", !string.IsNullOrEmpty(studentInfo.MotherDeathCertificatePath), studentInfo.MotherDeathCertificatePath, "MotherDeathCertificate")
            };

            // Return ALL documents (both uploaded and missing) for complete overview
            return documents;
        }

        #endregion

        #region SIS Lookup API Endpoints

        /// <summary>
        /// POST: /KuwaitiStudentInfo/LookupStudent
        /// AJAX endpoint for looking up student information by Civil ID
        /// </summary>
        /// <param name="civilId">Civil ID to lookup</param>
        /// <returns>JSON response with student data or error message</returns>
        [HttpPost("LookupStudent")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> LookupStudent([FromForm] string civilId)
        {
            var userId = GetCurrentUserId();

            try
            {
                if (string.IsNullOrWhiteSpace(civilId))
                {
                    return Json(new { success = false, message = "Civil ID is required" });
                }

                // Validate Civil ID format (12 digits)
                if (civilId.Length != 12 || !civilId.All(char.IsDigit))
                {
                    return Json(new { success = false, message = "Civil ID must be exactly 12 digits" });
                }

                _logger.LogInformation("Student lookup requested by user {UserId} for Civil ID: {CivilId}",
                    userId, civilId);

                var student = await _studentLookupService.GetStudentByNationalIdAsync(civilId);

                if (student == null)
                {
                    _logger.LogInformation("No student found for Civil ID: {CivilId}", civilId);
                    return Json(new
                    {
                        success = false,
                        message = "No student found with this Civil ID in our records"
                    });
                }

                // Return student data for pre-filling
                var studentData = new
                {
                    studentId = student.StudentID,
                    fullNameEN = student.FullNameEN,
                    fullNameAR = student.FullNameAR,
                    nationalId = student.NationalID,
                    email = student.Email,
                    nationality = student.Nationality,
                    isKuwaiti = string.Equals(student.Nationality, "KUWAIT", StringComparison.OrdinalIgnoreCase) ||
                               string.Equals(student.Nationality, "Kuwaiti", StringComparison.OrdinalIgnoreCase),
                    enrollmentStatus = student.EnrollmentStatus,
                    major = student.Major,
                    level = student.Level,
                    lastSyncDate = student.LastSyncDate
                };

                _logger.LogInformation("Student found for Civil ID: {CivilId}, StudentID: {StudentId}",
                    civilId, student.StudentID);

                return Json(new
                {
                    success = true,
                    message = "Student found successfully",
                    student = studentData
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error looking up student by Civil ID: {CivilId}", civilId);
                return Json(new
                {
                    success = false,
                    message = "An error occurred while looking up the student. Please try again."
                });
            }
        }

        /// <summary>
        /// GET: /KuwaitiStudentInfo/GetDataFreshness
        /// AJAX endpoint for checking SIS data freshness
        /// </summary>
        /// <returns>JSON response with data freshness information</returns>
        [HttpGet("GetDataFreshness")]
        public async Task<IActionResult> GetDataFreshness()
        {
            try
            {
                var stats = await _studentLookupService.GetDataFreshnessStatsAsync();

                return Json(new
                {
                    success = true,
                    totalStudents = stats.TotalStudents,
                    lastSyncDate = stats.LastSyncDate,
                    dataAge = stats.DataAge?.TotalHours,
                    freshDataPercentage = stats.FreshDataPercentage,
                    isDataStale = stats.DataAge?.TotalHours > 24 // Consider stale if older than 24 hours
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting data freshness stats");
                return Json(new
                {
                    success = false,
                    message = "Unable to check data freshness"
                });
            }
        }

        #endregion
    }
}
