using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Forms.ktech.Models
{
    /// <summary>
    /// Entity for tracking email notifications sent to students
    /// </summary>
    public class EmailNotification
    {
        /// <summary>
        /// Primary key for the email notification record
        /// </summary>
        [Key]
        public int Id { get; set; }
        
        /// <summary>
        /// Foreign key reference to the submission
        /// </summary>
        [Required]
        public int SubmissionId { get; set; }
        
        /// <summary>
        /// Type of email notification (e.g., DocumentDisapproval, SubmissionApproved)
        /// </summary>
        [Required]
        [StringLength(50)]
        public string EmailType { get; set; } = string.Empty;
        
        /// <summary>
        /// Email address of the recipient
        /// </summary>
        [Required]
        [StringLength(255)]
        [EmailAddress]
        public string RecipientEmail { get; set; } = string.Empty;
        
        /// <summary>
        /// Subject line of the email
        /// </summary>
        [Required]
        [StringLength(500)]
        public string Subject { get; set; } = string.Empty;
        
        /// <summary>
        /// HTML body content of the email
        /// </summary>
        [Required]
        public string Body { get; set; } = string.Empty;
        
        /// <summary>
        /// Current status of the email delivery
        /// </summary>
        [Required]
        public EmailStatus Status { get; set; } = EmailStatus.Pending;
        
        /// <summary>
        /// Date and time when the email was successfully sent
        /// </summary>
        public DateTime? SentDate { get; set; }
        
        /// <summary>
        /// Reason for delivery failure (if applicable)
        /// </summary>
        [StringLength(1000)]
        public string? FailureReason { get; set; }
        
        /// <summary>
        /// Number of retry attempts made for failed deliveries
        /// </summary>
        public int RetryCount { get; set; } = 0;
        
        /// <summary>
        /// Date and time when this email notification record was created
        /// </summary>
        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// Date and time when this email notification record was last updated
        /// </summary>
        public DateTime? UpdatedDate { get; set; }
        
        // Navigation Properties
        
        /// <summary>
        /// Navigation property to the related submission
        /// </summary>
        [ForeignKey(nameof(SubmissionId))]
        public virtual Features.KuwaitiStudentInfo.Models.StudentInfo Submission { get; set; } = null!;
        
        /// <summary>
        /// Marks the email notification as sent
        /// </summary>
        public void MarkAsSent()
        {
            Status = EmailStatus.Sent;
            SentDate = DateTime.UtcNow;
            UpdatedDate = DateTime.UtcNow;
        }
        
        /// <summary>
        /// Marks the email notification as failed with a reason
        /// </summary>
        /// <param name="reason">The reason for the failure</param>
        public void MarkAsFailed(string reason)
        {
            Status = EmailStatus.Failed;
            FailureReason = reason;
            UpdatedDate = DateTime.UtcNow;
        }
        
        /// <summary>
        /// Increments the retry count and marks as retrying
        /// </summary>
        public void IncrementRetry()
        {
            RetryCount++;
            Status = EmailStatus.Retrying;
            UpdatedDate = DateTime.UtcNow;
        }
        
        /// <summary>
        /// Gets the status display text in bilingual format
        /// </summary>
        public string GetStatusDisplayText()
        {
            return Status switch
            {
                EmailStatus.Pending => "Pending / في الانتظار",
                EmailStatus.Sent => "Sent / تم الإرسال",
                EmailStatus.Failed => "Failed / فشل",
                EmailStatus.Retrying => "Retrying / إعادة المحاولة",
                _ => "Unknown / غير معروف"
            };
        }
        
        /// <summary>
        /// Gets the CSS class for status badge styling
        /// </summary>
        public string GetStatusBadgeClass()
        {
            return Status switch
            {
                EmailStatus.Pending => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
                EmailStatus.Sent => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
                EmailStatus.Failed => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
                EmailStatus.Retrying => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
                _ => "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
            };
        }
        
        /// <summary>
        /// Gets the icon class for status display
        /// </summary>
        public string GetStatusIconClass()
        {
            return Status switch
            {
                EmailStatus.Pending => "fas fa-clock",
                EmailStatus.Sent => "fas fa-check-circle",
                EmailStatus.Failed => "fas fa-exclamation-triangle",
                EmailStatus.Retrying => "fas fa-redo",
                _ => "fas fa-question-circle"
            };
        }
        
        /// <summary>
        /// Checks if the email can be retried (not exceeded max retry attempts)
        /// </summary>
        /// <param name="maxRetries">Maximum number of retry attempts allowed</param>
        /// <returns>True if the email can be retried</returns>
        public bool CanRetry(int maxRetries = 3)
        {
            return Status == EmailStatus.Failed && RetryCount < maxRetries;
        }
        
        /// <summary>
        /// Gets the email type display name in bilingual format
        /// </summary>
        public string GetEmailTypeDisplayName()
        {
            return EmailType switch
            {
                "DocumentDisapproval" => "Document Disapproval / رفض المستند",
                "SubmissionApproved" => "Submission Approved / الموافقة على الطلب",
                "SubmissionRejected" => "Submission Rejected / رفض الطلب",
                "DocumentApproved" => "Document Approved / الموافقة على المستند",
                "ReminderNotification" => "Reminder Notification / تذكير",
                _ => "General Notification / إشعار عام"
            };
        }
    }
    
    /// <summary>
    /// Enumeration for email delivery status
    /// </summary>
    public enum EmailStatus
    {
        /// <summary>
        /// Email is queued for delivery
        /// </summary>
        Pending = 0,
        
        /// <summary>
        /// Email has been successfully sent
        /// </summary>
        Sent = 1,
        
        /// <summary>
        /// Email delivery failed
        /// </summary>
        Failed = 2,
        
        /// <summary>
        /// Email is being retried after a failure
        /// </summary>
        Retrying = 3,

        /// <summary>
        /// Email is saved as draft for preview/editing
        /// </summary>
        Draft = 4
    }
}
