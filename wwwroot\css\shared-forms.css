/* 
 * Shared Forms CSS - forms.ktech
 * Common styles used across multiple forms for consistency and DRY principles
 */

/* ===== CARD HOVER EFFECTS ===== */
.card:hover {
    transform: translateY(-2px);
    transition: transform 0.3s ease;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* ===== TIMELINE COMPONENT ===== */
.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: -2rem;
    top: 0;
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid white;
    box-shadow: 0 0 0 3px #e9ecef;
}

.timeline-content {
    padding-left: 1rem;
}

/* ===== LOADING ANIMATIONS ===== */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.overlay-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.loading-content {
    position: relative;
    background: white;
    padding: 2rem;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.downloading {
    opacity: 0.7;
    pointer-events: none;
}

.downloading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== FORM VALIDATION STATES ===== */
.is-invalid {
    border-color: #dc3545 !important;
}

.is-valid {
    border-color: #198754 !important;
}

/* ===== FILE UPLOAD COMPONENTS ===== */
.file-drop-zone {
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-drop-zone:hover {
    border-color: #0d6efd !important;
    background-color: #f8f9fa;
}

.file-drop-zone.drag-over {
    border-color: #0d6efd !important;
    background-color: #e7f3ff;
    transform: scale(1.02);
}

/* ===== CONDITIONAL FIELDS ===== */
.conditional-field {
    transition: all 0.3s ease;
}

/* ===== BADGE STYLING ===== */
.badge {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
a:focus, button:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* ===== PRINT STYLES ===== */
@media print {
    .btn, .alert, .card-header {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
        break-inside: avoid;
        margin-bottom: 1rem !important;
    }
    
    .timeline::before {
        background: #000 !important;
    }
    
    .timeline-marker {
        border-color: #000 !important;
        box-shadow: none !important;
    }
    
    .text-primary, .text-info, .text-success {
        color: #000 !important;
    }
    
    .bg-light {
        background-color: #f8f9fa !important;
    }
    
    /* Add page breaks */
    .card:nth-child(3) {
        page-break-before: always;
    }
}

/* ===== PDF EXPORT STYLES ===== */
.pdf-export .btn,
.pdf-export .alert {
    display: none !important;
}

/* ===== MOBILE RESPONSIVENESS ===== */
@media (max-width: 768px) {
    .timeline {
        padding-left: 1.5rem;
    }
    
    .timeline-marker {
        left: -1.5rem;
        width: 1.5rem;
        height: 1.5rem;
    }
    
    .timeline::before {
        left: 0.75rem;
    }
    
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
    
    .card-body {
        padding: 1.5rem 1rem;
    }
    
    .card-header h5 {
        font-size: 1rem;
    }
}

/* ===== ANIMATION CLASSES ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate__fadeInUp {
    animation: fadeInUp 0.6s ease-out;
}

/* ===== HOVER EFFECTS ===== */
.btn:hover {
    transform: translateY(-1px);
    transition: transform 0.2s ease;
}

/* ===== ICON STYLING ===== */
.rounded-circle {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ===== SIS INTEGRATION STYLING ===== */
/* Phase 5: SIS Pre-filled Field Indicators */

/* SIS Badge Styling */
.sis-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 0.375rem;
    background-color: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
    margin-left: 0.5rem;
}

.sis-badge i {
    margin-right: 0.25rem;
    font-size: 0.625rem;
}

/* Read-only Field Styling */
.sis-readonly {
    background-color: #f8fafc !important;
    border-color: #cbd5e1 !important;
    color: #64748b !important;
    cursor: not-allowed;
    position: relative;
}

.sis-readonly:focus {
    border-color: #cbd5e1 !important;
    box-shadow: 0 0 0 3px rgba(203, 213, 225, 0.1) !important;
}

/* Dark mode support for SIS styling */
.dark .sis-badge {
    background-color: #1e3a8a;
    color: #bfdbfe;
    border-color: #3b82f6;
}

.dark .sis-readonly {
    background-color: #374151 !important;
    border-color: #4b5563 !important;
    color: #9ca3af !important;
}

/* SIS Field Container */
.sis-field-container {
    position: relative;
}

.sis-field-container .sis-indicator {
    position: absolute;
    top: 0;
    right: 0;
    transform: translateY(-50%);
    z-index: 10;
}

/* Data Freshness Indicator */
.data-freshness {
    display: inline-flex;
    align-items: center;
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 0.25rem;
}

.data-freshness.fresh {
    color: #059669;
}

.data-freshness.stale {
    color: #d97706;
}

.data-freshness.very-stale {
    color: #dc2626;
}

.data-freshness i {
    margin-right: 0.25rem;
    font-size: 0.625rem;
}

/* SIS Pre-fill Alert Styling */
.sis-prefill-alert {
    background-color: #eff6ff;
    border-color: #3b82f6;
    color: #1e40af;
}

.dark .sis-prefill-alert {
    background-color: #1e3a8a;
    border-color: #3b82f6;
    color: #bfdbfe;
}

/* Loading State for SIS Operations */
.sis-loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.sis-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 0.75rem;
    width: 1rem;
    height: 1rem;
    margin-top: -0.5rem;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Accessibility improvements for SIS fields */
.sis-readonly[aria-describedby] {
    outline: none;
}

.sis-readonly:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Mobile responsiveness for SIS indicators */
@media (max-width: 768px) {
    .sis-badge {
        font-size: 0.625rem;
        padding: 0.125rem 0.375rem;
        margin-left: 0.25rem;
    }

    .sis-field-container .sis-indicator {
        position: static;
        transform: none;
        margin-top: 0.25rem;
        display: block;
    }

    .data-freshness {
        font-size: 0.625rem;
    }
}
