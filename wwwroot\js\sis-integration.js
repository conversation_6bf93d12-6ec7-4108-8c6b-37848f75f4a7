/**
 * SIS Integration JavaScript - forms.ktech
 * Phase 5: Specialized JavaScript for SIS integration features
 * Handles student lookup, data pre-filling, and real-time SIS operations
 */

// ===== SIS INTEGRATION NAMESPACE =====
window.SisIntegration = window.SisIntegration || {};

// ===== STUDENT LOOKUP FUNCTIONALITY =====
SisIntegration.StudentLookup = {
    /**
     * Performs student lookup by Civil ID
     * @param {string} civilId - The Civil ID to lookup
     * @param {Function} onSuccess - Success callback
     * @param {Function} onError - Error callback
     */
    lookupByCivilId: function(civilId, onSuccess, onError) {
        if (!civilId || civilId.length !== 12) {
            if (onError) onError('Civil ID must be exactly 12 digits');
            return;
        }

        // Show loading state
        SharedForms.SisIntegration.showLoadingState('StudentCivilId');
        
        $.ajax({
            url: '/KuwaitiStudentInfo/LookupStudentByCivilId',
            method: 'POST',
            data: { civilId: civilId },
            headers: {
                'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
            },
            success: function(response) {
                SharedForms.SisIntegration.hideLoadingState('StudentCivilId');
                
                if (response.success && response.student) {
                    if (onSuccess) onSuccess(response.student);
                    SharedForms.Toast.show('Student information found and loaded!', 'success');
                } else {
                    if (onError) onError(response.message || 'Student not found');
                }
            },
            error: function(xhr, status, error) {
                SharedForms.SisIntegration.hideLoadingState('StudentCivilId');
                console.error('Student lookup error:', error);
                if (onError) onError('Error occurred during student lookup');
            }
        });
    },

    /**
     * Pre-fills form fields with student data
     * @param {Object} studentData - Student data from SIS
     */
    preFillForm: function(studentData) {
        if (!studentData) return;

        // Pre-fill student name
        if (studentData.fullNameEN) {
            $('#StudentName').val(studentData.fullNameEN);
            this.markFieldAsPreFilled('StudentName');
        }

        // Pre-fill civil ID
        if (studentData.nationalId) {
            $('#StudentCivilId').val(studentData.nationalId);
            this.markFieldAsPreFilled('StudentCivilId');
        }

        // Pre-fill nationality status
        if (typeof studentData.isKuwaiti === 'boolean') {
            $('#studentKuwaiti').prop('checked', studentData.isKuwaiti);
            this.markFieldAsPreFilled('StudentIsKuwaiti');
        }

        // Show pre-fill alert
        this.showPreFillAlert(studentData);
    },

    /**
     * Marks a field as pre-filled from SIS
     * @param {string} fieldName - Name of the field
     */
    markFieldAsPreFilled: function(fieldName) {
        const field = $(`#${fieldName}, [name="${fieldName}"]`);
        const container = field.closest('div');
        
        // Add SIS badge if not already present
        if (!container.find('.sis-badge').length) {
            const badge = $(`
                <span class="sis-badge" role="img" aria-label="Pre-filled from Student Information System">
                    <i class="fas fa-database" aria-hidden="true"></i>
                    From SIS
                </span>
            `);
            field.after(badge);
        }

        // Make field read-only
        field.addClass('sis-readonly').prop('readonly', true);
        
        // Add accessibility info
        const infoId = `${fieldName}-sis-info`;
        if (!$(`#${infoId}`).length) {
            const info = $(`
                <div id="${infoId}" class="sr-only">
                    This field has been automatically filled from the Student Information System and cannot be edited.
                </div>
            `);
            field.after(info);
            field.attr('aria-describedby', infoId);
        }
    },

    /**
     * Shows the pre-fill alert with student information
     * @param {Object} studentData - Student data from SIS
     */
    showPreFillAlert: function(studentData) {
        const alertHtml = `
            <div class="sis-prefill-alert flex items-start p-4 mb-6 text-sm border rounded-lg" role="alert">
                <svg class="flex-shrink-0 inline w-4 h-4 me-3 mt-0.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                </svg>
                <div>
                    <span class="font-medium">Information Pre-filled from Student Records</span>
                    <div class="mt-1 text-sm">
                        Student information has been automatically loaded from the Student Information System.
                        <div class="data-freshness fresh mt-2">
                            <i class="fas fa-check-circle" aria-hidden="true"></i>
                            Data retrieved ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Remove existing alert and add new one
        $('.sis-prefill-alert').remove();
        $('#validation-summary').before(alertHtml);
    }
};

// ===== REAL-TIME SIS OPERATIONS =====
SisIntegration.RealTime = {
    /**
     * Checks data freshness and shows warnings if needed
     * @param {Date} lastSyncDate - When data was last synchronized
     */
    checkDataFreshness: function(lastSyncDate) {
        if (!lastSyncDate) return;

        const now = new Date();
        const syncDate = new Date(lastSyncDate);
        const hoursDiff = (now - syncDate) / (1000 * 60 * 60);
        
        if (hoursDiff > 48) { // More than 2 days old
            this.showDataFreshnessWarning('very-stale', 'Student data may be outdated. Please contact support if information seems incorrect.');
        } else if (hoursDiff > 24) { // More than 1 day old
            this.showDataFreshnessWarning('stale', 'Student data is somewhat old but should still be accurate.');
        }
    },

    /**
     * Shows data freshness warning
     * @param {string} level - Warning level (stale, very-stale)
     * @param {string} message - Warning message
     */
    showDataFreshnessWarning: function(level, message) {
        const warningClass = level === 'very-stale' ? 'text-red-600' : 'text-yellow-600';
        const iconClass = level === 'very-stale' ? 'fas fa-exclamation-triangle' : 'fas fa-clock';
        
        const warning = $(`
            <div class="data-freshness ${level} mt-2">
                <i class="${iconClass}" aria-hidden="true"></i>
                ${message}
            </div>
        `);
        
        $('.sis-prefill-alert').append(warning);
    },

    /**
     * Monitors form for changes to SIS fields
     */
    monitorSisFields: function() {
        $('.sis-readonly').on('input change', function() {
            SharedForms.Toast.show('This field contains verified information and cannot be modified.', 'warning', 3000);
            // Restore original value if available
            const originalValue = $(this).data('original-value');
            if (originalValue) {
                $(this).val(originalValue);
            }
        });
    }
};

// ===== INITIALIZATION =====
SisIntegration.init = function() {
    // Initialize real-time monitoring
    this.RealTime.monitorSisFields();
    
    // Set up Civil ID lookup on blur
    $('#StudentCivilId').on('blur', function() {
        const civilId = $(this).val().trim();
        if (civilId.length === 12 && !$(this).hasClass('sis-readonly')) {
            // Only lookup if field is not already pre-filled
            SisIntegration.StudentLookup.lookupByCivilId(
                civilId,
                function(studentData) {
                    SisIntegration.StudentLookup.preFillForm(studentData);
                },
                function(error) {
                    console.log('Student lookup failed:', error);
                    // Don't show error toast for manual entry
                }
            );
        }
    });
    
    // Store original values for SIS fields
    $('.sis-readonly').each(function() {
        $(this).data('original-value', $(this).val());
    });
};

// ===== AUTO-INITIALIZATION =====
$(document).ready(function() {
    // Initialize SIS integration if on the correct page
    const currentPath = window.location.pathname.toLowerCase();
    if (currentPath.includes('collectinfo') || currentPath.includes('kuwaiti')) {
        SisIntegration.init();
    }
});
