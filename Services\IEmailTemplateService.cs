using Forms.ktech.ViewModels.Admin;

namespace Forms.ktech.Services
{
    /// <summary>
    /// Interface for email template integration and processing operations
    /// </summary>
    public interface IEmailTemplateService
    {
        /// <summary>
        /// Generates email content using the existing KTECH template with dynamic content injection
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="disapprovedDocuments">List of disapproved documents</param>
        /// <returns>Complete HTML email content</returns>
        Task<string> GenerateEmailContentAsync(int submissionId, List<DisapprovedDocumentInfo> disapprovedDocuments);

        /// <summary>
        /// Loads the email template from the file system
        /// </summary>
        /// <returns>HTML template content</returns>
        Task<string> LoadEmailTemplateAsync();

        /// <summary>
        /// Generates email content with custom plain text content
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="customSubject">Custom email subject</param>
        /// <param name="customBody">Custom plain text body content</param>
        /// <returns>Complete HTML email content</returns>
        Task<string> GenerateCustomEmailContentAsync(int submissionId, string customSubject, string customBody);
    }
}
