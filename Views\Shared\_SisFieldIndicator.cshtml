@*
    SIS Field Indicator Partial View
    Phase 5: Reusable component for displaying SIS pre-fill indicators
    
    Parameters:
    - fieldName: The name of the field
    - isPreFilled: Whether the field was pre-filled from SIS
    - isReadOnly: Whether the field is read-only due to SIS data
    - showBadge: Whether to show the "From SIS" badge (default: true)
    - badgeText: Custom text for the badge (default: "From SIS")
*@

@{
    var fieldName = ViewData["fieldName"] as string ?? "";
    var isPreFilled = (bool)(ViewData["isPreFilled"] ?? false);
    var isReadOnly = (bool)(ViewData["isReadOnly"] ?? false);
    var showBadge = (bool)(ViewData["showBadge"] ?? true);
    var badgeText = ViewData["badgeText"] as string ?? "From SIS";
    var helpText = ViewData["helpText"] as string ?? "";
}

@if (isPreFilled && showBadge)
{
    <span class="sis-badge" role="img" aria-label="Pre-filled from Student Information System">
        <i class="fas fa-database" aria-hidden="true"></i>
        @badgeText
    </span>
}

@if (isReadOnly)
{
    <div id="@(fieldName)-sis-info" class="sr-only">
        This field has been automatically filled from the Student Information System and cannot be edited.
        @if (!string.IsNullOrEmpty(helpText))
        {
            @helpText
        }
    </div>
}
