using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Forms.ktech.Data;
using Forms.ktech.Features.KuwaitiStudentInfo.Models;
using Forms.ktech.Models;
using Forms.ktech.Services.SIS;
using Forms.ktech.Services;
using Forms.ktech.ViewModels.Admin;
using Microsoft.AspNetCore.SignalR;
using System.ComponentModel.DataAnnotations;

namespace Forms.ktech.Controllers
{
    /// <summary>
    /// Admin controller for managing form submissions and system settings
    /// Requires admin role for access
    /// </summary>
    [Authorize(Roles = "Admin")]
    [Route("Admin")]
    public class AdminController : Controller
    {
        private readonly FormsKTechContext _context;
        private readonly ILogger<AdminController> _logger;
        private readonly ISisSyncService _sisSyncService;
        private readonly ISisApiClient _sisApiClient;
        private readonly IFileUploadService _fileUploadService;
        private readonly IExportService _exportService;
        private readonly IDocumentApprovalService _documentApprovalService;
        private readonly IEmailNotificationService _emailNotificationService;
        private readonly IEmailPreviewService _emailPreviewService;
        private readonly ISubmissionApprovalService _submissionApprovalService;
        private readonly IConfiguration _configuration;

        public AdminController(
            FormsKTechContext context,
            ILogger<AdminController> logger,
            ISisSyncService sisSyncService,
            ISisApiClient sisApiClient,
            IFileUploadService fileUploadService,
            IExportService exportService,
            IDocumentApprovalService documentApprovalService,
            IEmailNotificationService emailNotificationService,
            IEmailPreviewService emailPreviewService,
            ISubmissionApprovalService submissionApprovalService,
            IConfiguration configuration)
        {
            _context = context;
            _logger = logger;
            _sisSyncService = sisSyncService;
            _sisApiClient = sisApiClient;
            _fileUploadService = fileUploadService;
            _exportService = exportService;
            _documentApprovalService = documentApprovalService;
            _emailNotificationService = emailNotificationService;
            _emailPreviewService = emailPreviewService;
            _submissionApprovalService = submissionApprovalService;
            _configuration = configuration;
        }

        /// <summary>
        /// GET: /Admin/Dashboard
        /// Displays the admin dashboard with statistics and recent submissions
        /// </summary>
        [HttpGet("Dashboard")]
        public async Task<IActionResult> Dashboard()
        {
            try
            {
                // Get submission statistics using the new service
                var statistics = await _submissionApprovalService.GetSubmissionStatisticsAsync();

                // Get recent submissions with status information
                var recentSubmissions = await _context.StudentInfos
                    .OrderByDescending(s => s.CreatedDate)
                    .Take(10)
                    .Select(s => new
                    {
                        s.Id,
                        StudentName = s.GetStudentName(),
                        s.CreatedDate,
                        s.SubmissionGuid,
                        s.Status,
                        StatusDisplayText = s.GetStatusDisplayText(),
                        StatusCssClass = s.GetStatusCssClass()
                    })
                    .ToListAsync();

                // Get pending reviews (submissions created in last 30 days that are still pending)
                var pendingReviews = await _context.StudentInfos
                    .Where(s => s.Status == SubmissionStatus.Pending && s.CreatedDate >= DateTime.UtcNow.AddDays(-30))
                    .CountAsync();

                ViewBag.Statistics = statistics;
                ViewBag.TotalSubmissions = statistics.TotalCount;
                ViewBag.PendingReviews = pendingReviews;
                ViewBag.RecentSubmissions = recentSubmissions;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading admin dashboard");
                return View("Error");
            }
        }

        /// <summary>
        /// GET: /Admin/Submissions
        /// Displays a list of all form submissions with filtering, search, and pagination
        /// </summary>
        [HttpGet("Submissions")]
        public async Task<IActionResult> Submissions(
            int page = 1,
            int pageSize = 20,
            string? status = null,
            string? search = null,
            string? sortBy = "CreatedDate",
            string? sortOrder = "desc")
        {
            try
            {
                var query = _context.StudentInfos.AsQueryable();

                // Apply status filter
                if (!string.IsNullOrEmpty(status) && Enum.TryParse<SubmissionStatus>(status, out var statusEnum))
                {
                    query = query.Where(s => s.Status == statusEnum);
                }

                // Apply search filter
                if (!string.IsNullOrEmpty(search))
                {
                    var searchLower = search.ToLower();
                    query = query.Where(s =>
                        (s.SisStudent != null && s.SisStudent.FullNameEN.ToLower().Contains(searchLower)) ||
                        (s.SisStudent != null && s.SisStudent.NationalID.Contains(search)) ||
                        s.Id.ToString().Contains(search));
                }

                // Apply sorting
                query = sortBy?.ToLower() switch
                {
                    "name" => sortOrder == "asc"
                        ? query.OrderBy(s => s.SisStudent != null ? s.SisStudent.FullNameEN : "")
                        : query.OrderByDescending(s => s.SisStudent != null ? s.SisStudent.FullNameEN : ""),
                    "status" => sortOrder == "asc"
                        ? query.OrderBy(s => s.Status)
                        : query.OrderByDescending(s => s.Status),
                    "civilid" => sortOrder == "asc"
                        ? query.OrderBy(s => s.SisStudent != null ? s.SisStudent.NationalID : "")
                        : query.OrderByDescending(s => s.SisStudent != null ? s.SisStudent.NationalID : ""),
                    _ => sortOrder == "asc"
                        ? query.OrderBy(s => s.CreatedDate)
                        : query.OrderByDescending(s => s.CreatedDate)
                };

                var totalCount = await query.CountAsync();
                var submissions = await query
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                // Get submission statistics for the filter UI
                var statistics = await _submissionApprovalService.GetSubmissionStatisticsAsync();

                ViewBag.CurrentPage = page;
                ViewBag.PageSize = pageSize;
                ViewBag.TotalCount = totalCount;
                ViewBag.TotalPages = (int)Math.Ceiling((double)totalCount / pageSize);
                ViewBag.CurrentStatus = status;
                ViewBag.CurrentSearch = search;
                ViewBag.CurrentSortBy = sortBy;
                ViewBag.CurrentSortOrder = sortOrder;
                ViewBag.Statistics = statistics;

                return View(submissions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading submissions list");
                return View("Error");
            }
        }

        /// <summary>
        /// GET: /Admin/Settings
        /// Displays the admin settings page
        /// </summary>
        [HttpGet("Settings")]
        public IActionResult Settings()
        {
            return View();
        }

        /// <summary>
        /// GET: /Admin/ViewSubmission/{id}
        /// Displays detailed view of a specific submission with comprehensive data
        /// </summary>
        [HttpGet("ViewSubmission/{id}")]
        public async Task<IActionResult> ViewSubmission(int id, string? returnUrl = null)
        {
            try
            {
                // Enhanced query with related data
                var submission = await _context.StudentInfos
                    .Include(s => s.SisStudent)
                    .AsSplitQuery()
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (submission == null)
                {
                    _logger.LogWarning("Submission not found: {SubmissionId}", id);
                    return NotFound();
                }

                // Create comprehensive view model
                var viewModel = await CreateSubmissionViewModelAsync(submission, returnUrl);

                // Add email preview data for batch notification system
                try
                {
                    var emailPreview = await _emailPreviewService.GenerateEmailPreviewAsync(id);
                    ViewBag.EmailPreview = emailPreview;
                    ViewBag.HasPendingEmails = emailPreview.HasPendingEmails;
                    ViewBag.DisapprovedDocuments = emailPreview.DisapprovedDocuments;
                }
                catch (Exception emailEx)
                {
                    _logger.LogWarning(emailEx, "Could not load email preview data for submission {SubmissionId}", id);
                    ViewBag.EmailPreview = null;
                    ViewBag.HasPendingEmails = false;
                    ViewBag.DisapprovedDocuments = new List<ViewModels.Admin.DisapprovedDocumentInfo>();
                }

                // Log admin access for audit trail
                await LogAdminAccessAsync(id, "ViewSubmission", $"Admin viewed submission details");

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading submission {SubmissionId}", id);
                return View("Error");
            }
        }

        #region SIS Sync Management

        /// <summary>
        /// GET: /Admin/SyncDashboard
        /// Displays the SIS synchronization dashboard with status and statistics
        /// </summary>
        [HttpGet("SyncDashboard")]
        public async Task<IActionResult> SyncDashboard()
        {
            try
            {
                // Get sync statistics
                var totalStudents = await _context.SisStudents.CountAsync();
                var lastSyncDate = await _context.SyncHistories
                    .Where(s => s.Status == Data.SyncStatus.Completed)
                    .OrderByDescending(s => s.EndTime)
                    .Select(s => s.EndTime)
                    .FirstOrDefaultAsync();

                // Get current sync status
                var currentSyncStatus = await _sisSyncService.GetCurrentSyncStatusAsync();

                // Get recent sync history
                var recentSyncs = await _context.SyncHistories
                    .OrderByDescending(s => s.StartTime)
                    .Take(5)
                    .ToListAsync();

                // Get SIS API health status
                var apiHealthStatus = await _sisApiClient.GetHealthStatusAsync();

                ViewBag.TotalStudents = totalStudents;
                ViewBag.LastSyncDate = lastSyncDate;
                ViewBag.CurrentSyncStatus = currentSyncStatus;
                ViewBag.RecentSyncs = recentSyncs;
                ViewBag.ApiHealthStatus = apiHealthStatus;

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading SIS sync dashboard");
                return View("Error");
            }
        }

        /// <summary>
        /// GET: /Admin/SyncHistory
        /// Displays paginated history of SIS synchronization operations
        /// </summary>
        [HttpGet("SyncHistory")]
        public async Task<IActionResult> SyncHistory(int page = 1, int pageSize = 20)
        {
            try
            {
                var syncHistory = await _context.SyncHistories
                    .OrderByDescending(s => s.StartTime)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var totalCount = await _context.SyncHistories.CountAsync();

                ViewBag.CurrentPage = page;
                ViewBag.PageSize = pageSize;
                ViewBag.TotalCount = totalCount;
                ViewBag.TotalPages = (int)Math.Ceiling((double)totalCount / pageSize);

                return View(syncHistory);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading sync history");
                return View("Error");
            }
        }

        /// <summary>
        /// POST: /Admin/TriggerSync
        /// Triggers a manual SIS synchronization operation
        /// </summary>
        [HttpPost("TriggerSync")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> TriggerSync(string syncType = "Full")
        {
            try
            {
                var userId = User.Identity?.Name ?? "Unknown";
                _logger.LogInformation("Manual sync triggered by user: {UserId}, Type: {SyncType}", userId, syncType);

                Services.SIS.SyncResult result;
                if (syncType.Equals("Incremental", StringComparison.OrdinalIgnoreCase))
                {
                    result = await _sisSyncService.StartIncrementalSyncAsync(userId);
                }
                else
                {
                    result = await _sisSyncService.StartFullSyncAsync(userId);
                }

                if (result.Status == Data.SyncStatus.Completed)
                {
                    TempData["SuccessMessage"] = $"Sync completed successfully. Processed {result.RecordsProcessed} records.";
                }
                else
                {
                    TempData["ErrorMessage"] = $"Sync failed: {result.ErrorMessage}";
                }

                return RedirectToAction(nameof(SyncDashboard));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error triggering manual sync");
                TempData["ErrorMessage"] = "Failed to start synchronization. Please try again.";
                return RedirectToAction(nameof(SyncDashboard));
            }
        }

        /// <summary>
        /// POST: /Admin/CancelSync
        /// Cancels a running SIS synchronization operation
        /// </summary>
        [HttpPost("CancelSync")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CancelSync()
        {
            try
            {
                var userId = User.Identity?.Name ?? "Unknown";
                _logger.LogInformation("Sync cancellation requested by user: {UserId}", userId);

                // Get the current running sync
                var runningSyncs = await _context.SyncHistories
                    .Where(sh => sh.Status == Data.SyncStatus.InProgress)
                    .OrderByDescending(sh => sh.StartTime)
                    .ToListAsync();

                if (!runningSyncs.Any())
                {
                    return Json(new { success = false, message = "No running sync operation found to cancel." });
                }

                var currentSync = runningSyncs.First();
                var cancelled = await _sisSyncService.CancelSyncAsync(currentSync.SyncId);

                if (cancelled)
                {
                    _logger.LogInformation("Sync operation {SyncId} cancelled successfully by user {UserId}", currentSync.SyncId, userId);
                    return Json(new { success = true, message = "Sync operation cancelled successfully." });
                }
                else
                {
                    _logger.LogWarning("Failed to cancel sync operation {SyncId} for user {UserId}", currentSync.SyncId, userId);
                    return Json(new { success = false, message = "Failed to cancel sync operation. It may have already completed." });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling sync operation");
                return Json(new { success = false, message = "An error occurred while cancelling the sync operation." });
            }
        }

        /// <summary>
        /// GET: /Admin/SyncSettings
        /// Displays SIS synchronization settings and configuration
        /// </summary>
        [HttpGet("SyncSettings")]
        public IActionResult SyncSettings()
        {
            return View();
        }        /// <summary>
                 /// GET: /Admin/SyncStatus
                 /// API endpoint for getting current sync status (for AJAX polling)
                 /// </summary>
        [HttpGet("SyncStatus")]
        [ResponseCache(NoStore = true, Location = ResponseCacheLocation.None)]
        public async Task<IActionResult> SyncStatus()
        {
            try
            {
                var currentStatus = await _sisSyncService.GetCurrentSyncStatusAsync();
                var lastSync = await _context.SyncHistories
                    .OrderByDescending(s => s.StartTime)
                    .FirstOrDefaultAsync();

                return Json(new
                {
                    currentStatus = currentStatus.ToString(),
                    lastSync = lastSync != null ? new
                    {
                        id = lastSync.SyncId,
                        type = lastSync.SyncType.ToString(),
                        status = lastSync.Status.ToString(),
                        startTime = lastSync.StartTime,
                        endTime = lastSync.EndTime,
                        progress = lastSync.ProgressPercentage,
                        currentStep = lastSync.CurrentStep,
                        recordsProcessed = lastSync.RecordsProcessed,
                        recordsAdded = lastSync.RecordsAdded,
                        recordsUpdated = lastSync.RecordsUpdated,
                        duration = lastSync.GetDurationString()
                    } : null
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sync status");
                return Json(new { error = "Failed to get sync status" });
            }
        }

        /// <summary>
        /// GET: /Admin/SubmissionStatistics
        /// API endpoint for getting submission statistics (for AJAX updates)
        /// </summary>
        [HttpGet("SubmissionStatistics")]
        [ResponseCache(NoStore = true, Location = ResponseCacheLocation.None)]
        public async Task<IActionResult> SubmissionStatistics()
        {
            try
            {
                var statistics = await _submissionApprovalService.GetSubmissionStatisticsAsync();

                return Json(new
                {
                    totalCount = statistics.TotalCount,
                    pendingCount = statistics.PendingCount,
                    approvedCount = statistics.ApprovedCount,
                    rejectedCount = statistics.RejectedCount,
                    flaggedCount = statistics.FlaggedCount,
                    lastUpdated = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submission statistics");
                return Json(new { error = "Failed to get submission statistics" });
            }
        }

        #endregion

        #region Document Management

        /// <summary>
        /// GET: /Admin/ViewDocument/{submissionId}/{documentType}
        /// Securely displays a document for admin viewing
        /// </summary>
        [HttpGet("ViewDocument/{submissionId}/{documentType}")]

        public async Task<IActionResult> ViewDocument(int submissionId, string documentType)
        {
            try
            {
                // Validate submission exists and admin has access
                var submission = await _context.StudentInfos.FindAsync(submissionId);
                if (submission == null)
                {
                    _logger.LogWarning("Document view attempted for non-existent submission: {SubmissionId}", submissionId);
                    return NotFound("Submission not found");
                }

                // Get document path
                var documentPath = GetDocumentPath(submission, documentType);
                if (string.IsNullOrEmpty(documentPath))
                {
                    _logger.LogWarning("Document type not found: {DocumentType} for submission {SubmissionId}", documentType, submissionId);
                    return NotFound("Document not found");
                }

                // Validate file exists
                if (!_fileUploadService.FileExists(documentPath))
                {
                    _logger.LogWarning("Document file not found: {DocumentPath} for submission {SubmissionId}", documentPath, submissionId);
                    return NotFound("Document file not found");
                }

                // Get file info
                var fileInfo = _fileUploadService.GetFileInfo(documentPath);
                if (fileInfo == null)
                {
                    return NotFound("Unable to access document");
                }

                // Log admin document access
                await LogAdminAccessAsync(submissionId, "ViewDocument", $"Admin viewed document: {documentType}");

                // Get file stream
                var fileStream = await _fileUploadService.GetFileStreamAsync(documentPath);
                if (fileStream == null)
                {
                    return NotFound("Unable to read document");
                }

                // Determine content type
                var contentType = GetContentType(fileInfo.Extension);

                // For supported preview types, return inline
                if (IsSupportedPreviewType(fileInfo.Extension))
                {
                    return File(fileStream, contentType, enableRangeProcessing: true);
                }
                else
                {
                    // For unsupported types, force download
                    return File(fileStream, "application/octet-stream", fileInfo.Name);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error viewing document {DocumentType} for submission {SubmissionId}", documentType, submissionId);
                return StatusCode(500, "Error accessing document");
            }
        }

        /// <summary>
        /// GET: /Admin/DownloadDocument/{submissionId}/{documentType}
        /// Securely downloads a document for admin users
        /// </summary>
        [HttpGet("DownloadDocument/{submissionId}/{documentType}")]

        public async Task<IActionResult> DownloadDocument(int submissionId, string documentType)
        {
            try
            {
                // Validate submission exists and admin has access
                var submission = await _context.StudentInfos.FindAsync(submissionId);
                if (submission == null)
                {
                    _logger.LogWarning("Document download attempted for non-existent submission: {SubmissionId}", submissionId);
                    return NotFound("Submission not found");
                }

                // Get document path
                var documentPath = GetDocumentPath(submission, documentType);
                if (string.IsNullOrEmpty(documentPath))
                {
                    _logger.LogWarning("Document type not found: {DocumentType} for submission {SubmissionId}", documentType, submissionId);
                    return NotFound("Document not found");
                }

                // Validate file exists
                if (!_fileUploadService.FileExists(documentPath))
                {
                    _logger.LogWarning("Document file not found: {DocumentPath} for submission {SubmissionId}", documentPath, submissionId);
                    return NotFound("Document file not found");
                }

                // Get file info
                var fileInfo = _fileUploadService.GetFileInfo(documentPath);
                if (fileInfo == null)
                {
                    return NotFound("Unable to access document");
                }

                // Log admin document download
                await LogAdminAccessAsync(submissionId, "DownloadDocument", $"Admin downloaded document: {documentType}");

                // Get file stream
                var fileStream = await _fileUploadService.GetFileStreamAsync(documentPath);
                if (fileStream == null)
                {
                    return NotFound("Unable to read document");
                }

                // Generate secure filename for download
                var downloadFileName = GenerateSecureDownloadFileName(submission, documentType, fileInfo.Extension);

                // Return file for download
                return File(fileStream, "application/octet-stream", downloadFileName, enableRangeProcessing: true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading document {DocumentType} for submission {SubmissionId}", documentType, submissionId);
                return StatusCode(500, "Error downloading document");
            }
        }

        /// <summary>
        /// GET: /Admin/DocumentInfo/{submissionId}/{documentType}
        /// Returns detailed information about a document
        /// </summary>
        [HttpGet("DocumentInfo/{submissionId}/{documentType}")]

        public async Task<IActionResult> DocumentInfo(int submissionId, string documentType)
        {
            try
            {
                _logger.LogInformation("=== DocumentInfo API Called ===");
                _logger.LogInformation("Request: SubmissionId={SubmissionId}, DocumentType='{DocumentType}'", submissionId, documentType);

                // Validate submission exists
                var submission = await _context.StudentInfos.FindAsync(submissionId);
                if (submission == null)
                {
                    _logger.LogWarning("DocumentInfo: Submission not found for ID {SubmissionId}", submissionId);
                    return NotFound("Submission not found");
                }

                // Get document path (can be null for missing documents)
                var documentPath = GetDocumentPath(submission, documentType);

                // Get comprehensive document information (handles missing documents)
                var documentInfo = await GetDetailedDocumentInfoAsync(submission, documentType, documentPath);



                return Json(documentInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting document info {DocumentType} for submission {SubmissionId}", documentType, submissionId);
                return StatusCode(500, "Error retrieving document information");
            }
        }





        #endregion

        #region ViewSubmission Helper Methods

        /// <summary>
        /// Creates a comprehensive view model for submission details
        /// </summary>
        /// <param name="submission">The submission entity</param>
        /// <param name="returnUrl">URL to return to after viewing</param>
        /// <returns>Complete view model with all necessary data</returns>
        private async Task<SubmissionDetailViewModel> CreateSubmissionViewModelAsync(StudentInfo submission, string? returnUrl = null)
        {
            var viewModel = new SubmissionDetailViewModel
            {
                Submission = submission,
                DocumentInfo = await GetDocumentInfoAsync(submission),
                SisIntegrationStatus = await GetSisIntegrationStatusAsync(submission),
                AuditTrail = await GetAuditTrailAsync(submission.Id),
                Statistics = await GetSubmissionStatisticsAsync(submission),
                NavigationContext = new NavigationContext
                {
                    ReturnUrl = returnUrl ?? Url.Action("Submissions", "Admin"),
                    ReturnText = "Back to Submissions"
                }
            };

            return viewModel;
        }

        /// <summary>
        /// Gathers information about all documents for a submission
        /// </summary>
        /// <param name="submission">The submission entity</param>
        /// <returns>List of document information</returns>
        private async Task<List<DocumentInfo>> GetDocumentInfoAsync(StudentInfo submission)
        {
            var documents = new List<DocumentInfo>();

            // Define document mappings with bilingual names
            var documentMappings = new Dictionary<string, (string DisplayName, string Category, bool IsRequired)>
            {
                { nameof(submission.StudentCivilIdPath), ("Student Civil ID / الهوية المدنية للطالب", "Student", true) },
                { nameof(submission.StudentNationalityCertificatePath), ("Student Nationality Certificate / شهادة جنسية الطالب", "Student", submission.StudentIsKuwaiti) },
                { nameof(submission.StudentBirthCertificatePath), ("Student Birth Certificate / شهادة ميلاد الطالب", "Student", true) },
                { nameof(submission.FatherCivilIdPath), ("Father Civil ID / الهوية المدنية للأب", "Father", submission.FatherIsKuwaiti) },
                { nameof(submission.FatherNationalityCertificatePath), ("Father Nationality Certificate / شهادة جنسية الأب", "Father", submission.FatherIsKuwaiti) },
                { nameof(submission.FatherDeathCertificatePath), ("Father Death Certificate / شهادة وفاة الأب", "Father", submission.FatherIsDeceased) },
                { nameof(submission.MotherCivilIdPath), ("Mother Civil ID / الهوية المدنية للأم", "Mother", submission.MotherIsKuwaiti) },
                { nameof(submission.MotherNationalityCertificatePath), ("Mother Nationality Certificate / شهادة جنسية الأم", "Mother", submission.MotherIsKuwaiti) },
                { nameof(submission.MotherDeathCertificatePath), ("Mother Death Certificate / شهادة وفاة الأم", "Mother", submission.MotherIsDeceased) }
            };

            foreach (var mapping in documentMappings)
            {
                var propertyName = mapping.Key;
                var (displayName, category, isRequired) = mapping.Value;

                // Get file path using reflection
                var filePath = GetPropertyValue(submission, propertyName) as string;

                var documentDetail = new DocumentDetail
                {
                    DocumentType = propertyName,
                    DisplayName = displayName,
                    Category = category,
                    IsRequired = isRequired,
                    FilePath = filePath,
                    IsUploaded = !string.IsNullOrEmpty(filePath)
                };

                if (!string.IsNullOrEmpty(filePath))
                {
                    try
                    {
                        var physicalPath = _fileUploadService.GetPhysicalPath(filePath);
                        var fileInfo = new FileInfo(physicalPath);

                        documentDetail.FileExists = fileInfo.Exists;
                        documentDetail.FileName = fileInfo.Name;
                        documentDetail.FileSizeBytes = fileInfo.Exists ? fileInfo.Length : 0;
                        documentDetail.FormattedFileSize = FormatFileSize(documentDetail.FileSizeBytes);
                        documentDetail.UploadDate = submission.CreatedDate; // Approximate upload date
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error getting file info for {FilePath}", filePath);
                        documentDetail.FileExists = false;
                    }
                }

                // Create DocumentInfo container with this single document
                var documentInfo = new DocumentInfo();
                documentInfo.Documents.Add(documentDetail);
                documents.Add(documentInfo);
            }

            return documents;
        }

        /// <summary>
        /// Gets SIS integration status for a submission
        /// </summary>
        /// <param name="submission">The submission entity</param>
        /// <returns>SIS integration information</returns>
        private async Task<SisIntegrationInfo> GetSisIntegrationStatusAsync(StudentInfo submission)
        {
            var sisInfo = new SisIntegrationInfo
            {
                IsLinkedToSis = submission.SisStudentId.HasValue && submission.SisStudent != null
            };

            if (sisInfo.IsLinkedToSis && submission.SisStudent != null)
            {
                sisInfo.LastSyncDate = submission.SisStudent.LastSyncDate;
                sisInfo.SisStudentId = submission.SisStudent.Id.ToString();
                sisInfo.SisFullNameAR = submission.SisStudent.FullNameAR;
                sisInfo.SisFullNameEN = submission.SisStudent.FullNameEN;
                sisInfo.IsDataFresh = sisInfo.HoursSinceLastSync < 24; // Consider data fresh if synced within 24 hours

                // Determine pre-filled fields (simplified logic for Phase 1)
                sisInfo.PreFilledFields = ["StudentName", "StudentCivilId"];
            }

            return sisInfo;
        }

        /// <summary>
        /// Gets audit trail for a submission (placeholder for Phase 1)
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <returns>List of audit trail entries</returns>
        private async Task<List<AuditTrailEntry>> GetAuditTrailAsync(int submissionId)
        {
            // Placeholder implementation for Phase 1
            // In future phases, this would query an actual audit log table
            return [];
        }

        /// <summary>
        /// Gets statistical information about a submission
        /// </summary>
        /// <param name="submission">The submission entity</param>
        /// <returns>Submission statistics</returns>
        private async Task<SubmissionStatistics> GetSubmissionStatisticsAsync(StudentInfo submission)
        {
            var documents = await GetDocumentInfoAsync(submission);
            var allDocumentDetails = documents.SelectMany(d => d.Documents).ToList();
            var uploadedDocuments = allDocumentDetails.Where(d => !string.IsNullOrEmpty(d.FilePath) && d.FileExists).ToList();

            return new SubmissionStatistics
            {
                TotalDocuments = uploadedDocuments.Count,
                RequiredDocuments = uploadedDocuments.Count(d => d.IsRequired),
                OptionalDocuments = uploadedDocuments.Count(d => !d.IsRequired),
                TotalFileSize = uploadedDocuments.Sum(d => d.FileSizeBytes),
                AdminViewCount = 1, // Placeholder
                LastAdminView = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Logs admin access for audit purposes
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="action">The action performed</param>
        /// <param name="details">Additional details</param>
        private async Task LogAdminAccessAsync(int submissionId, string action, string details)
        {
            var userId = User.Identity?.Name ?? "Unknown";
            var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";

            _logger.LogInformation("Admin Access: User {UserId} performed {Action} on submission {SubmissionId} from IP {IpAddress}. Details: {Details}",
                userId, action, submissionId, ipAddress, details);

            // In future phases, this would also write to an audit log table
        }

        /// <summary>
        /// Gets property value using reflection
        /// </summary>
        /// <param name="obj">The object</param>
        /// <param name="propertyName">Property name</param>
        /// <returns>Property value</returns>
        private static object? GetPropertyValue(object obj, string propertyName)
        {
            return obj.GetType().GetProperty(propertyName)?.GetValue(obj);
        }

        /// <summary>
        /// Gets MIME type based on file extension
        /// </summary>
        /// <param name="extension">File extension</param>
        /// <returns>MIME type</returns>
        private static string GetMimeType(string extension)
        {
            return extension.ToLowerInvariant() switch
            {
                ".pdf" => "application/pdf",
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                _ => "application/octet-stream"
            };
        }

        #endregion

        #region Document Helper Methods

        /// <summary>
        /// Gets the document path for a specific document type from a submission
        /// </summary>
        /// <param name="submission">The submission entity</param>
        /// <param name="documentType">The document type property name</param>
        /// <returns>Document path or null if not found</returns>
        private static string? GetDocumentPath(StudentInfo submission, string documentType)
        {
            return documentType switch
            {
                nameof(submission.StudentCivilIdPath) => submission.StudentCivilIdPath,
                nameof(submission.StudentNationalityCertificatePath) => submission.StudentNationalityCertificatePath,
                nameof(submission.StudentBirthCertificatePath) => submission.StudentBirthCertificatePath,
                nameof(submission.FatherCivilIdPath) => submission.FatherCivilIdPath,
                nameof(submission.FatherNationalityCertificatePath) => submission.FatherNationalityCertificatePath,
                nameof(submission.FatherDeathCertificatePath) => submission.FatherDeathCertificatePath,
                nameof(submission.MotherCivilIdPath) => submission.MotherCivilIdPath,
                nameof(submission.MotherNationalityCertificatePath) => submission.MotherNationalityCertificatePath,
                nameof(submission.MotherDeathCertificatePath) => submission.MotherDeathCertificatePath,
                _ => null
            };
        }

        /// <summary>
        /// Gets the content type for a file extension
        /// </summary>
        /// <param name="extension">File extension</param>
        /// <returns>MIME content type</returns>
        private static string GetContentType(string extension)
        {
            return extension.ToLowerInvariant() switch
            {
                ".pdf" => "application/pdf",
                ".jpg" or ".jpeg" => "image/jpeg",
                ".png" => "image/png",
                ".gif" => "image/gif",
                ".bmp" => "image/bmp",
                ".webp" => "image/webp",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".txt" => "text/plain",
                _ => "application/octet-stream"
            };
        }

        /// <summary>
        /// Checks if a file type supports inline preview
        /// </summary>
        /// <param name="extension">File extension</param>
        /// <returns>True if preview is supported</returns>
        private static bool IsSupportedPreviewType(string extension)
        {
            var supportedTypes = new[] { ".pdf", ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".txt" };
            return supportedTypes.Contains(extension.ToLowerInvariant());
        }

        /// <summary>
        /// Generates a secure filename for document downloads
        /// </summary>
        /// <param name="submission">The submission entity</param>
        /// <param name="documentType">The document type</param>
        /// <param name="extension">File extension</param>
        /// <returns>Secure download filename</returns>
        private static string GenerateSecureDownloadFileName(StudentInfo submission, string documentType, string extension)
        {
            var civilId = submission.GetStudentCivilId();
            var documentName = GetDocumentDisplayName(documentType);
            var timestamp = DateTime.Now.ToString("yyyyMMdd");

            // Create safe filename: CivilID_DocumentName_Date.ext
            var safeFileName = $"{civilId}_{documentName}_{timestamp}{extension}";

            // Remove any invalid characters
            var invalidChars = Path.GetInvalidFileNameChars();
            foreach (var invalidChar in invalidChars)
            {
                safeFileName = safeFileName.Replace(invalidChar, '_');
            }

            return safeFileName;
        }

        /// <summary>
        /// Gets a display name for a document type
        /// </summary>
        /// <param name="documentType">The document type property name</param>
        /// <returns>Human-readable document name</returns>
        private static string GetDocumentDisplayName(string documentType)
        {
            return documentType switch
            {
                nameof(StudentInfo.StudentCivilIdPath) => "Student_Civil_ID",
                nameof(StudentInfo.StudentNationalityCertificatePath) => "Student_Nationality_Certificate",
                nameof(StudentInfo.StudentBirthCertificatePath) => "Student_Birth_Certificate",
                nameof(StudentInfo.FatherCivilIdPath) => "Father_Civil_ID",
                nameof(StudentInfo.FatherNationalityCertificatePath) => "Father_Nationality_Certificate",
                nameof(StudentInfo.FatherDeathCertificatePath) => "Father_Death_Certificate",
                nameof(StudentInfo.MotherCivilIdPath) => "Mother_Civil_ID",
                nameof(StudentInfo.MotherNationalityCertificatePath) => "Mother_Nationality_Certificate",
                nameof(StudentInfo.MotherDeathCertificatePath) => "Mother_Death_Certificate",
                _ => "Document"
            };
        }

        /// <summary>
        /// Gets a bilingual display name for a document type
        /// </summary>
        /// <param name="documentType">The document type property name</param>
        /// <returns>Bilingual document name</returns>
        private static string GetDocumentDisplayNameBilingual(string documentType)
        {
            return documentType switch
            {
                nameof(StudentInfo.StudentCivilIdPath) => "Student Civil ID / الهوية المدنية للطالب",
                nameof(StudentInfo.StudentNationalityCertificatePath) => "Student Nationality Certificate / شهادة جنسية الطالب",
                nameof(StudentInfo.StudentBirthCertificatePath) => "Student Birth Certificate / شهادة ميلاد الطالب",
                nameof(StudentInfo.FatherCivilIdPath) => "Father Civil ID / الهوية المدنية للأب",
                nameof(StudentInfo.FatherNationalityCertificatePath) => "Father Nationality Certificate / شهادة جنسية الأب",
                nameof(StudentInfo.FatherDeathCertificatePath) => "Father Death Certificate / شهادة وفاة الأب",
                nameof(StudentInfo.MotherCivilIdPath) => "Mother Civil ID / الهوية المدنية للأم",
                nameof(StudentInfo.MotherNationalityCertificatePath) => "Mother Nationality Certificate / شهادة جنسية الأم",
                nameof(StudentInfo.MotherDeathCertificatePath) => "Mother Death Certificate / شهادة وفاة الأم",
                _ => "Document / مستند"
            };
        }

        /// <summary>
        /// Gets detailed document information including validation and metadata
        /// </summary>
        /// <param name="submission">The submission entity</param>
        /// <param name="documentType">The document type</param>
        /// <param name="documentPath">The document path (can be null for missing documents)</param>
        /// <returns>Detailed document information</returns>
        private async Task<object> GetDetailedDocumentInfoAsync(StudentInfo submission, string documentType, string? documentPath)
        {
            // Get document display name from mapping
            var displayName = GetDocumentDisplayNameBilingual(documentType);
            var isRequired = IsDocumentRequired(submission, documentType);

            FileInfo? fileInfo = null;
            bool exists = false;
            string physicalPath = "";



            // Only check file info if path exists
            if (!string.IsNullOrEmpty(documentPath))
            {
                try
                {
                    // Use FileUploadService to get the physical path
                    physicalPath = _fileUploadService.GetPhysicalPath(documentPath);

                    // Check if file exists using direct System.IO.File.Exists for reliability
                    exists = System.IO.File.Exists(physicalPath);

                    if (exists)
                    {
                        fileInfo = new FileInfo(physicalPath);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error getting file info for DocumentPath={DocumentPath}, PhysicalPath={PhysicalPath}",
                        documentPath, physicalPath);
                    exists = false;
                }
            }


            // Create comprehensive result object
            var result = new
            {
                DocumentType = documentType,
                DisplayName = displayName,
                FilePath = documentPath ?? "",
                FileExists = exists,
                FileName = fileInfo?.Name ?? (exists ? Path.GetFileName(documentPath) : ""),
                FileSize = fileInfo?.Length ?? 0,
                FormattedFileSize = fileInfo != null ? FormatFileSize(fileInfo.Length) : (exists ? "Unknown size" : "N/A"),
                LastModified = fileInfo?.LastWriteTime,
                Extension = fileInfo?.Extension ?? (exists ? Path.GetExtension(documentPath) : ""),
                ContentType = fileInfo != null ? GetContentType(fileInfo.Extension) :
                             (exists ? GetContentType(Path.GetExtension(documentPath) ?? "") : "Unknown"),
                IsPreviewSupported = fileInfo != null ? IsSupportedPreviewType(fileInfo.Extension) :
                                   (exists ? IsSupportedPreviewType(Path.GetExtension(documentPath) ?? "") : false),
                IsRequired = isRequired,
                ValidationStatus = await ValidateDocumentAsync(submission, documentType, documentPath),
                UploadDate = submission.CreatedDate, // Approximate upload date
                SubmissionId = submission.Id,
                SubmissionGuid = submission.SubmissionGuid,
                // Add action URLs for the modal
                ViewUrl = !string.IsNullOrEmpty(documentPath) && exists ?
                    Url.Action("ViewDocument", "Admin", new { submissionId = submission.Id, documentType }) : null,
                DownloadUrl = !string.IsNullOrEmpty(documentPath) && exists ?
                    Url.Action("DownloadDocument", "Admin", new { submissionId = submission.Id, documentType }) : null,

            };



            return result;
        }

        /// <summary>
        /// Formats file size for display
        /// </summary>
        /// <param name="bytes">File size in bytes</param>
        /// <returns>Formatted file size string</returns>
        private static string FormatFileSize(long bytes)
        {
            if (bytes == 0) return "0 B";

            string[] sizes = { "B", "KB", "MB", "GB" };
            int order = 0;
            double size = bytes;

            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }

            return $"{size:0.##} {sizes[order]}";
        }

        /// <summary>
        /// Checks if a document is required for a submission
        /// </summary>
        /// <param name="submission">The submission entity</param>
        /// <param name="documentType">The document type</param>
        /// <returns>True if the document is required</returns>
        private static bool IsDocumentRequired(StudentInfo submission, string documentType)
        {
            return documentType switch
            {
                nameof(submission.StudentCivilIdPath) => true,
                nameof(submission.StudentNationalityCertificatePath) => submission.StudentIsKuwaiti,
                nameof(submission.StudentBirthCertificatePath) => true, // Updated: Birth Certificate is now required for all submissions
                nameof(submission.FatherCivilIdPath) => submission.FatherIsKuwaiti,
                nameof(submission.FatherNationalityCertificatePath) => submission.FatherIsKuwaiti,
                nameof(submission.FatherDeathCertificatePath) => submission.FatherIsDeceased,
                nameof(submission.MotherCivilIdPath) => submission.MotherIsKuwaiti,
                nameof(submission.MotherNationalityCertificatePath) => submission.MotherIsKuwaiti,
                nameof(submission.MotherDeathCertificatePath) => submission.MotherIsDeceased,
                _ => false
            };
        }

        /// <summary>
        /// Validates a document and returns validation status
        /// </summary>
        /// <param name="submission">The submission entity</param>
        /// <param name="documentType">The document type</param>
        /// <param name="documentPath">The document path</param>
        /// <returns>Document validation status</returns>
        private async Task<object> ValidateDocumentAsync(StudentInfo submission, string documentType, string? documentPath)
        {
            var errors = new List<string>();
            var warnings = new List<string>();

            // Check if required document is missing
            if (IsDocumentRequired(submission, documentType) && string.IsNullOrEmpty(documentPath))
            {
                var documentDisplayName = GetDocumentDisplayNameBilingual(documentType);
                errors.Add($"Required document '{documentDisplayName}' is missing");
            }
            else if (!string.IsNullOrEmpty(documentPath))
            {
                try
                {
                    var physicalPath = _fileUploadService.GetPhysicalPath(documentPath);

                    // Check if file exists
                    if (!System.IO.File.Exists(physicalPath))
                    {
                        errors.Add("File not found on server");
                    }
                    else
                    {
                        var fileInfo = new FileInfo(physicalPath);

                        // Check file size
                        if (fileInfo.Length == 0)
                        {
                            errors.Add("File is empty");
                        }
                        else if (fileInfo.Length > 5 * 1024 * 1024) // 5MB limit
                        {
                            warnings.Add("File size exceeds recommended limit of 5MB");
                        }

                        // Check file extension
                        var allowedExtensions = new[] { ".pdf", ".jpg", ".jpeg", ".png" };
                        if (!allowedExtensions.Contains(fileInfo.Extension.ToLowerInvariant()))
                        {
                            errors.Add($"File type {fileInfo.Extension} is not allowed");
                        }

                        // Check if file is accessible
                        try
                        {
                            using var stream = new FileStream(physicalPath, FileMode.Open, FileAccess.Read, FileShare.Read);
                            if (stream.Length == 0)
                            {
                                errors.Add("File cannot be read or is corrupted");
                            }
                        }
                        catch (Exception ex)
                        {
                            errors.Add($"File is corrupted or inaccessible: {ex.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    errors.Add($"Error validating file: {ex.Message}");
                }
            }

            var status = errors.Count > 0 ? "Invalid" : warnings.Count > 0 ? "Warning" : "Valid";

            return new
            {
                IsValid = errors.Count == 0,
                Errors = errors,
                Warnings = warnings,
                Status = status
            };
        }

        #endregion

        #region Export Functionality

        /// <summary>
        /// GET: /Admin/ExportSubmissionPdf/{submissionId}
        /// Exports a single submission to PDF format
        /// </summary>
        [HttpGet("ExportSubmissionPdf/{submissionId}")]

        public async Task<IActionResult> ExportSubmissionPdf(int submissionId)
        {
            try
            {
                // Get submission with related data
                var submission = await _context.StudentInfos
                    .Include(s => s.SisStudent)
                    .FirstOrDefaultAsync(s => s.Id == submissionId);

                if (submission == null)
                {
                    _logger.LogWarning("PDF export attempted for non-existent submission: {SubmissionId}", submissionId);
                    return NotFound("Submission not found");
                }

                // Get document information
                var documentInfo = await GetDocumentInfoForExportAsync(submission);

                // Get SIS integration status
                var sisIntegrationStatus = await GetSisIntegrationStatusForExportAsync(submission);

                // Generate PDF
                var pdfBytes = await _exportService.ExportSubmissionToPdfAsync(submission, documentInfo, sisIntegrationStatus);

                // Generate secure filename
                var fileName = _exportService.GenerateSecureFilename(submission.GetStudentCivilId(), "pdf");

                // Log admin export action
                await LogAdminAccessAsync(submissionId, "ExportPdf", $"Admin exported submission to PDF");

                return File(pdfBytes, "application/pdf", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting submission {SubmissionId} to PDF", submissionId);
                return StatusCode(500, "Error generating PDF export");
            }
        }

        /// <summary>
        /// GET: /Admin/ExportSubmissionExcel/{submissionId}
        /// Exports a single submission to Excel format
        /// </summary>
        [HttpGet("ExportSubmissionExcel/{submissionId}")]

        public async Task<IActionResult> ExportSubmissionExcel(int submissionId)
        {
            try
            {
                // Get submission with related data
                var submission = await _context.StudentInfos
                    .Include(s => s.SisStudent)
                    .FirstOrDefaultAsync(s => s.Id == submissionId);

                if (submission == null)
                {
                    _logger.LogWarning("Excel export attempted for non-existent submission: {SubmissionId}", submissionId);
                    return NotFound("Submission not found");
                }

                // Get document information
                var documentInfo = await GetDocumentInfoForExportAsync(submission);

                // Get SIS integration status
                var sisIntegrationStatus = await GetSisIntegrationStatusForExportAsync(submission);

                // Generate Excel
                var excelBytes = await _exportService.ExportSubmissionToExcelAsync(submission, documentInfo, sisIntegrationStatus);

                // Generate secure filename
                var fileName = _exportService.GenerateSecureFilename(submission.GetStudentCivilId(), "xlsx");

                // Log admin export action
                await LogAdminAccessAsync(submissionId, "ExportExcel", $"Admin exported submission to Excel");

                return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting submission {SubmissionId} to Excel", submissionId);
                return StatusCode(500, "Error generating Excel export");
            }
        }

        /// <summary>
        /// POST: /Admin/ExportMultipleExcel
        /// Exports multiple submissions to Excel format
        /// </summary>
        [HttpPost("ExportMultipleExcel")]

        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ExportMultipleExcel([FromForm] int[] submissionIds)
        {
            try
            {
                if (submissionIds == null || submissionIds.Length == 0)
                {
                    return BadRequest("No submissions selected for export");
                }

                // Limit the number of submissions that can be exported at once
                if (submissionIds.Length > 1000)
                {
                    return BadRequest("Too many submissions selected. Maximum 1000 submissions can be exported at once.");
                }

                // Get submissions
                var submissions = await _context.StudentInfos
                    .Where(s => submissionIds.Contains(s.Id))
                    .ToListAsync();

                if (!submissions.Any())
                {
                    return NotFound("No valid submissions found for export");
                }

                // Generate Excel
                var excelBytes = await _exportService.ExportMultipleSubmissionsToExcelAsync(submissions);

                // Generate secure filename
                var fileName = _exportService.GenerateSecureFilename("", "xlsx", isMultiple: true);

                // Log admin export action
                await LogAdminAccessAsync(0, "ExportMultipleExcel", $"Admin exported {submissions.Count} submissions to Excel");

                return File(excelBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting multiple submissions to Excel");
                return StatusCode(500, "Error generating Excel export");
            }
        }

        #endregion

        #region Admin Actions

        /// <summary>
        /// POST: /Admin/ApproveSubmission/{submissionId}
        /// Approves a submission
        /// </summary>
        [HttpPost("ApproveSubmission/{submissionId}")]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> ApproveSubmission(int submissionId, [FromBody] object? requestBody = null)
        {
            // Log incoming request details for debugging
            _logger.LogInformation("ApproveSubmission called - SubmissionId: {SubmissionId}, User: {User}, RequestBody: {RequestBody}, ContentType: {ContentType}",
                submissionId, User.Identity?.Name, requestBody?.ToString() ?? "null", Request.ContentType);

            try
            {
                // Enhanced authorization checks
                var userEmail = User.Identity?.Name ?? "Unknown Admin";
                var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

                // Verify submission exists and admin has access
                var submission = await _context.StudentInfos.FindAsync(submissionId);
                if (submission == null)
                {
                    _logger.LogWarning("Approval attempted for non-existent submission {SubmissionId} by user {UserId}", submissionId, userEmail);
                    return Json(new { success = false, message = "Submission not found" });
                }

                // Check if submission is already approved
                if (submission.Status == SubmissionStatus.Approved)
                {
                    _logger.LogWarning("Approval attempted for already approved submission {SubmissionId} by user {UserId}", submissionId, userEmail);
                    return Json(new { success = false, message = "Submission is already approved" });
                }

                // Log the approval attempt for audit trail
                _logger.LogInformation("Submission approval initiated for {SubmissionId} by admin {UserId} ({UserEmail})",
                    submissionId, userId, userEmail);

                var result = await _submissionApprovalService.ApproveSubmissionAsync(submissionId, userEmail);

                if (result.Success)
                {
                    // Log successful approval
                    _logger.LogInformation("Submission {SubmissionId} successfully approved by admin {UserId} ({UserEmail})",
                        submissionId, userId, userEmail);

                    // Log admin access for audit trail
                    await LogAdminAccessAsync(submissionId, "ApproveSubmission",
                        $"Submission approved by admin {userEmail}. Previous status: {result.PreviousStatus}, New status: {result.NewStatus}");

                    return Json(new { success = true, message = result.Details ?? "Submission approved successfully" });
                }
                else
                {
                    // Log failed approval
                    _logger.LogWarning("Failed to approve submission {SubmissionId} by admin {UserId} ({UserEmail}): {Error}",
                        submissionId, userId, userEmail, result.ErrorMessage);

                    return Json(new { success = false, message = result.ErrorMessage ?? "Failed to approve submission" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving submission {SubmissionId} by user {UserEmail}. Request body: {RequestBody}",
                    submissionId, User.Identity?.Name, requestBody?.ToString() ?? "null");
                return Json(new { success = false, message = "An error occurred while approving the submission" });
            }
        }

        /// <summary>
        /// POST: /Admin/RejectSubmission/{submissionId}
        /// Rejects a submission - requires Admin role and additional authorization checks
        /// </summary>
        [HttpPost("RejectSubmission/{submissionId}")]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> RejectSubmission(int submissionId, [FromBody] RejectSubmissionRequest request)
        {
            // Log incoming request details for debugging
            _logger.LogInformation("RejectSubmission called - SubmissionId: {SubmissionId}, User: {User}, Reason: {Reason}, ContentType: {ContentType}",
                submissionId, User.Identity?.Name, request?.Reason ?? "null", Request.ContentType);

            try
            {
                // Enhanced authorization checks
                var userEmail = User.Identity?.Name ?? "Unknown Admin";
                var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                var reason = request?.Reason ?? "No reason provided";

                // Validate rejection reason
                if (string.IsNullOrWhiteSpace(reason) || reason == "No reason provided")
                {
                    _logger.LogWarning("Rejection attempted without valid reason for submission {SubmissionId} by user {UserId}", submissionId, userEmail);
                    return Json(new { success = false, message = "A valid reason for rejection is required" });
                }

                // Verify submission exists and admin has access
                var submission = await _context.StudentInfos.FindAsync(submissionId);
                if (submission == null)
                {
                    _logger.LogWarning("Rejection attempted for non-existent submission {SubmissionId} by user {UserId}", submissionId, userEmail);
                    return Json(new { success = false, message = "Submission not found" });
                }

                // Check if submission is already rejected
                if (submission.Status == SubmissionStatus.Rejected)
                {
                    _logger.LogWarning("Rejection attempted for already rejected submission {SubmissionId} by user {UserId}", submissionId, userEmail);
                    return Json(new { success = false, message = "Submission is already rejected" });
                }

                // Check if submission is already approved (may want to prevent this)
                if (submission.Status == SubmissionStatus.Approved)
                {
                    _logger.LogWarning("Rejection attempted for approved submission {SubmissionId} by user {UserId}", submissionId, userEmail);
                    return Json(new { success = false, message = "Cannot reject an approved submission" });
                }

                // Log the rejection attempt for audit trail
                _logger.LogInformation("Submission rejection initiated for {SubmissionId} by admin {UserId} ({UserEmail}) with reason: {Reason}",
                    submissionId, userId, userEmail, reason);

                var result = await _submissionApprovalService.RejectSubmissionAsync(submissionId, reason, userEmail);

                if (result.Success)
                {
                    // Log successful rejection
                    _logger.LogInformation("Submission {SubmissionId} successfully rejected by admin {UserId} ({UserEmail}) with reason: {Reason}",
                        submissionId, userId, userEmail, reason);

                    // Log admin access for audit trail
                    await LogAdminAccessAsync(submissionId, "RejectSubmission",
                        $"Submission rejected by admin {userEmail}. Reason: {reason}. Previous status: {result.PreviousStatus}, New status: {result.NewStatus}");

                    return Json(new { success = true, message = result.Details ?? "Submission rejected successfully" });
                }
                else
                {
                    // Log failed rejection
                    _logger.LogWarning("Failed to reject submission {SubmissionId} by admin {UserId} ({UserEmail}): {Error}",
                        submissionId, userId, userEmail, result.ErrorMessage);

                    return Json(new { success = false, message = result.ErrorMessage ?? "Failed to reject submission" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting submission {SubmissionId} by user {UserEmail}. Request: {Request}",
                    submissionId, User.Identity?.Name, request != null ? $"Reason: {request.Reason}" : "null");
                return Json(new { success = false, message = "An error occurred while rejecting the submission" });
            }
        }

        /// <summary>
        /// POST: /Admin/FlagForReview/{submissionId}
        /// Flags a submission for review - requires Admin role and additional authorization checks
        /// </summary>
        [HttpPost("FlagForReview/{submissionId}")]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> FlagForReview(int submissionId, [FromBody] FlagForReviewRequest request)
        {
            // Log incoming request details for debugging
            _logger.LogInformation("FlagForReview called - SubmissionId: {SubmissionId}, User: {User}, Notes: {Notes}, ContentType: {ContentType}",
                submissionId, User.Identity?.Name, request?.Notes ?? "null", Request.ContentType);

            try
            {
                // Enhanced authorization checks
                var userEmail = User.Identity?.Name ?? "Unknown Admin";
                var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                var notes = request?.Notes;

                // Verify submission exists and admin has access
                var submission = await _context.StudentInfos.FindAsync(submissionId);
                if (submission == null)
                {
                    _logger.LogWarning("Flag attempted for non-existent submission {SubmissionId} by user {UserId}", submissionId, userEmail);
                    return Json(new { success = false, message = "Submission not found" });
                }

                // Check if submission is already flagged
                if (submission.Status == SubmissionStatus.Flagged)
                {
                    _logger.LogWarning("Flag attempted for already flagged submission {SubmissionId} by user {UserId}", submissionId, userEmail);
                    return Json(new { success = false, message = "Submission is already flagged for review" });
                }

                // Check if submission is already approved (may want to allow flagging for re-review)
                if (submission.Status == SubmissionStatus.Approved)
                {
                    _logger.LogInformation("Flag attempted for approved submission {SubmissionId} by user {UserId} - allowing for re-review", submissionId, userEmail);
                }

                // Log the flag attempt for audit trail
                _logger.LogInformation("Submission flag initiated for {SubmissionId} by admin {UserId} ({UserEmail}) with notes: {Notes}",
                    submissionId, userId, userEmail, notes ?? "No notes provided");

                var result = await _submissionApprovalService.FlagSubmissionAsync(submissionId, notes, userEmail);

                if (result.Success)
                {
                    // Log successful flag
                    _logger.LogInformation("Submission {SubmissionId} successfully flagged by admin {UserId} ({UserEmail}) with notes: {Notes}",
                        submissionId, userId, userEmail, notes ?? "No notes provided");

                    // Log admin access for audit trail
                    await LogAdminAccessAsync(submissionId, "FlagForReview",
                        $"Submission flagged for review by admin {userEmail}. Notes: {notes ?? "No notes provided"}. Previous status: {result.PreviousStatus}, New status: {result.NewStatus}");

                    return Json(new { success = true, message = result.Details ?? "Submission flagged for review" });
                }
                else
                {
                    // Log failed flag
                    _logger.LogWarning("Failed to flag submission {SubmissionId} by admin {UserId} ({UserEmail}): {Error}",
                        submissionId, userId, userEmail, result.ErrorMessage);

                    return Json(new { success = false, message = result.ErrorMessage ?? "Failed to flag submission" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error flagging submission {SubmissionId} by user {UserEmail}. Request: {Request}",
                    submissionId, User.Identity?.Name, request != null ? $"Notes: {request.Notes}" : "null");
                return Json(new { success = false, message = "An error occurred while flagging the submission" });
            }
        }



        #endregion

        #region Related Submissions

        /// <summary>
        /// GET: /Admin/FindRelatedSubmissions
        /// Finds submissions related to a Civil ID
        /// </summary>
        [HttpGet("FindRelatedSubmissions")]

        public async Task<IActionResult> FindRelatedSubmissions(string civilId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(civilId))
                {
                    return BadRequest("Civil ID is required");
                }

                // Find all submissions with the same Civil ID
                var relatedSubmissions = await _context.StudentInfos
                    .Include(s => s.SisStudent)
                    .Where(s => s.SisStudent != null && s.SisStudent.NationalID == civilId)
                    .OrderByDescending(s => s.CreatedDate)
                    .Select(s => new
                    {
                        Id = s.Id,
                        StudentName = s.GetStudentName(),
                        SubmissionDate = s.CreatedDate.ToString("MMM dd, yyyy HH:mm"),
                        CivilId = s.GetStudentCivilId()
                    })
                    .ToListAsync();

                // Log admin action
                await LogAdminAccessAsync(0, "FindRelatedSubmissions", $"Admin searched for related submissions for Civil ID: {civilId}");

                return Ok(new
                {
                    CivilId = civilId,
                    Submissions = relatedSubmissions
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding related submissions for Civil ID {CivilId}", civilId);
                return StatusCode(500, "Error finding related submissions");
            }
        }

        #endregion

        #region Helper Methods for Export

        /// <summary>
        /// Gets document information for export (different from view model)
        /// </summary>
        private async Task<ViewModels.Admin.DocumentInfo> GetDocumentInfoForExportAsync(StudentInfo submission)
        {
            var documentInfo = new ViewModels.Admin.DocumentInfo();

            // Define document types and their properties
            var documentTypes = new[]
            {
                new { Type = "StudentBirthCertificate", DisplayName = "Student Birth Certificate / شهادة ميلاد الطالب", Category = "Student", IsRequired = true },
                new { Type = "FatherDeathCertificate", DisplayName = "Father Death Certificate / شهادة وفاة الأب", Category = "Father", IsRequired = submission.FatherIsDeceased },
                new { Type = "MotherDeathCertificate", DisplayName = "Mother Death Certificate / شهادة وفاة الأم", Category = "Mother", IsRequired = submission.MotherIsDeceased }
            };

            foreach (var docType in documentTypes)
            {
                var filePath = GetDocumentFilePath(submission.GetStudentCivilId(), docType.Type);
                var fileExists = !string.IsNullOrEmpty(filePath) && System.IO.File.Exists(filePath);
                var fileInfo = fileExists ? new FileInfo(filePath) : null;

                documentInfo.Documents.Add(new ViewModels.Admin.DocumentDetail
                {
                    DocumentType = docType.Type,
                    DisplayName = docType.DisplayName,
                    Category = docType.Category,
                    IsRequired = docType.IsRequired,
                    IsUploaded = fileExists,
                    FilePath = filePath,
                    FileExists = fileExists,
                    FileName = fileInfo?.Name,
                    FileSizeBytes = fileInfo?.Length ?? 0,
                    FormattedFileSize = fileInfo != null ? FormatFileSize(fileInfo.Length) : "N/A",
                    UploadDate = fileInfo?.CreationTime
                });
            }

            return documentInfo;
        }

        /// <summary>
        /// Gets SIS integration status for export (different from view model)
        /// </summary>
        private async Task<ViewModels.Admin.SisIntegrationStatus> GetSisIntegrationStatusForExportAsync(StudentInfo submission)
        {
            var sisIntegrationStatus = new ViewModels.Admin.SisIntegrationStatus
            {
                IsIntegrated = submission.SisStudent != null,
                IsLinkedToSis = submission.SisStudent != null
            };

            if (submission.SisStudent != null)
            {
                sisIntegrationStatus.SisStudentId = submission.SisStudent.Id.ToString();
                sisIntegrationStatus.SisFullNameEN = submission.SisStudent.FullNameEN;
                sisIntegrationStatus.SisFullNameAR = submission.SisStudent.FullNameAR;
                sisIntegrationStatus.LastSyncDate = submission.SisStudent.LastSyncDate;

                // Determine which fields were pre-filled from SIS
                sisIntegrationStatus.PreFilledFields = new List<string>();
                if (!string.IsNullOrEmpty(submission.SisStudent.FullNameEN))
                    sisIntegrationStatus.PreFilledFields.Add("StudentName");
                if (!string.IsNullOrEmpty(submission.SisStudent.NationalID))
                    sisIntegrationStatus.PreFilledFields.Add("StudentCivilId");
            }

            return sisIntegrationStatus;
        }

        /// <summary>
        /// Gets the file path for a document
        /// </summary>
        private string GetDocumentFilePath(string civilId, string documentType)
        {
            // This should match your file upload service logic
            var uploadsPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "uploads", civilId);

            if (!Directory.Exists(uploadsPath))
                return string.Empty;

            // Look for files with the document type prefix
            var files = Directory.GetFiles(uploadsPath, $"{documentType}.*");
            return files.FirstOrDefault() ?? string.Empty;
        }



        #endregion

        #region Document Approval Management

        /// <summary>
        /// POST: /Admin/ApproveDocument
        /// Approves a specific document for a submission
        /// </summary>
        [HttpPost("ApproveDocument")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ApproveDocument([FromBody] ApproveDocumentRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Get current user email (for now, we'll use email instead of user ID to avoid FK issues)
                var userEmail = User.Identity?.Name ?? "Unknown";

                if (string.IsNullOrEmpty(userEmail) || userEmail == "Unknown")
                {
                    _logger.LogWarning("Could not determine user email for document approval");
                    return Json(new { success = false, message = "User authentication error" });
                }

                _logger.LogInformation("Document approval requested by user {UserEmail} for submission {SubmissionId}, document {DocumentType}",
                    userEmail, request.SubmissionId, request.DocumentType);

                // Validate submission exists
                var submission = await _context.StudentInfos.FindAsync(request.SubmissionId);
                if (submission == null)
                {
                    return NotFound("Submission not found");
                }

                // Approve the document (pass null for ApprovedByUserId to avoid FK constraint issues)
                var approval = await _documentApprovalService.ApproveDocumentAsync(
                    request.SubmissionId,
                    request.DocumentType,
                    null, // We'll store the user email in comments or logs instead
                    $"Approved by: {userEmail}" + (string.IsNullOrEmpty(request.Comments) ? "" : $" - {request.Comments}"));

                // Log admin action
                await LogAdminAccessAsync(request.SubmissionId, "ApproveDocument",
                    $"Admin approved document: {request.DocumentType}");

                return Json(new
                {
                    success = true,
                    message = "Document approved successfully",
                    approval = new
                    {
                        id = approval.Id,
                        status = approval.Status.ToString(),
                        approvalDate = approval.ApprovalDate,
                        comments = approval.Comments
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving document {DocumentType} for submission {SubmissionId}",
                    request.DocumentType, request.SubmissionId);
                return StatusCode(500, "Error approving document");
            }
        }

        /// <summary>
        /// POST: /Admin/DisapproveDocument
        /// Disapproves a specific document for a submission with reason
        /// </summary>
        [HttpPost("DisapproveDocument")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DisapproveDocument([FromBody] DisapproveDocumentRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                // Get current user email (for now, we'll use email instead of user ID to avoid FK issues)
                var userEmail = User.Identity?.Name ?? "Unknown";

                if (string.IsNullOrEmpty(userEmail) || userEmail == "Unknown")
                {
                    _logger.LogWarning("Could not determine user email for document disapproval");
                    return Json(new { success = false, message = "User authentication error" });
                }

                _logger.LogInformation("Document disapproval requested by user {UserEmail} for submission {SubmissionId}, document {DocumentType}",
                    userEmail, request.SubmissionId, request.DocumentType);

                // Validate submission exists
                var submission = await _context.StudentInfos.FindAsync(request.SubmissionId);
                if (submission == null)
                {
                    return NotFound("Submission not found");
                }

                // Disapprove the document (pass null for ApprovedByUserId to avoid FK constraint issues)
                var approval = await _documentApprovalService.DisapproveDocumentAsync(
                    request.SubmissionId,
                    request.DocumentType,
                    null, // We'll store the user email in comments instead
                    request.Reason,
                    $"Disapproved by: {userEmail}");

                // Log admin action
                await LogAdminAccessAsync(request.SubmissionId, "DisapproveDocument",
                    $"Admin disapproved document: {request.DocumentType}, Reason: {request.Reason}");

                return Json(new
                {
                    success = true,
                    message = "Document disapproved and notification queued for sending",
                    approval = new
                    {
                        id = approval.Id,
                        status = approval.Status.ToString(),
                        approvalDate = approval.ApprovalDate,
                        disapprovalReason = approval.DisapprovalReason,
                        comments = approval.Comments
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disapproving document {DocumentType} for submission {SubmissionId}",
                    request.DocumentType, request.SubmissionId);
                return StatusCode(500, "Error disapproving document");
            }
        }

        /// <summary>
        /// GET: /Admin/DocumentApprovalStatus/{submissionId}/{documentType}
        /// Gets the current approval status for a specific document
        /// </summary>
        [HttpGet("DocumentApprovalStatus/{submissionId}/{documentType}")]
        public async Task<IActionResult> DocumentApprovalStatus(int submissionId, string documentType)
        {
            try
            {
                var approval = await _documentApprovalService.GetDocumentApprovalAsync(submissionId, documentType);

                if (approval == null)
                {
                    return Json(new
                    {
                        status = "Pending",
                        statusText = "Pending Review",
                        hasApproval = false
                    });
                }

                return Json(new
                {
                    status = approval.Status.ToString(),
                    statusText = approval.GetStatusDisplayText(),
                    hasApproval = true,
                    approvalDate = approval.ApprovalDate,
                    approvedBy = approval.ApprovedByUser?.UserName,
                    disapprovalReason = approval.DisapprovalReason,
                    comments = approval.Comments,
                    badgeClass = approval.GetStatusBadgeClass(),
                    iconClass = approval.GetStatusIconClass()
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting approval status for document {DocumentType} in submission {SubmissionId}",
                    documentType, submissionId);
                return StatusCode(500, "Error retrieving approval status");
            }
        }

        /// <summary>
        /// GET: /Admin/DocumentApprovalHistory/{submissionId}/{documentType}
        /// Gets the approval history for a specific document
        /// </summary>
        [HttpGet("DocumentApprovalHistory/{submissionId}/{documentType}")]
        public async Task<IActionResult> DocumentApprovalHistory(int submissionId, string documentType)
        {
            try
            {
                var history = await _documentApprovalService.GetDocumentApprovalHistoryAsync(submissionId, documentType);

                var historyData = history.Select(h => new
                {
                    id = h.Id,
                    status = h.Status.ToString(),
                    statusText = h.GetStatusDisplayText(),
                    approvalDate = h.ApprovalDate,
                    approvedBy = h.ApprovedByUser?.UserName ?? "System",
                    disapprovalReason = h.DisapprovalReason,
                    comments = h.Comments,
                    createdDate = h.CreatedDate,
                    badgeClass = h.GetStatusBadgeClass(),
                    iconClass = h.GetStatusIconClass()
                }).ToList();

                return Json(new
                {
                    success = true,
                    history = historyData
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting approval history for document {DocumentType} in submission {SubmissionId}",
                    documentType, submissionId);
                return StatusCode(500, "Error retrieving approval history");
            }
        }

        #endregion



        #region Email Preview and Batch Notification

        /// <summary>
        /// GET: /Admin/GetEmailPreview/{submissionId}
        /// Gets email preview data for a specific submission (used by ViewSubmission page)
        /// </summary>
        [HttpGet("GetEmailPreview/{submissionId}")]
        public async Task<IActionResult> GetEmailPreview(int submissionId, bool forceDefault = false)
        {
            try
            {
                var emailPreview = await _emailPreviewService.GenerateEmailPreviewAsync(submissionId);

                // If forceDefault is requested, clear custom content to show only defaults
                if (forceDefault)
                {
                    emailPreview.CustomSubject = null;
                    emailPreview.CustomBody = null;
                }

                return Json(new
                {
                    success = true,
                    data = emailPreview
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting email preview for submission {SubmissionId}", submissionId);
                return Json(new
                {
                    success = false,
                    message = "Error loading email preview"
                });
            }
        }

        /// <summary>
        /// POST: /Admin/UpdateEmailContent
        /// Updates custom email content for a submission
        /// </summary>
        [HttpPost("UpdateEmailContent")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateEmailContent([FromBody] ViewModels.Admin.EmailContentUpdateRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return Json(new
                    {
                        success = false,
                        message = "Invalid request data"
                    });
                }

                var updatedPreview = await _emailPreviewService.UpdateEmailContentAsync(
                    request.SubmissionId,
                    request.Subject,
                    request.Body);

                return Json(new
                {
                    success = true,
                    message = "Email content updated successfully",
                    data = updatedPreview
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating email content for submission {SubmissionId}", request.SubmissionId);
                return Json(new
                {
                    success = false,
                    message = "Error updating email content"
                });
            }
        }

        /// <summary>
        /// POST: /Admin/SendBatchEmail
        /// Sends batch email notification for a submission
        /// </summary>
        [HttpPost("SendBatchEmail")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SendBatchEmail(int submissionId, string? customSubject = null, string? customBody = null)
        {
            try
            {
                var userEmail = User.Identity?.Name ?? "Unknown";
                _logger.LogInformation("Batch email send requested by user {UserEmail} for submission {SubmissionId}",
                    userEmail, submissionId);

                var emailNotification = await _emailPreviewService.SendBatchEmailNotificationAsync(
                    submissionId, customSubject, customBody);

                // Log admin action
                await LogAdminAccessAsync(submissionId, "SendBatchEmail",
                    $"Admin sent batch email notification to student");

                return Json(new
                {
                    success = true,
                    message = "Email notification sent successfully",
                    emailId = emailNotification.Id,
                    sentDate = emailNotification.SentDate
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending batch email for submission {SubmissionId}", submissionId);
                return Json(new
                {
                    success = false,
                    message = "Error sending email notification"
                });
            }
        }

        /// <summary>
        /// POST: /Admin/SendBulkEmails
        /// Sends bulk email notifications for multiple submissions
        /// </summary>
        [HttpPost("SendBulkEmails")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SendBulkEmails([FromBody] ViewModels.Admin.BulkEmailSendRequest request)
        {
            try
            {
                if (!ModelState.IsValid || !request.SubmissionIds.Any())
                {
                    return Json(new
                    {
                        success = false,
                        message = "Invalid request data"
                    });
                }

                var userEmail = User.Identity?.Name ?? "Unknown";
                _logger.LogInformation("Bulk email send requested by user {UserEmail} for {Count} submissions",
                    userEmail, request.SubmissionIds.Length);

                var emailNotifications = await _emailPreviewService.SendBulkEmailNotificationsAsync(request.SubmissionIds);

                // Log admin actions
                foreach (var submissionId in request.SubmissionIds)
                {
                    await LogAdminAccessAsync(submissionId, "SendBulkEmail",
                        $"Admin sent bulk email notification to student");
                }

                var results = emailNotifications.Select(en => new ViewModels.Admin.EmailSendResult
                {
                    SubmissionId = en.SubmissionId,
                    Success = en.Status == EmailStatus.Sent,
                    Message = en.Status == EmailStatus.Sent ? "Email sent successfully" : en.FailureReason ?? "Unknown error",
                    EmailNotificationId = en.Id,
                    SentDate = en.SentDate ?? DateTime.UtcNow
                }).ToList();

                return Json(new
                {
                    success = true,
                    message = $"Bulk email operation completed. {results.Count(r => r.Success)} of {results.Count} emails sent successfully.",
                    results = results
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending bulk emails");
                return Json(new
                {
                    success = false,
                    message = "Error sending bulk email notifications"
                });
            }
        }

        /// <summary>
        /// POST: /Admin/SendBatchEmails
        /// Sends batch email notifications for specified submissions (used by ViewSubmission page)
        /// </summary>
        [HttpPost("SendBatchEmails")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> SendBatchEmails([FromBody] SendBatchEmailsRequest request)
        {
            try
            {
                if (request?.SubmissionIds == null || !request.SubmissionIds.Any())
                {
                    return Json(new
                    {
                        success = false,
                        message = "No submissions specified for email sending"
                    });
                }

                var userEmail = User.Identity?.Name ?? "Unknown";
                _logger.LogInformation("Batch email send requested by user {UserEmail} for {Count} submissions",
                    userEmail, request.SubmissionIds.Count);

                var results = new List<object>();
                var successCount = 0;

                foreach (var submissionId in request.SubmissionIds)
                {
                    try
                    {
                        var emailNotification = await _emailPreviewService.SendBatchEmailNotificationAsync(submissionId);

                        // Log admin action
                        await LogAdminAccessAsync(submissionId, "SendBatchEmail",
                            $"Admin sent batch email notification to student");

                        results.Add(new
                        {
                            submissionId = submissionId,
                            success = true,
                            message = "Email sent successfully",
                            emailId = emailNotification.Id
                        });
                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error sending email for submission {SubmissionId}", submissionId);
                        results.Add(new
                        {
                            submissionId = submissionId,
                            success = false,
                            message = ex.Message
                        });
                    }
                }

                return Json(new
                {
                    success = successCount > 0,
                    message = $"{successCount} of {request.SubmissionIds.Count} emails sent successfully",
                    emailsSent = successCount,
                    errors = results.Where(r => !(bool)((dynamic)r).success).ToList(),
                    results = results
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending batch emails");
                return Json(new
                {
                    success = false,
                    message = "Error sending email notifications"
                });
            }
        }

        /// <summary>
        /// GET: /Admin/GetEmailStats
        /// Gets email notification statistics for dashboard
        /// </summary>
        [HttpGet("GetEmailStats")]
        public async Task<IActionResult> GetEmailStats()
        {
            try
            {
                var pendingSubmissions = await _emailPreviewService.GetSubmissionsWithPendingEmailsAsync();

                var stats = new
                {
                    pendingCount = pendingSubmissions.Count,
                    disapprovedDocsCount = pendingSubmissions.Sum(s => s.DisapprovedDocumentCount),
                    lastUpdate = DateTime.UtcNow
                };

                return Json(stats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting email statistics");
                return Json(new
                {
                    pendingCount = 0,
                    disapprovedDocsCount = 0,
                    error = "Error loading statistics"
                });
            }
        }



        #endregion

        #region Request Models

        /// <summary>
        /// Request model for rejecting a submission
        /// </summary>
        public class RejectSubmissionRequest
        {
            public string Reason { get; set; } = string.Empty;
        }

        /// <summary>
        /// Request model for flagging a submission for review
        /// </summary>
        public class FlagForReviewRequest
        {
            public string Notes { get; set; } = string.Empty;
        }

        /// <summary>
        /// Request model for approving a document
        /// </summary>
        public class ApproveDocumentRequest
        {
            [Required]
            public int SubmissionId { get; set; }

            [Required]
            [StringLength(100)]
            public string DocumentType { get; set; } = string.Empty;

            [StringLength(2000)]
            public string? Comments { get; set; }
        }

        /// <summary>
        /// Request model for disapproving a document
        /// </summary>
        public class DisapproveDocumentRequest
        {
            [Required]
            public int SubmissionId { get; set; }

            [Required]
            [StringLength(100)]
            public string DocumentType { get; set; } = string.Empty;

            [Required]
            [StringLength(1000)]
            public string Reason { get; set; } = string.Empty;

            [StringLength(2000)]
            public string? Comments { get; set; }
        }

        /// <summary>
        /// Request model for sending batch emails
        /// </summary>
        public class SendBatchEmailsRequest
        {
            [Required]
            public List<int> SubmissionIds { get; set; } = new List<int>();
        }

        #endregion
    }
}
