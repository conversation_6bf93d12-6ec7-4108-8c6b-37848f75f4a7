using Forms.ktech.Data;
using Forms.ktech.Features.KuwaitiStudentInfo.Models;
using Forms.ktech.Models;
using Forms.ktech.ViewModels.Admin;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Forms.ktech.Services
{
    /// <summary>
    /// Service for handling submission approval operations
    /// </summary>
    public class SubmissionApprovalService : ISubmissionApprovalService
    {
        private readonly FormsKTechContext _context;
        private readonly ILogger<SubmissionApprovalService> _logger;
        private readonly ISubmissionNotificationService _submissionNotificationService;

        public SubmissionApprovalService(
            FormsKTechContext context,
            ILogger<SubmissionApprovalService> logger,
            ISubmissionNotificationService submissionNotificationService)
        {
            _context = context;
            _logger = logger;
            _submissionNotificationService = submissionNotificationService;
        }

        /// <summary>
        /// Approves a submission
        /// </summary>
        public async Task<SubmissionApprovalResult> ApproveSubmissionAsync(int submissionId, string? approvedByUserId = null)
        {
            try
            {
                var submission = await _context.StudentInfos.FindAsync(submissionId);
                if (submission == null)
                {
                    return SubmissionApprovalResult.CreateFailure(submissionId, "Submission not found");
                }

                // Validate submission can be approved
                var validationResult = await ValidateSubmissionForApprovalAsync(submissionId);
                if (!validationResult.IsValid)
                {
                    return SubmissionApprovalResult.CreateFailure(submissionId, 
                        $"Submission cannot be approved: {string.Join(", ", validationResult.Issues)}");
                }

                var previousStatus = submission.Status;
                
                // Approve the submission
                submission.Approve(approvedByUserId);
                
                await _context.SaveChangesAsync();

                // Send approval notification
                try
                {
                    await _submissionNotificationService.SendApprovalNotificationAsync(submissionId);
                }
                catch (Exception notificationEx)
                {
                    _logger.LogWarning(notificationEx, "Failed to send approval notification for submission {SubmissionId}", submissionId);
                }

                _logger.LogInformation("Submission {SubmissionId} approved by {UserId}", submissionId, approvedByUserId);

                return SubmissionApprovalResult.CreateSuccess(submissionId, previousStatus, submission.Status,
                    "Submission approved successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving submission {SubmissionId}", submissionId);
                return SubmissionApprovalResult.CreateFailure(submissionId, "An error occurred while approving the submission");
            }
        }

        /// <summary>
        /// Rejects a submission
        /// </summary>
        public async Task<SubmissionApprovalResult> RejectSubmissionAsync(int submissionId, string reason, string? rejectedByUserId = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(reason))
                {
                    return SubmissionApprovalResult.CreateFailure(submissionId, "Rejection reason is required");
                }

                var submission = await _context.StudentInfos.FindAsync(submissionId);
                if (submission == null)
                {
                    return SubmissionApprovalResult.CreateFailure(submissionId, "Submission not found");
                }

                var previousStatus = submission.Status;
                
                // Reject the submission
                submission.Reject(reason, rejectedByUserId);
                
                await _context.SaveChangesAsync();

                // Send rejection notification
                try
                {
                    await _submissionNotificationService.SendRejectionNotificationAsync(submissionId, reason);
                }
                catch (Exception notificationEx)
                {
                    _logger.LogWarning(notificationEx, "Failed to send rejection notification for submission {SubmissionId}", submissionId);
                }

                _logger.LogInformation("Submission {SubmissionId} rejected by {UserId} with reason: {Reason}",
                    submissionId, rejectedByUserId, reason);

                return SubmissionApprovalResult.CreateSuccess(submissionId, previousStatus, submission.Status,
                    $"Submission rejected: {reason}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting submission {SubmissionId}", submissionId);
                return SubmissionApprovalResult.CreateFailure(submissionId, "An error occurred while rejecting the submission");
            }
        }

        /// <summary>
        /// Flags a submission for review
        /// </summary>
        public async Task<SubmissionApprovalResult> FlagSubmissionAsync(int submissionId, string? notes = null, string? flaggedByUserId = null)
        {
            try
            {
                var submission = await _context.StudentInfos.FindAsync(submissionId);
                if (submission == null)
                {
                    return SubmissionApprovalResult.CreateFailure(submissionId, "Submission not found");
                }

                var previousStatus = submission.Status;
                
                // Flag the submission
                submission.Flag(notes, flaggedByUserId);
                
                await _context.SaveChangesAsync();

                // Send flagged notification (internal only)
                try
                {
                    await _submissionNotificationService.SendFlaggedNotificationAsync(submissionId, notes);
                }
                catch (Exception notificationEx)
                {
                    _logger.LogWarning(notificationEx, "Failed to process flagged notification for submission {SubmissionId}", submissionId);
                }

                _logger.LogInformation("Submission {SubmissionId} flagged by {UserId} with notes: {Notes}",
                    submissionId, flaggedByUserId, notes ?? "No notes");

                return SubmissionApprovalResult.CreateSuccess(submissionId, previousStatus, submission.Status,
                    $"Submission flagged for review{(string.IsNullOrEmpty(notes) ? "" : $": {notes}")}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error flagging submission {SubmissionId}", submissionId);
                return SubmissionApprovalResult.CreateFailure(submissionId, "An error occurred while flagging the submission");
            }
        }

        /// <summary>
        /// Gets submission statistics by status
        /// </summary>
        public async Task<SubmissionStatusStatistics> GetSubmissionStatisticsAsync()
        {
            try
            {
                var statistics = await _context.StudentInfos
                    .GroupBy(s => s.Status)
                    .Select(g => new { Status = g.Key, Count = g.Count() })
                    .ToListAsync();

                return new SubmissionStatusStatistics
                {
                    PendingCount = statistics.FirstOrDefault(s => s.Status == SubmissionStatus.Pending)?.Count ?? 0,
                    ApprovedCount = statistics.FirstOrDefault(s => s.Status == SubmissionStatus.Approved)?.Count ?? 0,
                    RejectedCount = statistics.FirstOrDefault(s => s.Status == SubmissionStatus.Rejected)?.Count ?? 0,
                    FlaggedCount = statistics.FirstOrDefault(s => s.Status == SubmissionStatus.Flagged)?.Count ?? 0
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submission statistics");
                return new SubmissionStatusStatistics();
            }
        }

        /// <summary>
        /// Validates if a submission can be approved
        /// </summary>
        public async Task<SubmissionValidationResult> ValidateSubmissionForApprovalAsync(int submissionId)
        {
            var result = new SubmissionValidationResult { IsValid = true };

            try
            {
                var submission = await _context.StudentInfos
                    .FirstOrDefaultAsync(s => s.Id == submissionId);

                if (submission == null)
                {
                    result.IsValid = false;
                    result.Issues.Add("Submission not found");
                    return result;
                }

                // Check if submission is already approved
                if (submission.Status == SubmissionStatus.Approved)
                {
                    result.IsValid = false;
                    result.Issues.Add("Submission is already approved");
                }

                // Check if student is eligible
                if (!submission.IsEligible())
                {
                    result.Warnings.Add("Student is not eligible for the program (Case D)");
                }

                // Check required documents
                var requiredDocuments = new[]
                {
                    "StudentBirthCertificate",
                    "StudentCivilId",
                    "FatherCivilId",
                    "MotherCivilId"
                };

                // Check for required document paths
                var requiredDocumentPaths = new Dictionary<string, string?>
                {
                    { "Student Birth Certificate", submission.StudentBirthCertificatePath },
                    { "Student Civil ID", submission.StudentCivilIdPath },
                    { "Father Civil ID", submission.FatherCivilIdPath },
                    { "Mother Civil ID", submission.MotherCivilIdPath }
                };

                foreach (var docPath in requiredDocumentPaths)
                {
                    if (string.IsNullOrEmpty(docPath.Value))
                    {
                        result.Warnings.Add($"Missing required document: {docPath.Key}");
                    }
                }

                // Check for document approvals in the database
                var documentApprovals = await _context.DocumentApprovals
                    .Where(da => da.SubmissionId == submissionId)
                    .ToListAsync();

                var disapprovedDocs = documentApprovals.Where(da => da.Status == DocumentApprovalStatus.Disapproved).ToList();
                if (disapprovedDocs.Any())
                {
                    result.Warnings.Add($"Submission has {disapprovedDocs.Count} disapproved documents");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating submission {SubmissionId}", submissionId);
                result.IsValid = false;
                result.Issues.Add("Error occurred during validation");
            }

            return result;
        }

        /// <summary>
        /// Gets submission history for audit purposes
        /// </summary>
        public async Task<List<SubmissionStatusHistory>> GetSubmissionHistoryAsync(int submissionId)
        {
            try
            {
                var submission = await _context.StudentInfos.FindAsync(submissionId);
                if (submission == null)
                {
                    return new List<SubmissionStatusHistory>();
                }

                var history = new List<SubmissionStatusHistory>();

                // Add creation entry
                history.Add(new SubmissionStatusHistory
                {
                    ChangedDate = submission.CreatedDate,
                    PreviousStatus = null,
                    NewStatus = SubmissionStatus.Pending,
                    ChangedByUserId = submission.CreatedByUserId,
                    Reason = "Submission created",
                    Details = "Initial submission"
                });

                // Add status change entries based on available data
                if (submission.Status == SubmissionStatus.Approved && submission.ApprovedDate.HasValue)
                {
                    history.Add(new SubmissionStatusHistory
                    {
                        ChangedDate = submission.ApprovedDate.Value,
                        PreviousStatus = SubmissionStatus.Pending,
                        NewStatus = SubmissionStatus.Approved,
                        ChangedByUserId = submission.StatusChangedByUserId,
                        Reason = "Submission approved",
                        Details = "Approved by admin"
                    });
                }
                else if (submission.Status == SubmissionStatus.Rejected && submission.RejectedDate.HasValue)
                {
                    history.Add(new SubmissionStatusHistory
                    {
                        ChangedDate = submission.RejectedDate.Value,
                        PreviousStatus = SubmissionStatus.Pending,
                        NewStatus = SubmissionStatus.Rejected,
                        ChangedByUserId = submission.StatusChangedByUserId,
                        Reason = submission.RejectionReason ?? "No reason provided",
                        Details = "Rejected by admin"
                    });
                }
                else if (submission.Status == SubmissionStatus.Flagged && submission.FlaggedDate.HasValue)
                {
                    history.Add(new SubmissionStatusHistory
                    {
                        ChangedDate = submission.FlaggedDate.Value,
                        PreviousStatus = SubmissionStatus.Pending,
                        NewStatus = SubmissionStatus.Flagged,
                        ChangedByUserId = submission.StatusChangedByUserId,
                        Reason = submission.FlagNotes ?? "No notes provided",
                        Details = "Flagged for review by admin"
                    });
                }

                return history.OrderBy(h => h.ChangedDate).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submission history for {SubmissionId}", submissionId);
                return new List<SubmissionStatusHistory>();
            }
        }

        /// <summary>
        /// Bulk approve multiple submissions
        /// </summary>
        public async Task<List<SubmissionApprovalResult>> BulkApproveSubmissionsAsync(List<int> submissionIds, string? approvedByUserId = null)
        {
            var results = new List<SubmissionApprovalResult>();

            foreach (var submissionId in submissionIds)
            {
                var result = await ApproveSubmissionAsync(submissionId, approvedByUserId);
                results.Add(result);
            }

            return results;
        }

        /// <summary>
        /// Bulk reject multiple submissions
        /// </summary>
        public async Task<List<SubmissionApprovalResult>> BulkRejectSubmissionsAsync(List<int> submissionIds, string reason, string? rejectedByUserId = null)
        {
            var results = new List<SubmissionApprovalResult>();

            foreach (var submissionId in submissionIds)
            {
                var result = await RejectSubmissionAsync(submissionId, reason, rejectedByUserId);
                results.Add(result);
            }

            return results;
        }
    }
}
