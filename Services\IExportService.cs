using Forms.ktech.Models;
using Forms.ktech.ViewModels.Admin;
using Forms.ktech.Features.KuwaitiStudentInfo.Models;

namespace Forms.ktech.Services
{
    /// <summary>
    /// Service for exporting submission data to various formats
    /// </summary>
    public interface IExportService
    {
        /// <summary>
        /// Exports a single submission to PDF format
        /// </summary>
        /// <param name="submission">The submission to export</param>
        /// <param name="documentInfo">Document information for the submission</param>
        /// <param name="sisIntegrationStatus">SIS integration status</param>
        /// <returns>PDF file as byte array</returns>
        Task<byte[]> ExportSubmissionToPdfAsync(StudentInfo submission, DocumentInfo documentInfo, SisIntegrationStatus sisIntegrationStatus);

        /// <summary>
        /// Exports a single submission to Excel format
        /// </summary>
        /// <param name="submission">The submission to export</param>
        /// <param name="documentInfo">Document information for the submission</param>
        /// <param name="sisIntegrationStatus">SIS integration status</param>
        /// <returns>Excel file as byte array</returns>
        Task<byte[]> ExportSubmissionToExcelAsync(StudentInfo submission, DocumentInfo documentInfo, SisIntegrationStatus sisIntegrationStatus);

        /// <summary>
        /// Exports multiple submissions to Excel format
        /// </summary>
        /// <param name="submissions">List of submissions to export</param>
        /// <returns>Excel file as byte array</returns>
        Task<byte[]> ExportMultipleSubmissionsToExcelAsync(IEnumerable<StudentInfo> submissions);

        /// <summary>
        /// Generates a secure filename based on Civil ID and export type
        /// </summary>
        /// <param name="civilId">Civil ID for filename generation</param>
        /// <param name="exportType">Type of export (PDF/Excel)</param>
        /// <param name="isMultiple">Whether this is a multiple submission export</param>
        /// <returns>Secure filename</returns>
        string GenerateSecureFilename(string civilId, string exportType, bool isMultiple = false);
    }
}
