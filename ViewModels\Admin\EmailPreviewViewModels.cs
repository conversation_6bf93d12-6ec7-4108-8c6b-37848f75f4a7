using System.ComponentModel.DataAnnotations;

namespace Forms.ktech.ViewModels.Admin
{
    /// <summary>
    /// View model for email preview data
    /// </summary>
    public class EmailPreviewData
    {
        public int SubmissionId { get; set; }
        public string StudentName { get; set; } = string.Empty;
        public string StudentEmail { get; set; } = string.Empty;
        public string CivilId { get; set; } = string.Empty;
        public string DefaultSubject { get; set; } = string.Empty;
        public string DefaultBody { get; set; } = string.Empty;
        public string? CustomSubject { get; set; }
        public string? CustomBody { get; set; }
        public List<DisapprovedDocumentInfo> DisapprovedDocuments { get; set; } = new();
        public DateTime CreatedDate { get; set; }
        public bool HasPendingEmails { get; set; }
    }

    /// <summary>
    /// View model for submission with pending emails
    /// </summary>
    public class EmailPreviewSubmission
    {
        public int SubmissionId { get; set; }
        public string StudentName { get; set; } = string.Empty;
        public string StudentEmail { get; set; } = string.Empty;
        public string CivilId { get; set; } = string.Empty;
        public int DisapprovedDocumentCount { get; set; }
        public DateTime LastDisapprovalDate { get; set; }
        public List<string> DisapprovedDocumentTypes { get; set; } = new();
        public bool HasCustomContent { get; set; }
    }

    /// <summary>
    /// View model for disapproved document information
    /// </summary>
    public class DisapprovedDocumentInfo
    {
        public string DocumentType { get; set; } = string.Empty;
        public string DocumentName { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
        public string? Comments { get; set; }
        public DateTime DisapprovalDate { get; set; }
        public string DisapprovedBy { get; set; } = string.Empty;
        public bool EmailSent { get; set; }
    }

    /// <summary>
    /// View model for email content update request
    /// </summary>
    public class EmailContentUpdateRequest
    {
        [Required]
        public int SubmissionId { get; set; }

        [Required]
        [StringLength(500)]
        public string Subject { get; set; } = string.Empty;

        [Required]
        public string Body { get; set; } = string.Empty;
    }

    /// <summary>
    /// View model for bulk email send request
    /// </summary>
    public class BulkEmailSendRequest
    {
        [Required]
        public int[] SubmissionIds { get; set; } = Array.Empty<int>();

        public bool UseCustomContent { get; set; }
    }

    /// <summary>
    /// View model for email send result
    /// </summary>
    public class EmailSendResult
    {
        public int SubmissionId { get; set; }
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public int EmailNotificationId { get; set; }
        public DateTime SentDate { get; set; }
    }
}
