@model Forms.ktech.Features.KuwaitiStudentInfo.Models.StudentFormViewModel

@{
    ViewData["Title"] = "Kuwaiti Student Information Form";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@section Breadcrumb {
    <li class="inline-flex items-center">
        <a asp-area="" asp-controller="Home" asp-action="Index" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
            <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
            </svg>
            Home
        </a>
    </li>
    <li>
        <div class="flex items-center">
            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
            </svg>
            <span class="text-sm font-medium text-gray-700 dark:text-gray-400">Forms</span>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
            </svg>
            <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400">Kuwaiti Student Information</span>
        </div>
    </li>
}

@section Styles {
    <link rel="stylesheet" href="~/css/shared-forms.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/kuwaiti-student-info.css" asp-append-version="true" />
}

<div>
    <!-- Header Section -->
    <div class="bg-white rounded-lg shadow-lg mb-6 dark:bg-gray-800">
        <div class="bg-blue-600 text-white p-6 rounded-t-lg">
            <div class="flex items-center">
                <i class="fas fa-graduation-cap text-3xl me-4"></i>
                <div>
                    <h1 class="text-2xl font-bold mb-1">Student Information Form / نموذج معلومات الطالب</h1>
                    <p class="text-blue-100">Please provide accurate information / يرجى تقديم معلومات دقيقة</p>
                </div>
            </div>
        </div>
        <div class="p-6">
            <div class="flex items-start p-4 text-sm text-green-800 border border-green-300 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400 dark:border-green-800" role="alert">
                <svg class="flex-shrink-0 inline w-4 h-4 me-3 mt-0.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                </svg>
                <div>
                    <span class="font-medium">Welcome / مرحباً بك</span>
                    <div class="mt-1 text-sm text-green-700 dark:text-green-400">
                        Please fill out this form with accurate information. Required documents will be requested in the next step. / يرجى ملء هذا النموذج بمعلومات دقيقة. سيتم طلب المستندات المطلوبة في الخطوة التالية.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Bar -->
    <div class="bg-white rounded-lg shadow-lg p-6 mb-6 dark:bg-gray-800">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Application Progress</h2>
            <span id="step-indicator" class="text-sm text-gray-500 dark:text-gray-400">Step 1 of 3 / الخطوة ١ من ٣</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
            <div id="progress-bar" class="bg-blue-600 h-2.5 rounded-full transition-all duration-300" style="width: 33.33%"></div>
        </div>
        <div class="flex justify-between mt-2 text-xs text-gray-500 dark:text-gray-400">
            <span>Basic Information / المعلومات الأساسية</span>
            <span>Document Upload / رفع المستندات</span>
            <span>Review & Submit / المراجعة والإرسال</span>
        </div>
    </div>



    <!-- Validation Summary -->
    <div id="validation-summary" class="hidden">
        @{
            ViewData["title"] = "Please correct the following errors before continuing:";
            ViewData["cssClass"] = "mb-6";
        }
        @await Html.PartialAsync("_ValidationSummary")
    </div>

    <!-- Main Wizard Form -->
    <form id="wizard-form" enctype="multipart/form-data" novalidate>
        @Html.AntiForgeryToken()
        <input type="hidden" asp-for="SubmissionGuid" />

        <!-- Step 1: Basic Information -->
        <div id="step-1" class="wizard-step">
            <!-- Family Information Section -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6 dark:bg-gray-800">
                <div class="flex items-center mb-6 pb-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg dark:bg-blue-900 me-3">
                        <i class="fas fa-users text-blue-600 dark:text-blue-300"></i>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Family Information / معلومات العائلة</h2>
                </div>

                <!-- Student Information -->
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <div class="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-lg dark:bg-blue-900 me-3">
                            <i class="fas fa-user text-blue-600 dark:text-blue-300 text-sm"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Student Information / معلومات الطالب</h3>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                        <div class="sis-field-container">
                            <label asp-for="StudentName" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                                Student Name / اسم الطالب <span class="text-red-500">*</span>
                                @if (Model.IsFieldPreFilled(nameof(Model.StudentName)))
                                {
                                    <span class="sis-badge" role="img" aria-label="Pre-filled from Student Information System">
                                        <i class="fas fa-database" aria-hidden="true"></i>
                                        From SIS
                                    </span>
                                }
                            </label>
                            <input asp-for="StudentName"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 @(Model.IsFieldReadOnly(nameof(Model.StudentName)) ? "sis-readonly" : "")"
                                   placeholder="Enter full name"
                                   readonly="@Model.IsFieldReadOnly(nameof(Model.StudentName))"
                                   aria-describedby="@(Model.IsFieldReadOnly(nameof(Model.StudentName)) ? "StudentName-sis-info" : "")" />
                            <span asp-validation-for="StudentName" class="text-sm text-red-600 dark:text-red-500"></span>
                            @if (Model.IsFieldReadOnly(nameof(Model.StudentName)))
                            {
                                <div id="StudentName-sis-info" class="sr-only">
                                    This field has been automatically filled from the Student Information System and cannot be edited.
                                </div>
                            }
                        </div>
                        <div class="sis-field-container">
                            <label asp-for="StudentCivilId" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                                Student Civil ID / نسخه من البطاقة المدنية - من الجهتين <span class="text-red-500">*</span>
                                @if (Model.IsFieldPreFilled(nameof(Model.StudentCivilId)))
                                {
                                    <span class="sis-badge" role="img" aria-label="Pre-filled from Student Information System">
                                        <i class="fas fa-database" aria-hidden="true"></i>
                                        From SIS
                                    </span>
                                }
                            </label>
                            <input asp-for="StudentCivilId"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500 @(Model.IsFieldReadOnly(nameof(Model.StudentCivilId)) ? "sis-readonly" : "")"
                                   placeholder="12-digit Civil ID"
                                   maxlength="12"
                                   readonly="@Model.IsFieldReadOnly(nameof(Model.StudentCivilId))"
                                   aria-describedby="@(Model.IsFieldReadOnly(nameof(Model.StudentCivilId)) ? "StudentCivilId-sis-info" : "")" />
                            <span asp-validation-for="StudentCivilId" class="text-sm text-red-600 dark:text-red-500"></span>
                            @if (Model.IsFieldReadOnly(nameof(Model.StudentCivilId)))
                            {
                                <div id="StudentCivilId-sis-info" class="sr-only">
                                    This field has been automatically filled from the Student Information System and cannot be edited.
                                </div>
                            }
                            @if (!Model.IsFieldReadOnly(nameof(Model.StudentCivilId)))
                            {
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Enter 12-digit Kuwait Civil ID number</p>
                            }
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                        <div>
                            <label asp-for="StudentMobileNumber" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                                Mobile Number / رقم الهاتف المحمول <span class="text-red-500">*</span>
                            </label>
                            <input asp-for="StudentMobileNumber"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                                   placeholder="+965XXXXXXXX or XXXXXXXX"
                                   type="tel" />
                            <span asp-validation-for="StudentMobileNumber" class="text-sm text-red-600 dark:text-red-500"></span>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Enter Kuwait mobile number (starting with 5, 6, or 9)</p>
                        </div>
                        <div></div>
                    </div>

                    <div class="flex items-center">
                        <input asp-for="StudentIsKuwaiti" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 nationality-checkbox" type="checkbox" id="studentKuwaiti" />
                        <label class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300" for="studentKuwaiti">
                            <strong>Student is Kuwaiti citizen / الطالب مواطن كويتي</strong>
                        </label>
                    </div>
                </div>

                <!-- Parents Information -->
                <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                    <div class="flex items-center mb-6">
                        <div class="flex items-center justify-center w-8 h-8 bg-green-100 rounded-lg dark:bg-green-900 me-3">
                            <i class="fas fa-user-friends text-green-600 dark:text-green-300 text-sm"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Parents Information / معلومات الوالدين</h3>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Father Information -->
                        <div class="space-y-4">
                            <div class="flex items-center mb-3">
                                <div class="flex items-center justify-center w-6 h-6 bg-green-100 rounded-lg dark:bg-green-900 me-2">
                                    <i class="fas fa-male text-green-600 dark:text-green-300 text-xs"></i>
                                </div>
                                <h4 class="text-md font-medium text-gray-900 dark:text-white">Father / الأب</h4>
                            </div>

                            <div class="flex items-center">
                                <input asp-for="FatherIsKuwaiti" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 nationality-checkbox" type="checkbox" id="fatherKuwaiti" />
                                <label class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300" for="fatherKuwaiti">
                                    <strong>Father is Kuwaiti citizen / الأب مواطن كويتي</strong>
                                </label>
                            </div>

                            <!-- Deceased Father Checkbox (only visible when father is Kuwaiti) -->
                            <div class="conditional-field hidden" data-condition="father-kuwaiti">
                                <div class="flex items-center">
                                    <input asp-for="FatherIsDeceased" class="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500 dark:focus:ring-red-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 deceased-checkbox" type="checkbox" id="fatherDeceased" />
                                    <label class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300" for="fatherDeceased">
                                        Father is deceased / الأب متوفى
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Mother Information -->
                        <div class="space-y-4">
                            <div class="flex items-center mb-3">
                                <div class="flex items-center justify-center w-6 h-6 bg-purple-100 rounded-lg dark:bg-purple-900 me-2">
                                    <i class="fas fa-female text-purple-600 dark:text-purple-300 text-xs"></i>
                                </div>
                                <h4 class="text-md font-medium text-gray-900 dark:text-white">Mother / الأم</h4>
                            </div>

                            <div class="flex items-center">
                                <input asp-for="MotherIsKuwaiti" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 nationality-checkbox" type="checkbox" id="motherKuwaiti" />
                                <label class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300" for="motherKuwaiti">
                                    <strong>Mother is Kuwaiti citizen / الأم مواطنة كويتية</strong>
                                </label>
                            </div>

                            <!-- Deceased Mother Checkbox (only visible when mother is Kuwaiti) -->
                            <div class="conditional-field hidden" data-condition="mother-kuwaiti">
                                <div class="flex items-center">
                                    <input asp-for="MotherIsDeceased" class="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500 dark:focus:ring-red-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 deceased-checkbox" type="checkbox" id="motherDeceased" />
                                    <label class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300" for="motherDeceased">
                                        Mother is deceased / الأم متوفاة
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 2: Document Upload -->
        <div id="step-2" class="wizard-step hidden">
            <!-- File Upload Section -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6 dark:bg-gray-800">
                <div class="flex items-center mb-6 pb-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-center w-10 h-10 bg-orange-100 rounded-lg dark:bg-orange-900 me-3">
                        <i class="fas fa-file-upload text-orange-600 dark:text-orange-300"></i>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Required Documents / المستندات المطلوبة</h2>
                </div>

                <div class="flex items-start p-4 mb-6 text-sm text-blue-800 border border-blue-300 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400 dark:border-blue-800" role="alert">
                    <svg class="flex-shrink-0 inline w-4 h-4 me-3 mt-0.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                    </svg>
                    <div>
                        <span class="font-medium">Document Upload Instructions / تعليمات رفع المستندات</span>
                        <div class="mt-2 text-sm text-blue-700 dark:text-blue-400">
                            Please upload clear, readable documents in PDF, JPG, or PNG format. Maximum file size: 5MB per file. / يرجى رفع مستندات واضحة وقابلة للقراءة بصيغة PDF أو JPG أو PNG. الحد الأقصى لحجم الملف: ٥ ميجابايت لكل ملف.
                        </div>
                    </div>
                </div>

                <!-- Enhanced validation alert (initially hidden) -->
                <div id="document-validation-alert" class="hidden flex items-start p-4 mb-6 text-sm text-red-800 border border-red-300 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 dark:border-red-800" role="alert">
                    <svg class="flex-shrink-0 inline w-4 h-4 me-3 mt-0.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                    </svg>
                    <div>
                        <span class="font-medium">Missing Required Documents / المستندات المطلوبة المفقودة</span>
                        <div id="document-validation-message" class="mt-2 text-sm text-red-700 dark:text-red-400">
                            <!-- Validation message will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Student Documents -->
                <div class="document-section mb-6">
                    <div class="flex items-center mb-4">
                        <div class="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-lg dark:bg-blue-900 me-3">
                            <i class="fas fa-user text-blue-600 dark:text-blue-300 text-sm"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Student Documents / مستندات الطالب</h3>
                    </div>

                    <!-- Student Documents Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Student Civil ID (Always Required) -->
                        <div class="mb-4">
                            @{
                                ViewData["name"] = "StudentCivilIdFile";
                                ViewData["label"] = "Student Civil ID Document / نسخه من البطاقة المدنية - من الجهتين";
                                ViewData["required"] = true;
                                ViewData["accept"] = ".pdf,.jpg,.jpeg,.png";
                                ViewData["maxSize"] = 5;
                                ViewData["helpText"] = "Upload a clear copy of the student's Kuwait Civil ID / ارفع نسخة واضحة من البطاقة المدنية - من الجهتين";
                            }
                            @await Html.PartialAsync("_FileUpload")
                            <span asp-validation-for="StudentCivilIdFile" class="text-sm text-red-600 dark:text-red-500"></span>
                        </div>

                        <!-- Student Nationality Certificate (Conditional) -->
                        <div class="mb-4 conditional-field hidden" data-condition="student-not-kuwaiti">
                            @{
                                ViewData["name"] = "StudentNationalityCertificateFile";
                                ViewData["label"] = "Student Nationality Certificate / شهادة جنسية الطالب - موضح مادة الجنسية";
                                ViewData["required"] = false;
                                ViewData["accept"] = ".pdf,.jpg,.jpeg,.png";
                                ViewData["maxSize"] = 5;
                                ViewData["helpText"] = "Upload student's nationality certificate / ارفع شهادة جنسية الطالب - موضح مادة الجنسية";
                            }
                            @await Html.PartialAsync("_FileUpload")
                            <span asp-validation-for="StudentNationalityCertificateFile" class="text-sm text-red-600 dark:text-red-500"></span>
                        </div>

                        <!-- Student Birth Certificate (Always Required for Eligible Cases) -->
                        <div class="mb-4">
                            @{
                                ViewData["name"] = "StudentBirthCertificateFile";
                                ViewData["label"] = "Student Birth Certificate / شهادة ميلاد الطالب";
                                ViewData["required"] = true;
                                ViewData["accept"] = ".pdf,.jpg,.jpeg,.png";
                                ViewData["maxSize"] = 5;
                                ViewData["helpText"] = "Upload a clear copy of the student's birth certificate / ارفع نسخة واضحة من شهادة ميلاد الطالب";
                            }
                            @await Html.PartialAsync("_FileUpload")
                            <span asp-validation-for="StudentBirthCertificateFile" class="text-sm text-red-600 dark:text-red-500"></span>
                        </div>
                    </div>
                </div>

                <!-- Father Documents -->
                <div class="document-section mb-6">
                    <div class="flex items-center mb-4">
                        <div class="flex items-center justify-center w-8 h-8 bg-green-100 rounded-lg dark:bg-green-900 me-3">
                            <i class="fas fa-male text-green-600 dark:text-green-300 text-sm"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Father Documents / مستندات الأب</h3>
                    </div>

                    <!-- Father Documents Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Father Civil ID (Conditional) -->
                        <div class="mb-4 conditional-field hidden" data-condition="father-kuwaiti">
                            @{
                                ViewData["name"] = "FatherCivilIdFile";
                                ViewData["label"] = "Father's Civil ID Document / نسخة من البطاقة المدنية للأب - من الجهتين";
                                ViewData["required"] = false;
                                ViewData["accept"] = ".pdf,.jpg,.jpeg,.png";
                                ViewData["maxSize"] = 5;
                                ViewData["helpText"] = "Upload father's Kuwait Civil ID / ارفع نسخة من البطاقة المدنية الكويتيه للأب";
                            }
                            @await Html.PartialAsync("_FileUpload")
                            <span asp-validation-for="FatherCivilIdFile" class="text-sm text-red-600 dark:text-red-500"></span>
                        </div>

                        <!-- Father Nationality Certificate (Conditional) -->
                        <div class="mb-4 conditional-field hidden" data-condition="father-not-kuwaiti">
                            @{
                                ViewData["name"] = "FatherNationalityCertificateFile";
                                ViewData["label"] = "Father's Nationality Certificate / شهادة جنسية الأب - موضح مادة الجنسية";
                                ViewData["required"] = false;
                                ViewData["accept"] = ".pdf,.jpg,.jpeg,.png";
                                ViewData["maxSize"] = 5;
                                ViewData["helpText"] = "Upload father's nationality certificate / ارفع شهادة جنسية الأب - موضح مادة الجنسية";
                            }
                            @await Html.PartialAsync("_FileUpload")
                            <span asp-validation-for="FatherNationalityCertificateFile" class="text-sm text-red-600 dark:text-red-500"></span>
                        </div>

                        <!-- Father Death Certificate (Conditional - only if father is Kuwaiti and deceased) -->
                        <div class="mb-4 conditional-field hidden" data-condition="father-deceased">
                            @{
                                ViewData["name"] = "FatherDeathCertificateFile";
                                ViewData["label"] = "Father's Death Certificate / شهادة وفاة الأب";
                                ViewData["required"] = false;
                                ViewData["accept"] = ".pdf,.jpg,.jpeg,.png";
                                ViewData["maxSize"] = 5;
                                ViewData["helpText"] = "Upload father's death certificate / ارفع شهادة وفاة الأب";
                            }
                            @await Html.PartialAsync("_FileUpload")
                            <span asp-validation-for="FatherDeathCertificateFile" class="text-sm text-red-600 dark:text-red-500"></span>
                        </div>
                    </div>
                </div>

                <!-- Mother Documents -->
                <div class="document-section mb-6">
                    <div class="flex items-center mb-4">
                        <div class="flex items-center justify-center w-8 h-8 bg-purple-100 rounded-lg dark:bg-purple-900 me-3">
                            <i class="fas fa-female text-purple-600 dark:text-purple-300 text-sm"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Mother Documents / مستندات الأم</h3>
                    </div>

                    <!-- Mother Documents Grid -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Mother Civil ID (Conditional) -->
                        <div class="mb-4 conditional-field hidden" data-condition="mother-kuwaiti">
                            @{
                                ViewData["name"] = "MotherCivilIdFile";
                                ViewData["label"] = "Mother's Civil ID Document / نسخة من البطاقة المدنية للأم - من الجهتين";
                                ViewData["required"] = false;
                                ViewData["accept"] = ".pdf,.jpg,.jpeg,.png";
                                ViewData["maxSize"] = 5;
                                ViewData["helpText"] = "Upload mother's Kuwait Civil ID / ارفع نسخة من البطاقة المدنية الكويتيه للأم";
                            }
                            @await Html.PartialAsync("_FileUpload")
                            <span asp-validation-for="MotherCivilIdFile" class="text-sm text-red-600 dark:text-red-500"></span>
                        </div>

                        <!-- Mother Nationality Certificate (Conditional) -->
                        <div class="mb-4 conditional-field hidden" data-condition="mother-not-kuwaiti">
                            @{
                                ViewData["name"] = "MotherNationalityCertificateFile";
                                ViewData["label"] = "Mother's Nationality Certificate / شهادة جنسية الأم - موضح مادة الجنسية";
                                ViewData["required"] = false;
                                ViewData["accept"] = ".pdf,.jpg,.jpeg,.png";
                                ViewData["maxSize"] = 5;
                                ViewData["helpText"] = "Upload mother's nationality certificate / ارفع شهادة جنسية الأم - موضح مادة الجنسية";
                            }
                            @await Html.PartialAsync("_FileUpload")
                            <span asp-validation-for="MotherNationalityCertificateFile" class="text-sm text-red-600 dark:text-red-500"></span>
                        </div>

                        <!-- Mother Death Certificate (Conditional - only if mother is Kuwaiti and deceased) -->
                        <div class="mb-4 conditional-field hidden" data-condition="mother-deceased">
                            @{
                                ViewData["name"] = "MotherDeathCertificateFile";
                                ViewData["label"] = "Mother's Death Certificate / شهادة وفاة الأم";
                                ViewData["required"] = false;
                                ViewData["accept"] = ".pdf,.jpg,.jpeg,.png";
                                ViewData["maxSize"] = 5;
                                ViewData["helpText"] = "Upload mother's death certificate / ارفع شهادة وفاة الأم";
                            }
                            @await Html.PartialAsync("_FileUpload")
                            <span asp-validation-for="MotherDeathCertificateFile" class="text-sm text-red-600 dark:text-red-500"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Step 3: Review & Submit -->
        <div id="step-3" class="wizard-step hidden">
            <!-- Review Section -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6 dark:bg-gray-800">
                <div class="flex items-center mb-6 pb-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-center w-10 h-10 bg-green-100 rounded-lg dark:bg-green-900 me-3">
                        <i class="fas fa-check-circle text-green-600 dark:text-green-300"></i>
                    </div>
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Review Your Application / مراجعة طلبك</h2>
                </div>

                <!-- Application Summary -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Personal Information / المعلومات الشخصية</h3>
                        <dl class="space-y-3">
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Student Name / اسم الطالب</dt>
                                <dd id="review-student-name" class="text-sm text-gray-900 dark:text-white">-</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Student Civil ID / نسخه من البطاقة المدنية - من الجهتين</dt>
                                <dd id="review-student-civil-id" class="text-sm text-gray-900 dark:text-white">-</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Mobile Number / رقم الهاتف المحمول</dt>
                                <dd id="review-student-mobile" class="text-sm text-gray-900 dark:text-white">-</dd>
                            </div>
                        </dl>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Nationality Status / حالة الجنسية</h3>
                        <div id="review-nationality-status" class="space-y-2">
                            <!-- Nationality badges will be populated by JavaScript -->
                        </div>

                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4 mt-6">Application Status / حالة الطلب</h3>
                        <div id="review-eligibility-case" class="text-sm mb-4">
                            <!-- Application status will be populated by JavaScript -->
                        </div>

                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4 mt-6">Required Documents / المستندات المطلوبة</h3>
                        <div id="review-case-requirements" class="text-sm">
                            <!-- Required documents will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Uploaded Documents -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Uploaded Documents / المستندات المرفوعة</h3>
                    <div id="review-documents" class="space-y-3">
                        <!-- Document list will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Final Confirmation -->
                <div class="flex items-start p-4 text-sm text-blue-800 border border-blue-300 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400 dark:border-blue-800" role="alert">
                    <svg class="flex-shrink-0 inline w-4 h-4 me-3 mt-0.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                    </svg>
                    <div>
                        <span class="font-medium">Please Review Carefully / يرجى المراجعة بعناية</span>
                        <div class="mt-1 text-sm text-blue-700 dark:text-blue-400">
                            Please review all information and documents before submitting. Once submitted, you cannot modify your application. / يرجى مراجعة جميع المعلومات والمستندات قبل الإرسال. بمجرد الإرسال، لا يمكنك تعديل طلبك.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Wizard Navigation -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6 dark:bg-gray-800">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div class="flex items-center">
                    <button type="button" id="btn-back" class="text-gray-900 bg-white border border-gray-300 hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700 hidden">
                        <i class="fas fa-arrow-left me-2"></i>
                        Previous / السابق
                    </button>
                </div>
                <div class="flex items-center gap-3">
                    <button type="button" id="btn-cancel" class="text-gray-900 bg-white border border-gray-300 hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700">
                        <i class="fas fa-times me-2"></i>
                        Cancel / إلغاء
                    </button>
                    <button type="button" id="btn-next" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-8 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                        <span id="btn-next-text">Next / التالي</span>
                        <i class="fas fa-arrow-right ms-2"></i>
                    </button>
                    <button type="submit" id="btn-submit" class="text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:outline-none focus:ring-green-300 font-medium rounded-lg text-sm px-8 py-2.5 dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800 hidden">
                        <i class="fas fa-paper-plane me-2"></i>
                        Submit Application / إرسال الطلب
                    </button>
                </div>
            </div>
        </div>
    </form>

    <!-- Loading Spinner -->
    @{
        ViewData["message"] = "Processing your application... / جاري معالجة طلبك...";
        ViewData["overlay"] = true;
        ViewData["id"] = "form-loading";
    }
    @await Html.PartialAsync("_LoadingSpinner")
</div>

@section Scripts {
    <script src="~/js/shared-forms.js" asp-append-version="true"></script>
    <script src="~/js/sis-integration.js" asp-append-version="true"></script>
    <script src="~/js/kuwaiti-student-info-wizard.js" asp-append-version="true"></script>
}
