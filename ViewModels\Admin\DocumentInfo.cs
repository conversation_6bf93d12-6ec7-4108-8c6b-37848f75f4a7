namespace Forms.ktech.ViewModels.Admin
{
    /// <summary>
    /// Contains information about documents for a submission
    /// </summary>
    public class DocumentInfo
    {
        /// <summary>
        /// List of document details
        /// </summary>
        public List<DocumentDetail> Documents { get; set; } = new List<DocumentDetail>();

        /// <summary>
        /// Gets total number of documents
        /// </summary>
        public int TotalDocuments => Documents.Count;

        /// <summary>
        /// Gets number of uploaded documents
        /// </summary>
        public int UploadedDocuments => Documents.Count(d => d.IsUploaded);

        /// <summary>
        /// Gets number of required documents
        /// </summary>
        public int RequiredDocuments => Documents.Count(d => d.IsRequired);

        /// <summary>
        /// Gets total file size of all documents
        /// </summary>
        public long TotalFileSizeBytes => Documents.Sum(d => d.FileSizeBytes);

        /// <summary>
        /// Gets formatted total file size
        /// </summary>
        public string FormattedTotalFileSize => FormatFileSize(TotalFileSizeBytes);

        /// <summary>
        /// Formats file size in human-readable format
        /// </summary>
        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }

    /// <summary>
    /// Details about a specific document
    /// </summary>
    public class DocumentDetail
    {
        /// <summary>
        /// Document type identifier
        /// </summary>
        public string DocumentType { get; set; } = string.Empty;

        /// <summary>
        /// Display name for the document
        /// </summary>
        public string DisplayName { get; set; } = string.Empty;

        /// <summary>
        /// Document category (Student, Father, Mother)
        /// </summary>
        public string Category { get; set; } = string.Empty;

        /// <summary>
        /// Whether this document is required
        /// </summary>
        public bool IsRequired { get; set; }

        /// <summary>
        /// Whether the document has been uploaded
        /// </summary>
        public bool IsUploaded { get; set; }

        /// <summary>
        /// File path on the server
        /// </summary>
        public string? FilePath { get; set; }

        /// <summary>
        /// Whether the file exists on disk
        /// </summary>
        public bool FileExists { get; set; }

        /// <summary>
        /// Original filename
        /// </summary>
        public string? FileName { get; set; }

        /// <summary>
        /// File size in bytes
        /// </summary>
        public long FileSizeBytes { get; set; }

        /// <summary>
        /// Formatted file size
        /// </summary>
        public string FormattedFileSize { get; set; } = "N/A";

        /// <summary>
        /// Upload date
        /// </summary>
        public DateTime? UploadDate { get; set; }
    }
}
