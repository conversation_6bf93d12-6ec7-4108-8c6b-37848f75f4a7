using Forms.ktech.Shared.ViewModels;

namespace Forms.ktech.Services
{
    /// <summary>
    /// Generic interface for handling form submissions across all forms
    /// </summary>
    /// <typeparam name="TViewModel">The ViewModel type for the form</typeparam>
    /// <typeparam name="TEntity">The Entity type for the form</typeparam>
    public interface IFormHandler<TViewModel, TEntity> 
        where TViewModel : class, IFormViewModel
        where TEntity : class
    {
        /// <summary>
        /// Checks if the form submission should be rejected based on business rules
        /// </summary>
        /// <param name="viewModel">The form ViewModel to validate</param>
        /// <returns>True if the submission should be rejected, false if it can proceed</returns>
        bool IsNotEligible(TViewModel viewModel);

        /// <summary>
        /// Gets the reason why a form submission is not eligible
        /// </summary>
        /// <param name="viewModel">The form ViewModel to check</param>
        /// <returns>Error message explaining why the form is not eligible, or null if eligible</returns>
        string? GetIneligibilityReason(TViewModel viewModel);

        /// <summary>
        /// Saves the form submission including files and data
        /// </summary>
        /// <param name="viewModel">The form ViewModel containing the data</param>
        /// <param name="formName">The name of the form (used for file organization)</param>
        /// <param name="userId">The ID of the user submitting the form</param>
        /// <returns>The ID of the created entity</returns>
        Task<int> SaveAsync(TViewModel viewModel, string formName, string? userId = null);

        /// <summary>
        /// Retrieves a form submission by ID
        /// </summary>
        /// <param name="id">The ID of the submission</param>
        /// <returns>The entity if found, null otherwise</returns>
        Task<TEntity?> GetByIdAsync(int id);

        /// <summary>
        /// Retrieves a form submission by submission GUID
        /// </summary>
        /// <param name="submissionGuid">The submission GUID</param>
        /// <returns>The entity if found, null otherwise</returns>
        Task<TEntity?> GetBySubmissionGuidAsync(Guid submissionGuid);

        /// <summary>
        /// Updates an existing form submission
        /// </summary>
        /// <param name="id">The ID of the submission to update</param>
        /// <param name="viewModel">The updated form data</param>
        /// <param name="formName">The name of the form</param>
        /// <param name="userId">The ID of the user making the update</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> UpdateAsync(int id, TViewModel viewModel, string formName, string? userId = null);

        /// <summary>
        /// Soft deletes a form submission
        /// </summary>
        /// <param name="id">The ID of the submission to delete</param>
        /// <param name="userId">The ID of the user performing the deletion</param>
        /// <returns>True if successful, false otherwise</returns>
        Task<bool> DeleteAsync(int id, string? userId = null);

        /// <summary>
        /// Gets all submissions for a specific user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>List of entities for the user</returns>
        Task<List<TEntity>> GetByUserIdAsync(string userId);

        /// <summary>
        /// Checks if a user has already submitted a form for a specific Civil ID
        /// Uses secure authorization checks to ensure users can only access their own submissions
        /// </summary>
        /// <param name="civilId">The Civil ID to check</param>
        /// <param name="userId">The ID of the user (for authorization)</param>
        /// <returns>The existing submission if found, null otherwise</returns>
        Task<TEntity?> GetExistingSubmissionByCivilIdAsync(string civilId, string? userId);

        /// <summary>
        /// Saves or updates a form submission based on whether an existing submission exists
        /// Implements single submission rule by checking for existing submissions by Civil ID
        /// </summary>
        /// <param name="viewModel">The form ViewModel containing the data</param>
        /// <param name="formName">The name of the form (used for file organization)</param>
        /// <param name="userId">The ID of the user submitting the form</param>
        /// <returns>The ID of the created or updated entity</returns>
        Task<int> SaveOrUpdateAsync(TViewModel viewModel, string formName, string? userId = null);

        /// <summary>
        /// Maps a ViewModel to an Entity
        /// </summary>
        /// <param name="viewModel">The ViewModel to map</param>
        /// <param name="entity">The existing entity to update, or null to create new</param>
        /// <returns>The mapped entity</returns>
        TEntity MapToEntity(TViewModel viewModel, TEntity? entity = null);

        /// <summary>
        /// Maps an Entity to a ViewModel
        /// </summary>
        /// <param name="entity">The entity to map</param>
        /// <returns>The mapped ViewModel</returns>
        TViewModel MapToViewModel(TEntity entity);
    }
}
