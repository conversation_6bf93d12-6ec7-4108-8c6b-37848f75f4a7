@{
    ViewData["Title"] = "SIS Sync Settings";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@section Breadcrumb {
    <li class="inline-flex items-center">
        <a asp-area="" asp-controller="Home" asp-action="Index" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
            <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
            </svg>
            Home
        </a>
    </li>
    <li>
        <div class="flex items-center">
            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
            </svg>
            <a asp-controller="Admin" asp-action="Dashboard" class="ms-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ms-2 dark:text-gray-400 dark:hover:text-white">Admin</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
            </svg>
            <a asp-controller="Admin" asp-action="SyncDashboard" class="ms-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ms-2 dark:text-gray-400 dark:hover:text-white">SIS Sync</a>
        </div>
    </li>
    <li aria-current="page">
        <div class="flex items-center">
            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
            </svg>
            <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400">Sync Settings</span>
        </div>
    </li>
}

<!-- Header Section -->
<div class="bg-white rounded-lg shadow-lg p-6 mb-6 dark:bg-gray-800">
    <div class="flex items-center justify-between">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white">
                    <i class="fas fa-cogs text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">SIS Sync Settings</h1>
                <p class="text-gray-600 dark:text-gray-300">Configure synchronization schedules and preferences</p>
            </div>
        </div>
        <div class="flex space-x-3">
            <a asp-controller="Admin" asp-action="SyncDashboard" class="bg-blue-700 hover:bg-blue-800 text-white font-medium py-2 px-4 rounded-lg focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-600">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    </div>
</div>

<!-- Settings Grid -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Sync Configuration -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <div class="flex items-center mb-6">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-10 w-10 rounded-md bg-blue-500 text-white">
                    <i class="fas fa-sync-alt"></i>
                </div>
            </div>
            <div class="ml-3">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Sync Configuration</h3>
                <p class="text-sm text-gray-600 dark:text-gray-300">Configure automatic synchronization settings</p>
            </div>
        </div>
        
        <form class="space-y-6">
            <!-- Auto Sync Enable -->
            <div class="flex items-center justify-between">
                <div class="flex flex-col">
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Automatic Sync</label>
                    <p class="text-xs text-gray-500 dark:text-gray-400">Enable scheduled synchronization</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="autoSyncEnabled" class="sr-only peer" checked>
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
            </div>

            <!-- Sync Schedule -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Sync Schedule (Cron Expression)</label>
                <input type="text" id="syncSchedule" value="0 2 * * *" 
                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Current: Daily at 2:00 AM</p>
            </div>

            <!-- Data Freshness -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Data Freshness (Hours)</label>
                <input type="number" id="dataFreshnessHours" value="24" min="1" max="168"
                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">How long data is considered fresh before warning</p>
            </div>

            <!-- Incremental Sync -->
            <div class="flex items-center justify-between">
                <div class="flex flex-col">
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Incremental Sync</label>
                    <p class="text-xs text-gray-500 dark:text-gray-400">Enable incremental synchronization</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="enableIncrementalSync" class="sr-only peer" checked>
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
            </div>

            <!-- Save Button -->
            <div class="pt-4">
                <button type="button" onclick="saveSyncSettings()" 
                        class="w-full bg-blue-700 hover:bg-blue-800 text-white font-medium py-2 px-4 rounded-lg focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-600">
                    <i class="fas fa-save me-2"></i>Save Settings
                </button>
            </div>
        </form>
    </div>

    <!-- API Configuration -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <div class="flex items-center mb-6">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-10 w-10 rounded-md bg-green-500 text-white">
                    <i class="fas fa-server"></i>
                </div>
            </div>
            <div class="ml-3">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">API Configuration</h3>
                <p class="text-sm text-gray-600 dark:text-gray-300">SIS API connection settings</p>
            </div>
        </div>
        
        <div class="space-y-6">
            <!-- API Status -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="font-medium text-gray-900 dark:text-white">Connection Status</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400" id="apiStatus">Checking...</p>
                    </div>
                    <div id="apiStatusIcon" class="flex items-center justify-center h-8 w-8 rounded-full bg-yellow-500 text-white">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                </div>
            </div>

            <!-- API Endpoints -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Base URL</label>
                <input type="text" id="apiBaseUrl" value="https://sisapi.ktech.edu.kw/api" readonly
                       class="bg-gray-100 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:text-white cursor-not-allowed">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Configured in application settings</p>
            </div>

            <!-- Timeout Settings -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Timeout (Seconds)</label>
                <input type="number" id="timeoutSeconds" value="30" min="5" max="300" readonly
                       class="bg-gray-100 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:text-white cursor-not-allowed">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Request timeout for API calls</p>
            </div>

            <!-- Retry Settings -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Retry Attempts</label>
                <input type="number" id="retryAttempts" value="3" min="0" max="10" readonly
                       class="bg-gray-100 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:text-white cursor-not-allowed">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Number of retry attempts on failure</p>
            </div>

            <!-- Test Connection Button -->
            <div class="pt-4">
                <button type="button" onclick="testApiConnection()" 
                        class="w-full bg-green-700 hover:bg-green-800 text-white font-medium py-2 px-4 rounded-lg focus:ring-4 focus:ring-green-300 dark:focus:ring-green-600">
                    <i class="fas fa-plug me-2"></i>Test Connection
                </button>
            </div>
        </div>
    </div>

    <!-- Performance Settings -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <div class="flex items-center mb-6">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-10 w-10 rounded-md bg-purple-500 text-white">
                    <i class="fas fa-tachometer-alt"></i>
                </div>
            </div>
            <div class="ml-3">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Performance Settings</h3>
                <p class="text-sm text-gray-600 dark:text-gray-300">Optimize sync performance</p>
            </div>
        </div>
        
        <div class="space-y-6">
            <!-- Batch Size -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Batch Size</label>
                <input type="number" id="batchSize" value="100" min="10" max="1000" readonly
                       class="bg-gray-100 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:text-white cursor-not-allowed">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Number of records processed per batch</p>
            </div>

            <!-- Max Concurrent Requests -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Max Concurrent Requests</label>
                <input type="number" id="maxConcurrentRequests" value="5" min="1" max="20" readonly
                       class="bg-gray-100 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:text-white cursor-not-allowed">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Maximum parallel API requests</p>
            </div>

            <!-- Sync History Retention -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">History Retention (Days)</label>
                <input type="number" id="maxSyncHistoryDays" value="90" min="7" max="365"
                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">How long to keep sync history records</p>
            </div>

            <!-- Performance Note -->
            <div class="bg-blue-50 dark:bg-blue-900 rounded-lg p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-info-circle text-blue-400"></i>
                    </div>
                    <div class="ml-3">
                        <h4 class="text-sm font-medium text-blue-800 dark:text-blue-200">Performance Note</h4>
                        <p class="text-sm text-blue-700 dark:text-blue-300 mt-1">
                            Most performance settings are configured in application settings and require application restart to take effect.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monitoring & Alerts -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <div class="flex items-center mb-6">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-10 w-10 rounded-md bg-red-500 text-white">
                    <i class="fas fa-bell"></i>
                </div>
            </div>
            <div class="ml-3">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Monitoring & Alerts</h3>
                <p class="text-sm text-gray-600 dark:text-gray-300">Configure monitoring and notifications</p>
            </div>
        </div>
        
        <div class="space-y-6">
            <!-- Email Notifications -->
            <div class="flex items-center justify-between">
                <div class="flex flex-col">
                    <label class="text-sm font-medium text-gray-700 dark:text-gray-300">Email Notifications</label>
                    <p class="text-xs text-gray-500 dark:text-gray-400">Send email alerts for sync failures</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="emailNotifications" class="sr-only peer">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
            </div>

            <!-- Alert Recipients -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Alert Recipients</label>
                <textarea id="alertRecipients" rows="3" placeholder="<EMAIL>&#10;<EMAIL>"
                          class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"></textarea>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">One email address per line</p>
            </div>

            <!-- Health Check Interval -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Health Check Interval (Minutes)</label>
                <input type="number" id="healthCheckInterval" value="15" min="5" max="60"
                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">How often to check API health</p>
            </div>

            <!-- Save Monitoring Settings -->
            <div class="pt-4">
                <button type="button" onclick="saveMonitoringSettings()" 
                        class="w-full bg-red-700 hover:bg-red-800 text-white font-medium py-2 px-4 rounded-lg focus:ring-4 focus:ring-red-300 dark:focus:ring-red-600">
                    <i class="fas fa-save me-2"></i>Save Monitoring Settings
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            checkApiStatus();
            loadCurrentSettings();
        });

        // Check API status
        async function checkApiStatus() {
            try {
                const response = await fetch('/Admin/SyncStatus');
                const data = await response.json();

                if (data.error) {
                    updateApiStatus(false, 'Connection Error');
                } else {
                    updateApiStatus(true, 'Connected');
                }
            } catch (error) {
                console.error('Error checking API status:', error);
                updateApiStatus(false, 'Connection Failed');
            }
        }

        function updateApiStatus(isHealthy, status) {
            const statusEl = document.getElementById('apiStatus');
            const iconEl = document.getElementById('apiStatusIcon');

            statusEl.textContent = status;

            if (isHealthy) {
                iconEl.innerHTML = '<i class="fas fa-check"></i>';
                iconEl.className = 'flex items-center justify-center h-8 w-8 rounded-full bg-green-500 text-white';
            } else {
                iconEl.innerHTML = '<i class="fas fa-times"></i>';
                iconEl.className = 'flex items-center justify-center h-8 w-8 rounded-full bg-red-500 text-white';
            }
        }

        // Load current settings (placeholder - in real implementation, fetch from server)
        function loadCurrentSettings() {
            // In a real implementation, you would fetch current settings from the server
            // For now, we'll use the default values already set in the form
            console.log('Settings loaded');
        }

        // Test API connection
        async function testApiConnection() {
            const button = event.target;
            const originalText = button.innerHTML;

            // Show loading state
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Testing...';
            button.disabled = true;

            try {
                // In a real implementation, you would call a test endpoint
                await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call

                // Simulate success
                updateApiStatus(true, 'Connection Test Successful');
                showNotification('API connection test successful!', 'success');

            } catch (error) {
                console.error('API test failed:', error);
                updateApiStatus(false, 'Connection Test Failed');
                showNotification('API connection test failed. Please check configuration.', 'error');
            } finally {
                // Restore button
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }

        // Save sync settings
        async function saveSyncSettings() {
            const button = event.target;
            const originalText = button.innerHTML;

            // Show loading state
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
            button.disabled = true;

            try {
                const settings = {
                    autoSyncEnabled: document.getElementById('autoSyncEnabled').checked,
                    syncSchedule: document.getElementById('syncSchedule').value,
                    dataFreshnessHours: parseInt(document.getElementById('dataFreshnessHours').value),
                    enableIncrementalSync: document.getElementById('enableIncrementalSync').checked,
                    maxSyncHistoryDays: parseInt(document.getElementById('maxSyncHistoryDays').value)
                };

                // Validate settings
                if (!validateSyncSettings(settings)) {
                    return;
                }

                // In a real implementation, you would send these settings to the server
                await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call

                showNotification('Sync settings saved successfully!', 'success');

            } catch (error) {
                console.error('Error saving sync settings:', error);
                showNotification('Failed to save sync settings. Please try again.', 'error');
            } finally {
                // Restore button
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }

        // Save monitoring settings
        async function saveMonitoringSettings() {
            const button = event.target;
            const originalText = button.innerHTML;

            // Show loading state
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
            button.disabled = true;

            try {
                const settings = {
                    emailNotifications: document.getElementById('emailNotifications').checked,
                    alertRecipients: document.getElementById('alertRecipients').value.split('\n').filter(email => email.trim()),
                    healthCheckInterval: parseInt(document.getElementById('healthCheckInterval').value)
                };

                // Validate settings
                if (!validateMonitoringSettings(settings)) {
                    return;
                }

                // In a real implementation, you would send these settings to the server
                await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call

                showNotification('Monitoring settings saved successfully!', 'success');

            } catch (error) {
                console.error('Error saving monitoring settings:', error);
                showNotification('Failed to save monitoring settings. Please try again.', 'error');
            } finally {
                // Restore button
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }

        // Validation functions
        function validateSyncSettings(settings) {
            if (settings.dataFreshnessHours < 1 || settings.dataFreshnessHours > 168) {
                showNotification('Data freshness must be between 1 and 168 hours.', 'error');
                return false;
            }

            if (settings.maxSyncHistoryDays < 7 || settings.maxSyncHistoryDays > 365) {
                showNotification('Sync history retention must be between 7 and 365 days.', 'error');
                return false;
            }

            // Basic cron expression validation
            const cronParts = settings.syncSchedule.split(' ');
            if (cronParts.length !== 5) {
                showNotification('Invalid cron expression. Please use format: minute hour day month weekday', 'error');
                return false;
            }

            return true;
        }

        function validateMonitoringSettings(settings) {
            if (settings.healthCheckInterval < 5 || settings.healthCheckInterval > 60) {
                showNotification('Health check interval must be between 5 and 60 minutes.', 'error');
                return false;
            }

            // Validate email addresses
            if (settings.emailNotifications && settings.alertRecipients.length > 0) {
                const emailRegex = /^[^\\s@@]+@@[^\\s@@]+\\.[^\\s@@]+$/;
                for (const email of settings.alertRecipients) {
                    if (!emailRegex.test(email.trim())) {
                        showNotification(`Invalid email address: ${email}`, 'error');
                        return false;
                    }
                }
            }

            return true;
        }

        // Notification system
        function showNotification(message, type) {
            const alertClass = type === 'success' ? 'bg-green-100 border-green-400 text-green-700 dark:bg-green-900 dark:border-green-700 dark:text-green-300' :
                              'bg-red-100 border-red-400 text-red-700 dark:bg-red-900 dark:border-red-700 dark:text-red-300';
            const iconClass = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';

            const alertHtml = `
                <div class="${alertClass} px-4 py-3 rounded mb-6 notification-alert" role="alert">
                    <div class="flex">
                        <div class="py-1">
                            <i class="${iconClass} me-2"></i>
                        </div>
                        <div>
                            <p class="font-bold">${type === 'success' ? 'Success!' : 'Error!'}</p>
                            <p class="text-sm">${message}</p>
                        </div>
                    </div>
                </div>
            `;

            // Insert after header section
            const header = document.querySelector('.bg-white.rounded-lg.shadow-lg.p-6.mb-6');
            header.insertAdjacentHTML('afterend', alertHtml);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                const alert = document.querySelector('.notification-alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }

        // Cron expression helper
        document.getElementById('syncSchedule').addEventListener('input', function() {
            const cronExpression = this.value;
            const description = parseCronExpression(cronExpression);
            const helpText = this.nextElementSibling;
            helpText.textContent = description;
        });

        function parseCronExpression(cron) {
            const parts = cron.split(' ');
            if (parts.length !== 5) return 'Invalid cron expression';

            const [minute, hour, day, month, weekday] = parts;

            // Simple cron parser for common patterns
            if (cron === '0 2 * * *') return 'Daily at 2:00 AM';
            if (cron === '0 */6 * * *') return 'Every 6 hours';
            if (cron === '0 0 * * 0') return 'Weekly on Sunday at midnight';
            if (cron === '0 0 1 * *') return 'Monthly on the 1st at midnight';

            return `Custom: ${minute} ${hour} ${day} ${month} ${weekday}`;
        }

        // Auto-refresh API status every 30 seconds
        setInterval(checkApiStatus, 30000);
    </script>
}
