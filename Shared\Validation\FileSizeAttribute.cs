using System.ComponentModel.DataAnnotations;

namespace Forms.ktech.Shared.Validation
{
    /// <summary>
    /// Validation attribute to check file size limits for IFormFile properties
    /// </summary>
    public class FileSizeAttribute : ValidationAttribute
    {
        private readonly long _maxSizeInBytes;

        /// <summary>
        /// Initializes a new instance of FileSizeAttribute
        /// </summary>
        /// <param name="maxSizeInMB">Maximum file size in megabytes</param>
        public FileSizeAttribute(int maxSizeInMB)
        {
            _maxSizeInBytes = maxSizeInMB * 1024 * 1024; // Convert MB to bytes
            ErrorMessage = $"File size cannot exceed {maxSizeInMB} MB.";
        }

        /// <summary>
        /// Validates the file size
        /// </summary>
        /// <param name="value">The IFormFile to validate</param>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation result</returns>
        protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
        {
            if (value == null)
            {
                return ValidationResult.Success; // Let [Required] handle null validation
            }

            if (value is IFormFile file)
            {
                if (file.Length > _maxSizeInBytes)
                {
                    return new ValidationResult(ErrorMessage, new[] { validationContext.MemberName });
                }
            }
            else if (value is IEnumerable<IFormFile> files)
            {
                foreach (var f in files)
                {
                    if (f.Length > _maxSizeInBytes)
                    {
                        return new ValidationResult(ErrorMessage, new[] { validationContext.MemberName });
                    }
                }
            }

            return ValidationResult.Success;
        }
    }
}
