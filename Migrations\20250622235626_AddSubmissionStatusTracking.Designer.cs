﻿// <auto-generated />
using System;
using Forms.ktech.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Forms.ktech.Migrations
{
    [DbContext(typeof(FormsKTechContext))]
    [Migration("20250622235626_AddSubmissionStatusTracking")]
    partial class AddSubmissionStatusTracking
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Forms.ktech.Data.SisStudent", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime>("BirthDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DataHash")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("EnrollmentStatus")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FullNameAR")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("FullNameEN")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Gender")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("LastSyncDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Level")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Major")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("MobileNo")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("NationalID")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Nationality")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("StudentID")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("Email")
                        .HasDatabaseName("IX_SisStudents_Email");

                    b.HasIndex("LastSyncDate")
                        .HasDatabaseName("IX_SisStudents_LastSyncDate");

                    b.HasIndex("NationalID")
                        .HasDatabaseName("IX_SisStudents_NationalID");

                    b.HasIndex("StudentID")
                        .IsUnique()
                        .HasDatabaseName("IX_SisStudents_StudentID");

                    b.ToTable("SisStudents", (string)null);
                });

            modelBuilder.Entity("Forms.ktech.Data.SyncHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AdditionalDetails")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<string>("ApiEndpoint")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int?>("BatchSize")
                        .HasColumnType("int");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CurrentStep")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<long?>("DurationMs")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("ErrorDetails")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)");

                    b.Property<string>("ErrorMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<bool>("IsAutomated")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<int>("ProgressPercentage")
                        .HasColumnType("int");

                    b.Property<int>("RecordsAdded")
                        .HasColumnType("int");

                    b.Property<int>("RecordsErrored")
                        .HasColumnType("int");

                    b.Property<int>("RecordsProcessed")
                        .HasColumnType("int");

                    b.Property<int>("RecordsSkipped")
                        .HasColumnType("int");

                    b.Property<int>("RecordsUpdated")
                        .HasColumnType("int");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("SubmissionGuid")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("SyncId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("SyncType")
                        .HasColumnType("int");

                    b.Property<string>("TriggeredByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CreatedDate")
                        .HasDatabaseName("IX_SyncHistories_CreatedDate");

                    b.HasIndex("StartTime")
                        .HasDatabaseName("IX_SyncHistories_StartTime");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_SyncHistories_Status");

                    b.HasIndex("SubmissionGuid")
                        .IsUnique()
                        .HasDatabaseName("IX_SyncHistories_SubmissionGuid");

                    b.HasIndex("SyncId")
                        .IsUnique()
                        .HasDatabaseName("IX_SyncHistories_SyncId");

                    b.HasIndex("TriggeredByUserId")
                        .HasDatabaseName("IX_SyncHistories_TriggeredBy");

                    b.HasIndex("SyncType", "Status")
                        .HasDatabaseName("IX_SyncHistories_TypeStatus");

                    b.ToTable("SyncHistories", (string)null);
                });

            modelBuilder.Entity("Forms.ktech.Features.KuwaitiStudentInfo.Models.StudentInfo", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ApprovedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("CreatedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DeletedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FatherCivilIdPath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("FatherDeathCertificatePath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("FatherIsDeceased")
                        .HasColumnType("bit");

                    b.Property<bool>("FatherIsKuwaiti")
                        .HasColumnType("bit");

                    b.Property<string>("FatherName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("FatherNationalityCertificatePath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("FlagNotes")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<DateTime?>("FlaggedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("MotherCivilIdPath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("MotherDeathCertificatePath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("MotherIsDeceased")
                        .HasColumnType("bit");

                    b.Property<bool>("MotherIsKuwaiti")
                        .HasColumnType("bit");

                    b.Property<string>("MotherName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("MotherNationalityCertificatePath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<DateTime?>("RejectedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("RejectionReason")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int?>("SisStudentId")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("StatusChangedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("StatusChangedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("StudentBirthCertificatePath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("StudentCivilIdPath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("StudentIsKuwaiti")
                        .HasColumnType("bit");

                    b.Property<string>("StudentMobileNumber")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("StudentNationalityCertificatePath")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<Guid>("SubmissionGuid")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("UpdatedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId")
                        .HasDatabaseName("IX_StudentInfos_CreatedByUserId");

                    b.HasIndex("CreatedDate")
                        .HasDatabaseName("IX_StudentInfos_CreatedDate");

                    b.HasIndex("SisStudentId")
                        .HasDatabaseName("IX_StudentInfos_SisStudentId");

                    b.HasIndex("SubmissionGuid")
                        .IsUnique()
                        .HasDatabaseName("IX_StudentInfos_SubmissionGuid");

                    b.ToTable("StudentInfos", (string)null);
                });

            modelBuilder.Entity("Forms.ktech.Models.DocumentApproval", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<DateTime?>("ApprovalDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("ApprovedByUserId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Comments")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("DisapprovalReason")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("DocumentType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("SubmissionId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("ApprovalDate")
                        .HasDatabaseName("IX_DocumentApprovals_ApprovalDate");

                    b.HasIndex("ApprovedByUserId")
                        .HasDatabaseName("IX_DocumentApprovals_ApprovedByUserId");

                    b.HasIndex("CreatedDate")
                        .HasDatabaseName("IX_DocumentApprovals_CreatedDate");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_DocumentApprovals_Status");

                    b.HasIndex("SubmissionId", "DocumentType")
                        .HasDatabaseName("IX_DocumentApprovals_SubmissionId_DocumentType");

                    b.ToTable("DocumentApprovals", (string)null);
                });

            modelBuilder.Entity("Forms.ktech.Models.EmailNotification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Body")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("CreatedDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("EmailType")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FailureReason")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("RecipientEmail")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("RetryCount")
                        .HasColumnType("int");

                    b.Property<DateTime?>("SentDate")
                        .HasColumnType("datetime2");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<string>("Subject")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<int>("SubmissionId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("UpdatedDate")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("CreatedDate")
                        .HasDatabaseName("IX_EmailNotifications_CreatedDate");

                    b.HasIndex("EmailType")
                        .HasDatabaseName("IX_EmailNotifications_EmailType");

                    b.HasIndex("SentDate")
                        .HasDatabaseName("IX_EmailNotifications_SentDate");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_EmailNotifications_Status");

                    b.HasIndex("SubmissionId")
                        .HasDatabaseName("IX_EmailNotifications_SubmissionId");

                    b.HasIndex("Status", "RetryCount")
                        .HasDatabaseName("IX_EmailNotifications_Status_RetryCount");

                    b.ToTable("EmailNotifications", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NormalizedUserName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("IdentityUser");
                });

            modelBuilder.Entity("Forms.ktech.Features.KuwaitiStudentInfo.Models.StudentInfo", b =>
                {
                    b.HasOne("Forms.ktech.Data.SisStudent", "SisStudent")
                        .WithMany()
                        .HasForeignKey("SisStudentId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("SisStudent");
                });

            modelBuilder.Entity("Forms.ktech.Models.DocumentApproval", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityUser", "ApprovedByUser")
                        .WithMany()
                        .HasForeignKey("ApprovedByUserId");

                    b.HasOne("Forms.ktech.Features.KuwaitiStudentInfo.Models.StudentInfo", "Submission")
                        .WithMany()
                        .HasForeignKey("SubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApprovedByUser");

                    b.Navigation("Submission");
                });

            modelBuilder.Entity("Forms.ktech.Models.EmailNotification", b =>
                {
                    b.HasOne("Forms.ktech.Features.KuwaitiStudentInfo.Models.StudentInfo", "Submission")
                        .WithMany()
                        .HasForeignKey("SubmissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Submission");
                });
#pragma warning restore 612, 618
        }
    }
}
