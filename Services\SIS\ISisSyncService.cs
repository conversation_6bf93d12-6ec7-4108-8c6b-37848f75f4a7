using Forms.ktech.Data;

namespace Forms.ktech.Services.SIS
{
    /// <summary>
    /// Interface for SIS data synchronization service
    /// Provides methods for synchronizing student data from SIS API to local database
    /// </summary>
    public interface ISisSyncService
    {
        #region Sync Operations

        /// <summary>
        /// Starts a full synchronization of all student data from SIS
        /// </summary>
        /// <param name="triggeredByUserId">ID of the user who triggered the sync (null for automated)</param>
        /// <param name="cancellationToken">Cancellation token for the operation</param>
        /// <returns>Result of the sync operation</returns>
        Task<SyncResult> StartFullSyncAsync(string? triggeredByUserId = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Starts an incremental synchronization of changed student data from SIS
        /// </summary>
        /// <param name="triggeredByUserId">ID of the user who triggered the sync (null for automated)</param>
        /// <param name="cancellationToken">Cancellation token for the operation</param>
        /// <returns>Result of the sync operation</returns>
        Task<SyncResult> StartIncrementalSyncAsync(string? triggeredByUserId = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the current status of any running sync operation
        /// </summary>
        /// <returns>Current sync status, or null if no sync is running</returns>
        Task<SyncStatus?> GetCurrentSyncStatusAsync();

        /// <summary>
        /// Cancels a running sync operation
        /// </summary>
        /// <param name="syncId">ID of the sync operation to cancel</param>
        /// <returns>True if cancellation was successful, false otherwise</returns>
        Task<bool> CancelSyncAsync(Guid syncId);

        #endregion

        #region Sync History and Monitoring

        /// <summary>
        /// Gets the history of sync operations
        /// </summary>
        /// <param name="pageSize">Number of records per page</param>
        /// <param name="pageNumber">Page number (1-based)</param>
        /// <returns>Paginated list of sync history records</returns>
        Task<IEnumerable<SyncHistory>> GetSyncHistoryAsync(int pageSize = 50, int pageNumber = 1);

        /// <summary>
        /// Gets detailed information about a specific sync operation
        /// </summary>
        /// <param name="syncId">ID of the sync operation</param>
        /// <returns>Sync history record if found, null otherwise</returns>
        Task<SyncHistory?> GetSyncDetailsAsync(Guid syncId);

        /// <summary>
        /// Gets the last successful sync date
        /// </summary>
        /// <returns>Date of last successful sync, null if no successful sync has occurred</returns>
        Task<DateTime?> GetLastSuccessfulSyncDateAsync();

        /// <summary>
        /// Gets statistics about the current state of synchronized data
        /// </summary>
        /// <returns>Sync statistics</returns>
        Task<SyncStatistics> GetSyncStatisticsAsync();

        #endregion

        #region Data Validation and Health

        /// <summary>
        /// Validates the connection to the SIS API
        /// </summary>
        /// <returns>True if SIS API is accessible, false otherwise</returns>
        Task<bool> ValidateSisConnectionAsync();

        /// <summary>
        /// Checks if the local student data is considered fresh
        /// </summary>
        /// <returns>True if data is fresh, false if stale</returns>
        Task<bool> IsDataFreshAsync();

        /// <summary>
        /// Gets the health status of the sync service
        /// </summary>
        /// <returns>Health status information</returns>
        Task<SyncServiceHealthStatus> GetHealthStatusAsync();

        #endregion

        #region Background Job Management

        /// <summary>
        /// Schedules automatic sync operations based on configuration
        /// </summary>
        /// <returns>True if scheduling was successful, false otherwise</returns>
        Task<bool> ScheduleAutomaticSyncAsync();

        /// <summary>
        /// Unschedules automatic sync operations
        /// </summary>
        /// <returns>True if unscheduling was successful, false otherwise</returns>
        Task<bool> UnscheduleAutomaticSyncAsync();

        /// <summary>
        /// Gets the status of automatic sync scheduling
        /// </summary>
        /// <returns>True if automatic sync is scheduled, false otherwise</returns>
        Task<bool> IsAutomaticSyncScheduledAsync();

        #endregion
    }

    #region Result and Status Models

    /// <summary>
    /// Result of a sync operation
    /// </summary>
    public class SyncResult
    {
        public Guid SyncId { get; set; }
        public SyncType SyncType { get; set; }
        public SyncStatus Status { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public int RecordsProcessed { get; set; }
        public int RecordsAdded { get; set; }
        public int RecordsUpdated { get; set; }
        public int RecordsSkipped { get; set; }
        public int RecordsErrored { get; set; }
        public string? ErrorMessage { get; set; }
        public TimeSpan? Duration => EndTime.HasValue ? EndTime.Value - StartTime : null;
        public double SuccessRate => RecordsProcessed > 0 ? (double)(RecordsAdded + RecordsUpdated + RecordsSkipped) / RecordsProcessed * 100 : 0;
    }

    /// <summary>
    /// Statistics about synchronized data
    /// </summary>
    public class SyncStatistics
    {
        public int TotalStudents { get; set; }
        public int ActiveStudents { get; set; }
        public int GraduatedStudents { get; set; }
        public DateTime? LastSyncDate { get; set; }
        public DateTime? OldestStudentRecord { get; set; }
        public DateTime? NewestStudentRecord { get; set; }
        public bool IsDataFresh { get; set; }
        public TimeSpan? DataAge { get; set; }
        public Dictionary<string, int> StudentsByMajor { get; set; } = new();
        public Dictionary<string, int> StudentsByLevel { get; set; } = new();
        public Dictionary<string, int> StudentsByEnrollmentStatus { get; set; } = new();
    }

    /// <summary>
    /// Health status of the sync service
    /// </summary>
    public class SyncServiceHealthStatus
    {
        public bool IsHealthy { get; set; }
        public string Status { get; set; } = string.Empty;
        public DateTime LastChecked { get; set; }
        public bool SisApiConnected { get; set; }
        public bool DatabaseConnected { get; set; }
        public bool AutoSyncEnabled { get; set; }
        public DateTime? LastSuccessfulSync { get; set; }
        public DateTime? NextScheduledSync { get; set; }
        public int PendingSyncOperations { get; set; }
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> AdditionalInfo { get; set; } = new();
    }

    /// <summary>
    /// Progress information for ongoing sync operations
    /// </summary>
    public class SyncProgress
    {
        public Guid SyncId { get; set; }
        public SyncType SyncType { get; set; }
        public int TotalRecords { get; set; }
        public int ProcessedRecords { get; set; }
        public int PercentageComplete { get; set; }
        public string CurrentStep { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public TimeSpan ElapsedTime { get; set; }
        public TimeSpan? EstimatedTimeRemaining { get; set; }
        public double RecordsPerSecond { get; set; }
    }

    #endregion

    #region Event Models

    /// <summary>
    /// Event arguments for sync progress updates
    /// </summary>
    public class SyncProgressEventArgs : EventArgs
    {
        public SyncProgress Progress { get; set; } = new();
    }

    /// <summary>
    /// Event arguments for sync completion
    /// </summary>
    public class SyncCompletedEventArgs : EventArgs
    {
        public SyncResult Result { get; set; } = new();
    }

    #endregion
}
