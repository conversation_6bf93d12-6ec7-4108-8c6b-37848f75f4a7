namespace Forms.ktech.Models
{
    /// <summary>
    /// Statistics for document approvals used in admin dashboard
    /// </summary>
    public class DocumentApprovalStatistics
    {
        /// <summary>
        /// Total number of approval records
        /// </summary>
        public int TotalApprovals { get; set; }
        
        /// <summary>
        /// Number of documents pending approval
        /// </summary>
        public int PendingApprovals { get; set; }
        
        /// <summary>
        /// Number of approved documents
        /// </summary>
        public int ApprovedCount { get; set; }
        
        /// <summary>
        /// Number of disapproved documents
        /// </summary>
        public int DisapprovedCount { get; set; }
        
        /// <summary>
        /// Approval rate as a percentage
        /// </summary>
        public double ApprovalRate { get; set; }
        
        /// <summary>
        /// Number of documents under review
        /// </summary>
        public int UnderReviewCount { get; set; }
        
        /// <summary>
        /// Average processing time in hours
        /// </summary>
        public double AverageProcessingTimeHours { get; set; }
        
        /// <summary>
        /// Gets the approval rate as a formatted percentage string
        /// </summary>
        public string FormattedApprovalRate => $"{ApprovalRate:F1}%";
        
        /// <summary>
        /// Gets the formatted average processing time
        /// </summary>
        public string FormattedProcessingTime
        {
            get
            {
                if (AverageProcessingTimeHours < 1)
                {
                    return $"{AverageProcessingTimeHours * 60:F0} minutes";
                }
                else if (AverageProcessingTimeHours < 24)
                {
                    return $"{AverageProcessingTimeHours:F1} hours";
                }
                else
                {
                    return $"{AverageProcessingTimeHours / 24:F1} days";
                }
            }
        }
    }
}
