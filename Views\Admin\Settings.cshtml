@{
    ViewData["Title"] = "Admin Settings";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@section Breadcrumb {
    <li class="inline-flex items-center">
        <a asp-area="" asp-controller="Home" asp-action="Index" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
            <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
            </svg>
            Home
        </a>
    </li>
    <li>
        <div class="flex items-center">
            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
            </svg>
            <a asp-controller="Admin" asp-action="Dashboard" class="text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">Admin</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
            </svg>
            <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400">Settings</span>
        </div>
    </li>
}

<!-- Header Section -->
<div class="bg-white rounded-lg shadow-lg p-6 mb-6 dark:bg-gray-800">
    <div class="flex items-center">
        <div class="flex-shrink-0">
            <div class="flex items-center justify-center h-12 w-12 rounded-md bg-gray-500 text-white">
                <i class="fas fa-cog text-xl"></i>
            </div>
        </div>
        <div class="ml-4">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">System Settings</h1>
            <p class="text-gray-600 dark:text-gray-300">Configure system preferences and options</p>
        </div>
    </div>
</div>

<!-- Settings Grid -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
    <!-- General Settings Card -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <div class="flex items-center mb-4">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-10 w-10 rounded-md bg-blue-500 text-white">
                    <i class="fas fa-sliders-h"></i>
                </div>
            </div>
            <div class="ml-3">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">General</h3>
                <p class="text-sm text-gray-600 dark:text-gray-300">Basic system configuration</p>
            </div>
        </div>
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">System Name</label>
                <input type="text" value="Forms.ktech" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Default Language</label>
                <select class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option selected>English</option>
                    <option>Arabic</option>
                </select>
            </div>
            <button type="button" class="w-full text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
                Save General Settings
            </button>
        </div>
    </div>

    <!-- Security Settings Card -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <div class="flex items-center mb-4">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-10 w-10 rounded-md bg-red-500 text-white">
                    <i class="fas fa-shield-alt"></i>
                </div>
            </div>
            <div class="ml-3">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Security</h3>
                <p class="text-sm text-gray-600 dark:text-gray-300">Authentication and access control</p>
            </div>
        </div>
        <div class="space-y-4">
            <div class="flex items-center">
                <input id="require-2fa" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                <label for="require-2fa" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Require Two-Factor Authentication</label>
            </div>
            <div class="flex items-center">
                <input id="session-timeout" type="checkbox" checked class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                <label for="session-timeout" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Enable Session Timeout</label>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Session Timeout (minutes)</label>
                <input type="number" value="30" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
            </div>
            <button type="button" class="w-full text-white bg-red-700 hover:bg-red-800 focus:ring-4 focus:ring-red-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-red-600 dark:hover:bg-red-700 focus:outline-none dark:focus:ring-red-800">
                Save Security Settings
            </button>
        </div>
    </div>

    <!-- Notification Settings Card -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <div class="flex items-center mb-4">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-10 w-10 rounded-md bg-yellow-500 text-white">
                    <i class="fas fa-bell"></i>
                </div>
            </div>
            <div class="ml-3">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Notifications</h3>
                <p class="text-sm text-gray-600 dark:text-gray-300">Email and system notifications</p>
            </div>
        </div>
        <div class="space-y-4">
            <div class="flex items-center">
                <input id="email-notifications" type="checkbox" checked class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                <label for="email-notifications" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Email Notifications</label>
            </div>
            <div class="flex items-center">
                <input id="submission-alerts" type="checkbox" checked class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                <label for="submission-alerts" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">New Submission Alerts</label>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Admin Email</label>
                <input type="email" value="<EMAIL>" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
            </div>
            <button type="button" class="w-full text-white bg-yellow-700 hover:bg-yellow-800 focus:ring-4 focus:ring-yellow-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-yellow-600 dark:hover:bg-yellow-700 focus:outline-none dark:focus:ring-yellow-800">
                Save Notification Settings
            </button>
        </div>
    </div>

    <!-- Appearance Settings Card -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <div class="flex items-center mb-4">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-10 w-10 rounded-md bg-purple-500 text-white">
                    <i class="fas fa-palette"></i>
                </div>
            </div>
            <div class="ml-3">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Appearance</h3>
                <p class="text-sm text-gray-600 dark:text-gray-300">Theme and display preferences</p>
            </div>
        </div>
        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Theme</label>
                <select class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option selected>Light</option>
                    <option>Dark</option>
                    <option>Auto</option>
                </select>
            </div>
            <div class="flex items-center">
                <input id="compact-mode" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                <label for="compact-mode" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Compact Mode</label>
            </div>
            <div class="flex items-center">
                <input id="sidebar-collapsed" type="checkbox" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                <label for="sidebar-collapsed" class="ms-2 text-sm font-medium text-gray-900 dark:text-gray-300">Collapse Sidebar by Default</label>
            </div>
            <button type="button" class="w-full text-white bg-purple-700 hover:bg-purple-800 focus:ring-4 focus:ring-purple-300 font-medium rounded-lg text-sm px-5 py-2.5 dark:bg-purple-600 dark:hover:bg-purple-700 focus:outline-none dark:focus:ring-purple-800">
                Save Appearance Settings
            </button>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="mt-6 bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">System Information</h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="bg-gray-50 rounded-lg p-4 dark:bg-gray-700">
            <div class="text-sm text-gray-500 dark:text-gray-400">Version</div>
            <div class="text-lg font-semibold text-gray-900 dark:text-white">1.0.0</div>
        </div>
        <div class="bg-gray-50 rounded-lg p-4 dark:bg-gray-700">
            <div class="text-sm text-gray-500 dark:text-gray-400">Environment</div>
            <div class="text-lg font-semibold text-gray-900 dark:text-white">Production</div>
        </div>
        <div class="bg-gray-50 rounded-lg p-4 dark:bg-gray-700">
            <div class="text-sm text-gray-500 dark:text-gray-400">Last Updated</div>
            <div class="text-lg font-semibold text-gray-900 dark:text-white">@DateTime.Now.ToString("MMM dd, yyyy")</div>
        </div>
    </div>
</div>
