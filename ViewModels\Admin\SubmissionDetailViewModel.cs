using System.ComponentModel.DataAnnotations;
using Forms.ktech.Features.KuwaitiStudentInfo.Models;

namespace Forms.ktech.ViewModels.Admin
{
    /// <summary>
    /// View model for displaying detailed submission information in the admin area
    /// Contains all necessary data for comprehensive submission review
    /// </summary>
    public class SubmissionDetailViewModel
    {
        /// <summary>
        /// The main submission entity with all form data
        /// </summary>
        [Required]
        public StudentInfo Submission { get; set; } = null!;

        /// <summary>
        /// Information about all uploaded documents for this submission
        /// </summary>
        public List<DocumentInfo> DocumentInfo { get; set; } = new();

        /// <summary>
        /// SIS integration status and pre-filled field information
        /// </summary>
        public SisIntegrationInfo? SisIntegrationStatus { get; set; }

        /// <summary>
        /// Audit trail entries for administrative actions on this submission
        /// </summary>
        public List<AuditTrailEntry> AuditTrail { get; set; } = new();

        /// <summary>
        /// Statistical information about the submission
        /// </summary>
        public SubmissionStatistics Statistics { get; set; } = new();

        /// <summary>
        /// Navigation context for returning to previous page
        /// </summary>
        public NavigationContext NavigationContext { get; set; } = new();
    }



    /// <summary>
    /// Information about SIS data integration for the submission
    /// </summary>
    public class SisIntegrationInfo
    {
        /// <summary>
        /// Whether this submission is linked to a SIS student record
        /// </summary>
        public bool IsLinkedToSis { get; set; }

        /// <summary>
        /// When the SIS data was last synchronized
        /// </summary>
        public DateTime? LastSyncDate { get; set; }

        /// <summary>
        /// List of field names that were pre-filled from SIS data
        /// </summary>
        public List<string> PreFilledFields { get; set; } = new();

        /// <summary>
        /// List of field names where SIS data conflicts with user input
        /// </summary>
        public List<string> ConflictingFields { get; set; } = new();

        /// <summary>
        /// SIS student ID if linked
        /// </summary>
        public string? SisStudentId { get; set; }

        /// <summary>
        /// Full name from SIS (Arabic)
        /// </summary>
        public string? SisFullNameAR { get; set; }

        /// <summary>
        /// Full name from SIS (English)
        /// </summary>
        public string? SisFullNameEN { get; set; }

        /// <summary>
        /// Whether the SIS data is considered fresh (within threshold)
        /// </summary>
        public bool IsDataFresh { get; set; }

        /// <summary>
        /// Number of hours since last sync
        /// </summary>
        public int? HoursSinceLastSync
        {
            get
            {
                if (!LastSyncDate.HasValue) return null;
                return (int)(DateTime.UtcNow - LastSyncDate.Value).TotalHours;
            }
        }
    }

    /// <summary>
    /// Audit trail entry for administrative actions
    /// </summary>
    public class AuditTrailEntry
    {
        /// <summary>
        /// Unique identifier for the audit entry
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// ID of the submission this audit entry relates to
        /// </summary>
        public int SubmissionId { get; set; }

        /// <summary>
        /// User ID of the admin who performed the action
        /// </summary>
        [Required]
        public string AdminUserId { get; set; } = string.Empty;

        /// <summary>
        /// Display name of the admin user
        /// </summary>
        public string? AdminDisplayName { get; set; }

        /// <summary>
        /// Action that was performed
        /// </summary>
        [Required]
        public string Action { get; set; } = string.Empty;

        /// <summary>
        /// When the action was performed
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// IP address of the admin user
        /// </summary>
        public string? IpAddress { get; set; }

        /// <summary>
        /// Additional details about the action
        /// </summary>
        public string? Details { get; set; }
    }

    /// <summary>
    /// Statistical information about the submission
    /// </summary>
    public class SubmissionStatistics
    {
        /// <summary>
        /// Total number of documents uploaded
        /// </summary>
        public int TotalDocuments { get; set; }

        /// <summary>
        /// Number of required documents uploaded
        /// </summary>
        public int RequiredDocuments { get; set; }

        /// <summary>
        /// Number of optional documents uploaded
        /// </summary>
        public int OptionalDocuments { get; set; }

        /// <summary>
        /// Total size of all uploaded files in bytes
        /// </summary>
        public long TotalFileSize { get; set; }

        /// <summary>
        /// Number of times this submission has been viewed by admins
        /// </summary>
        public int AdminViewCount { get; set; }

        /// <summary>
        /// When this submission was last viewed by an admin
        /// </summary>
        public DateTime? LastAdminView { get; set; }

        /// <summary>
        /// Gets formatted total file size for display
        /// </summary>
        public string FormattedTotalFileSize
        {
            get
            {
                if (TotalFileSize == 0) return "0 B";
                
                string[] sizes = { "B", "KB", "MB", "GB" };
                int order = 0;
                double size = TotalFileSize;
                
                while (size >= 1024 && order < sizes.Length - 1)
                {
                    order++;
                    size /= 1024;
                }
                
                return $"{size:0.##} {sizes[order]}";
            }
        }
    }

    /// <summary>
    /// Navigation context for maintaining state across admin pages
    /// </summary>
    public class NavigationContext
    {
        /// <summary>
        /// URL to return to (e.g., submissions list with filters)
        /// </summary>
        public string? ReturnUrl { get; set; }

        /// <summary>
        /// Display text for the return link
        /// </summary>
        public string ReturnText { get; set; } = "Back to Submissions / العودة إلى الطلبات";

        /// <summary>
        /// Current page number in pagination context
        /// </summary>
        public int? CurrentPage { get; set; }

        /// <summary>
        /// Applied filters for context preservation
        /// </summary>
        public Dictionary<string, string> AppliedFilters { get; set; } = new();
    }
}
