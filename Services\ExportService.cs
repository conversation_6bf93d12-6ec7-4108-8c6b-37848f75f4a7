using Forms.ktech.Models;
using Forms.ktech.Services;
using Forms.ktech.ViewModels.Admin;
using Forms.ktech.Features.KuwaitiStudentInfo.Models;
using iTextSharp.text;
using iTextSharp.text.pdf;
using OfficeOpenXml;
using OfficeOpenXml.Style;
using System.Drawing;
using System.Text;

namespace Forms.ktech.Services
{
    /// <summary>
    /// Service for exporting submission data to various formats
    /// </summary>
    public class ExportService : IExportService
    {
        private readonly ILogger<ExportService> _logger;

        public ExportService(ILogger<ExportService> logger)
        {
            _logger = logger;
            
            // Set EPPlus license context
            ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
        }

        /// <summary>
        /// Exports a single submission to PDF format
        /// </summary>
        public async Task<byte[]> ExportSubmissionToPdfAsync(StudentInfo submission, DocumentInfo documentInfo, SisIntegrationStatus sisIntegrationStatus)
        {
            try
            {
                using var memoryStream = new MemoryStream();
                var document = new Document(PageSize.A4, 50, 50, 50, 50);
                var writer = PdfWriter.GetInstance(document, memoryStream);
                
                document.Open();

                // Add title
                var titleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 18, BaseColor.DARK_GRAY);
                var title = new Paragraph($"Student Information Submission - #{submission.Id}", titleFont)
                {
                    Alignment = Element.ALIGN_CENTER,
                    SpacingAfter = 20
                };
                document.Add(title);

                // Add export metadata
                var metadataFont = FontFactory.GetFont(FontFactory.HELVETICA, 10, BaseColor.GRAY);
                var metadata = new Paragraph($"Exported on: {DateTime.Now:yyyy-MM-dd HH:mm} UTC", metadataFont)
                {
                    Alignment = Element.ALIGN_RIGHT,
                    SpacingAfter = 20
                };
                document.Add(metadata);

                // Add submission details
                await AddSubmissionDetailsToPdf(document, submission);
                
                // Add document information
                AddDocumentInfoToPdf(document, documentInfo);
                
                // Add SIS integration status
                AddSisIntegrationToPdf(document, sisIntegrationStatus);

                document.Close();
                return memoryStream.ToArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting submission {SubmissionId} to PDF", submission.Id);
                throw;
            }
        }

        /// <summary>
        /// Exports a single submission to Excel format
        /// </summary>
        public async Task<byte[]> ExportSubmissionToExcelAsync(StudentInfo submission, DocumentInfo documentInfo, SisIntegrationStatus sisIntegrationStatus)
        {
            try
            {
                using var package = new ExcelPackage();
                
                // Create worksheets
                var overviewSheet = package.Workbook.Worksheets.Add("Overview");
                var documentsSheet = package.Workbook.Worksheets.Add("Documents");
                var sisSheet = package.Workbook.Worksheets.Add("SIS Integration");

                // Populate overview sheet
                await PopulateOverviewSheet(overviewSheet, submission);
                
                // Populate documents sheet
                PopulateDocumentsSheet(documentsSheet, documentInfo);
                
                // Populate SIS integration sheet
                PopulateSisIntegrationSheet(sisSheet, sisIntegrationStatus);

                return package.GetAsByteArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting submission {SubmissionId} to Excel", submission.Id);
                throw;
            }
        }

        /// <summary>
        /// Exports multiple submissions to Excel format
        /// </summary>
        public async Task<byte[]> ExportMultipleSubmissionsToExcelAsync(IEnumerable<StudentInfo> submissions)
        {
            try
            {
                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("Submissions");

                // Add headers
                var headers = new[]
                {
                    "Submission ID", "Civil ID", "Student Name (English)", "Student Name (Arabic)",
                    "Father Name (English)", "Father Name (Arabic)", "Mother Name (English)", "Mother Name (Arabic)",
                    "Mobile Number", "Email", "Submission Date", "Status"
                };

                for (int i = 0; i < headers.Length; i++)
                {
                    worksheet.Cells[1, i + 1].Value = headers[i];
                    worksheet.Cells[1, i + 1].Style.Font.Bold = true;
                    worksheet.Cells[1, i + 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    worksheet.Cells[1, i + 1].Style.Fill.BackgroundColor.SetColor(Color.LightGray);
                }

                // Add data
                int row = 2;
                foreach (var submission in submissions)
                {
                    worksheet.Cells[row, 1].Value = submission.Id;
                    worksheet.Cells[row, 2].Value = submission.GetStudentCivilId();
                    worksheet.Cells[row, 3].Value = submission.GetStudentName();
                    worksheet.Cells[row, 4].Value = submission.SisStudent?.FullNameAR ?? "";
                    worksheet.Cells[row, 5].Value = submission.FatherName;
                    worksheet.Cells[row, 6].Value = submission.FatherName; // No separate Arabic field
                    worksheet.Cells[row, 7].Value = submission.MotherName;
                    worksheet.Cells[row, 8].Value = submission.MotherName; // No separate Arabic field
                    worksheet.Cells[row, 9].Value = submission.StudentMobileNumber;
                    worksheet.Cells[row, 10].Value = submission.CreatedByUserId; // No email field
                    worksheet.Cells[row, 11].Value = submission.CreatedDate.ToString("yyyy-MM-dd HH:mm");
                    worksheet.Cells[row, 12].Value = "Submitted"; // Default status
                    row++;
                }

                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();

                return package.GetAsByteArray();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting multiple submissions to Excel");
                throw;
            }
        }

        /// <summary>
        /// Generates a secure filename based on Civil ID and export type
        /// </summary>
        public string GenerateSecureFilename(string civilId, string exportType, bool isMultiple = false)
        {
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var sanitizedCivilId = string.IsNullOrEmpty(civilId) ? "Unknown" : new string(civilId.Where(char.IsLetterOrDigit).ToArray());

            if (isMultiple)
            {
                return $"Submissions_Export_{timestamp}.{exportType.ToLower()}";
            }

            return $"Submission_{sanitizedCivilId}_{timestamp}.{exportType.ToLower()}";
        }

        /// <summary>
        /// Adds submission details to PDF document
        /// </summary>
        private async Task AddSubmissionDetailsToPdf(Document document, StudentInfo submission)
        {
            var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 14, BaseColor.BLACK);
            var labelFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 10, BaseColor.BLACK);
            var valueFont = FontFactory.GetFont(FontFactory.HELVETICA, 10, BaseColor.BLACK);

            // Student Information Section
            var studentHeader = new Paragraph("Student Information / معلومات الطالب", headerFont)
            {
                SpacingBefore = 10,
                SpacingAfter = 10
            };
            document.Add(studentHeader);

            var studentTable = new PdfPTable(2) { WidthPercentage = 100 };
            studentTable.SetWidths(new float[] { 30, 70 });

            AddTableRow(studentTable, "Civil ID / الرقم المدني:", submission.GetStudentCivilId(), labelFont, valueFont);
            AddTableRow(studentTable, "Student Name (English):", submission.GetStudentName(), labelFont, valueFont);
            AddTableRow(studentTable, "Student Name (Arabic):", submission.SisStudent?.FullNameAR ?? "N/A", labelFont, valueFont);
            AddTableRow(studentTable, "Mobile Number / رقم الهاتف:", submission.StudentMobileNumber, labelFont, valueFont);
            AddTableRow(studentTable, "Email / البريد الإلكتروني:", submission.CreatedByUserId, labelFont, valueFont);

            document.Add(studentTable);

            // Father Information Section
            var fatherHeader = new Paragraph("Father Information / معلومات الأب", headerFont)
            {
                SpacingBefore = 15,
                SpacingAfter = 10
            };
            document.Add(fatherHeader);

            var fatherTable = new PdfPTable(2) { WidthPercentage = 100 };
            fatherTable.SetWidths(new float[] { 30, 70 });

            AddTableRow(fatherTable, "Father Name:", submission.FatherName, labelFont, valueFont);
            AddTableRow(fatherTable, "Father Is Kuwaiti / الأب كويتي:", submission.FatherIsKuwaiti ? "Yes / نعم" : "No / لا", labelFont, valueFont);
            AddTableRow(fatherTable, "Father Deceased / الأب متوفى:", submission.FatherIsDeceased ? "Yes / نعم" : "No / لا", labelFont, valueFont);

            document.Add(fatherTable);

            // Mother Information Section
            var motherHeader = new Paragraph("Mother Information / معلومات الأم", headerFont)
            {
                SpacingBefore = 15,
                SpacingAfter = 10
            };
            document.Add(motherHeader);

            var motherTable = new PdfPTable(2) { WidthPercentage = 100 };
            motherTable.SetWidths(new float[] { 30, 70 });

            AddTableRow(motherTable, "Mother Name:", submission.MotherName, labelFont, valueFont);
            AddTableRow(motherTable, "Mother Is Kuwaiti / الأم كويتية:", submission.MotherIsKuwaiti ? "Yes / نعم" : "No / لا", labelFont, valueFont);
            AddTableRow(motherTable, "Mother Deceased / الأم متوفاة:", submission.MotherIsDeceased ? "Yes / نعم" : "No / لا", labelFont, valueFont);

            document.Add(motherTable);

            // Submission Metadata
            var metaHeader = new Paragraph("Submission Details / تفاصيل الطلب", headerFont)
            {
                SpacingBefore = 15,
                SpacingAfter = 10
            };
            document.Add(metaHeader);

            var metaTable = new PdfPTable(2) { WidthPercentage = 100 };
            metaTable.SetWidths(new float[] { 30, 70 });

            AddTableRow(metaTable, "Submission ID:", submission.Id.ToString(), labelFont, valueFont);
            AddTableRow(metaTable, "Submission Date:", submission.CreatedDate.ToString("yyyy-MM-dd HH:mm"), labelFont, valueFont);
            AddTableRow(metaTable, "Last Updated:", submission.UpdatedDate?.ToString("yyyy-MM-dd HH:mm") ?? "N/A", labelFont, valueFont);

            document.Add(metaTable);
        }

        /// <summary>
        /// Adds document information to PDF
        /// </summary>
        private void AddDocumentInfoToPdf(Document document, DocumentInfo documentInfo)
        {
            var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 14, BaseColor.BLACK);
            var labelFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 10, BaseColor.BLACK);
            var valueFont = FontFactory.GetFont(FontFactory.HELVETICA, 10, BaseColor.BLACK);

            var docHeader = new Paragraph("Document Information / معلومات المستندات", headerFont)
            {
                SpacingBefore = 20,
                SpacingAfter = 10
            };
            document.Add(docHeader);

            var docTable = new PdfPTable(4) { WidthPercentage = 100 };
            docTable.SetWidths(new float[] { 30, 20, 20, 30 });

            // Add headers
            AddTableCell(docTable, "Document Type", labelFont, BaseColor.LIGHT_GRAY);
            AddTableCell(docTable, "Status", labelFont, BaseColor.LIGHT_GRAY);
            AddTableCell(docTable, "Required", labelFont, BaseColor.LIGHT_GRAY);
            AddTableCell(docTable, "File Size", labelFont, BaseColor.LIGHT_GRAY);

            // Add document rows
            foreach (var doc in documentInfo.Documents)
            {
                AddTableCell(docTable, doc.DisplayName, valueFont);
                AddTableCell(docTable, doc.IsUploaded ? "Uploaded" : "Missing", valueFont);
                AddTableCell(docTable, doc.IsRequired ? "Yes" : "No", valueFont);
                AddTableCell(docTable, doc.FormattedFileSize, valueFont);
            }

            document.Add(docTable);
        }

        /// <summary>
        /// Adds SIS integration status to PDF
        /// </summary>
        private void AddSisIntegrationToPdf(Document document, SisIntegrationStatus sisIntegrationStatus)
        {
            var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 14, BaseColor.BLACK);
            var labelFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 10, BaseColor.BLACK);
            var valueFont = FontFactory.GetFont(FontFactory.HELVETICA, 10, BaseColor.BLACK);

            var sisHeader = new Paragraph("SIS Integration Status / حالة التكامل مع نظام معلومات الطلاب", headerFont)
            {
                SpacingBefore = 20,
                SpacingAfter = 10
            };
            document.Add(sisHeader);

            var sisTable = new PdfPTable(2) { WidthPercentage = 100 };
            sisTable.SetWidths(new float[] { 30, 70 });

            AddTableRow(sisTable, "Integration Status:", sisIntegrationStatus.IsIntegrated ? "Integrated" : "Not Integrated", labelFont, valueFont);
            AddTableRow(sisTable, "Last Sync:", sisIntegrationStatus.LastSyncDate?.ToString("yyyy-MM-dd HH:mm") ?? "Never", labelFont, valueFont);

            if (!string.IsNullOrEmpty(sisIntegrationStatus.ErrorMessage))
            {
                AddTableRow(sisTable, "Error Message:", sisIntegrationStatus.ErrorMessage, labelFont, valueFont);
            }

            document.Add(sisTable);
        }

        /// <summary>
        /// Helper method to add a row to PDF table
        /// </summary>
        private void AddTableRow(PdfPTable table, string label, string value, iTextSharp.text.Font labelFont, iTextSharp.text.Font valueFont)
        {
            AddTableCell(table, label, labelFont);
            AddTableCell(table, value ?? "N/A", valueFont);
        }

        /// <summary>
        /// Helper method to add a cell to PDF table
        /// </summary>
        private void AddTableCell(PdfPTable table, string text, iTextSharp.text.Font font, BaseColor? backgroundColor = null)
        {
            var cell = new PdfPCell(new Phrase(text, font))
            {
                Padding = 5,
                Border = iTextSharp.text.Rectangle.BOX,
                BorderColor = BaseColor.GRAY
            };

            if (backgroundColor != null)
            {
                cell.BackgroundColor = backgroundColor;
            }

            table.AddCell(cell);
        }

        /// <summary>
        /// Populates the overview sheet in Excel export
        /// </summary>
        private async Task PopulateOverviewSheet(ExcelWorksheet worksheet, StudentInfo submission)
        {
            worksheet.Cells["A1"].Value = "Student Information Submission";
            worksheet.Cells["A1"].Style.Font.Size = 16;
            worksheet.Cells["A1"].Style.Font.Bold = true;
            worksheet.Cells["A1:B1"].Merge = true;

            int row = 3;

            // Student Information
            worksheet.Cells[row, 1].Value = "Student Information";
            worksheet.Cells[row, 1].Style.Font.Bold = true;
            worksheet.Cells[row, 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
            worksheet.Cells[row, 1].Style.Fill.BackgroundColor.SetColor(Color.LightBlue);
            worksheet.Cells[row, 1, row, 2].Merge = true;
            row++;

            AddExcelRow(worksheet, ref row, "Civil ID:", submission.GetStudentCivilId());
            AddExcelRow(worksheet, ref row, "Student Name (English):", submission.GetStudentName());
            AddExcelRow(worksheet, ref row, "Student Name (Arabic):", submission.SisStudent?.FullNameAR ?? "N/A");
            AddExcelRow(worksheet, ref row, "Mobile Number:", submission.StudentMobileNumber);
            AddExcelRow(worksheet, ref row, "User ID:", submission.CreatedByUserId);

            row++;

            // Father Information
            worksheet.Cells[row, 1].Value = "Father Information";
            worksheet.Cells[row, 1].Style.Font.Bold = true;
            worksheet.Cells[row, 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
            worksheet.Cells[row, 1].Style.Fill.BackgroundColor.SetColor(Color.LightGreen);
            worksheet.Cells[row, 1, row, 2].Merge = true;
            row++;

            AddExcelRow(worksheet, ref row, "Father Name:", submission.FatherName);
            AddExcelRow(worksheet, ref row, "Father Is Kuwaiti:", submission.FatherIsKuwaiti ? "Yes" : "No");
            AddExcelRow(worksheet, ref row, "Father Deceased:", submission.FatherIsDeceased ? "Yes" : "No");

            row++;

            // Mother Information
            worksheet.Cells[row, 1].Value = "Mother Information";
            worksheet.Cells[row, 1].Style.Font.Bold = true;
            worksheet.Cells[row, 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
            worksheet.Cells[row, 1].Style.Fill.BackgroundColor.SetColor(Color.LightCoral);
            worksheet.Cells[row, 1, row, 2].Merge = true;
            row++;

            AddExcelRow(worksheet, ref row, "Mother Name:", submission.MotherName);
            AddExcelRow(worksheet, ref row, "Mother Is Kuwaiti:", submission.MotherIsKuwaiti ? "Yes" : "No");
            AddExcelRow(worksheet, ref row, "Mother Deceased:", submission.MotherIsDeceased ? "Yes" : "No");

            row++;

            // Submission Details
            worksheet.Cells[row, 1].Value = "Submission Details";
            worksheet.Cells[row, 1].Style.Font.Bold = true;
            worksheet.Cells[row, 1].Style.Fill.PatternType = ExcelFillStyle.Solid;
            worksheet.Cells[row, 1].Style.Fill.BackgroundColor.SetColor(Color.LightYellow);
            worksheet.Cells[row, 1, row, 2].Merge = true;
            row++;

            AddExcelRow(worksheet, ref row, "Submission ID:", submission.Id.ToString());
            AddExcelRow(worksheet, ref row, "Submission Date:", submission.CreatedDate.ToString("yyyy-MM-dd HH:mm"));
            AddExcelRow(worksheet, ref row, "Last Updated:", submission.UpdatedDate?.ToString("yyyy-MM-dd HH:mm") ?? "N/A");

            worksheet.Cells.AutoFitColumns();
        }

        /// <summary>
        /// Populates the documents sheet in Excel export
        /// </summary>
        private void PopulateDocumentsSheet(ExcelWorksheet worksheet, DocumentInfo documentInfo)
        {
            worksheet.Cells["A1"].Value = "Document Information";
            worksheet.Cells["A1"].Style.Font.Size = 16;
            worksheet.Cells["A1"].Style.Font.Bold = true;
            worksheet.Cells["A1:D1"].Merge = true;

            // Headers
            worksheet.Cells["A3"].Value = "Document Type";
            worksheet.Cells["B3"].Value = "Status";
            worksheet.Cells["C3"].Value = "Required";
            worksheet.Cells["D3"].Value = "File Size";

            var headerRange = worksheet.Cells["A3:D3"];
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Fill.PatternType = ExcelFillStyle.Solid;
            headerRange.Style.Fill.BackgroundColor.SetColor(Color.LightGray);

            // Data
            int row = 4;
            foreach (var doc in documentInfo.Documents)
            {
                worksheet.Cells[row, 1].Value = doc.DisplayName;
                worksheet.Cells[row, 2].Value = doc.IsUploaded ? "Uploaded" : "Missing";
                worksheet.Cells[row, 3].Value = doc.IsRequired ? "Yes" : "No";
                worksheet.Cells[row, 4].Value = doc.FormattedFileSize;

                // Color code status
                if (doc.IsUploaded)
                {
                    worksheet.Cells[row, 2].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    worksheet.Cells[row, 2].Style.Fill.BackgroundColor.SetColor(Color.LightGreen);
                }
                else if (doc.IsRequired)
                {
                    worksheet.Cells[row, 2].Style.Fill.PatternType = ExcelFillStyle.Solid;
                    worksheet.Cells[row, 2].Style.Fill.BackgroundColor.SetColor(Color.LightPink);
                }

                row++;
            }

            worksheet.Cells.AutoFitColumns();
        }

        /// <summary>
        /// Populates the SIS integration sheet in Excel export
        /// </summary>
        private void PopulateSisIntegrationSheet(ExcelWorksheet worksheet, SisIntegrationStatus sisIntegrationStatus)
        {
            worksheet.Cells["A1"].Value = "SIS Integration Status";
            worksheet.Cells["A1"].Style.Font.Size = 16;
            worksheet.Cells["A1"].Style.Font.Bold = true;
            worksheet.Cells["A1:B1"].Merge = true;

            int row = 3;

            AddExcelRow(worksheet, ref row, "Integration Status:", sisIntegrationStatus.IsIntegrated ? "Integrated" : "Not Integrated");
            AddExcelRow(worksheet, ref row, "Last Sync:", sisIntegrationStatus.LastSyncDate?.ToString("yyyy-MM-dd HH:mm") ?? "Never");

            if (!string.IsNullOrEmpty(sisIntegrationStatus.ErrorMessage))
            {
                AddExcelRow(worksheet, ref row, "Error Message:", sisIntegrationStatus.ErrorMessage);
            }

            worksheet.Cells.AutoFitColumns();
        }

        /// <summary>
        /// Helper method to add a row to Excel worksheet
        /// </summary>
        private void AddExcelRow(ExcelWorksheet worksheet, ref int row, string label, string value)
        {
            worksheet.Cells[row, 1].Value = label;
            worksheet.Cells[row, 1].Style.Font.Bold = true;
            worksheet.Cells[row, 2].Value = value ?? "N/A";
            row++;
        }
    }
}
