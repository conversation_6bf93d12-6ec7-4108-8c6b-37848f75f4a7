@*
    Reusable loading spinner component
    Parameters:
    - message: Loading message to display (optional)
    - size: Size of spinner - "sm", "md", "lg" (default: "md")
    - overlay: Whether to show as full-screen overlay (default: false)
    - id: Custom ID for the spinner element
*@

@{
    var message = ViewData["message"]?.ToString() ?? "Loading...";
    var size = ViewData["size"]?.ToString() ?? "md";
    var overlay = (bool)(ViewData["overlay"] ?? false);
    var id = ViewData["id"]?.ToString() ?? "loading-spinner";

    var spinnerClass = size switch
    {
        "sm" => "w-4 h-4",
        "lg" => "w-12 h-12",
        _ => "w-8 h-8"
    };

    var containerClass = overlay ? "loading-overlay" : "loading-inline";
}

<div id="@id" class="@containerClass hidden" role="status" aria-live="polite">
    @if (overlay)
    {
        <div class="overlay-backdrop"></div>
    }

    <div class="loading-content text-center">
        <div class="flex justify-center mb-4">
            <svg aria-hidden="true" class="@spinnerClass text-gray-200 animate-spin dark:text-gray-600 fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/>
                <path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/>
            </svg>
            <span class="sr-only">Loading...</span>
        </div>

        <div class="loading-message">
            <p class="text-sm text-gray-500 dark:text-gray-400">@message</p>
        </div>

        @if (overlay)
        {
            <div class="mt-3">
                <p class="text-xs text-gray-400 dark:text-gray-500">Please wait while we process your request...</p>
            </div>
        }
    </div>
</div>

<script>
    // Loading spinner utility functions
    window.LoadingSpinner = {
        show: function (spinnerId = '@id', message = null) {
            const spinner = document.getElementById(spinnerId);
            if (spinner) {
                if (message) {
                    const messageElement = spinner.querySelector('.loading-message p');
                    if (messageElement) {
                        messageElement.textContent = message;
                    }
                }
                spinner.classList.remove('hidden');

                // Disable form inputs when overlay is shown
                if (spinner.classList.contains('loading-overlay')) {
                    document.body.style.overflow = 'hidden';
                    const formElements = document.querySelectorAll('input, button, select, textarea');
                    formElements.forEach(el => {
                        if (!el.disabled) {
                            el.setAttribute('data-was-enabled', 'true');
                            el.disabled = true;
                        }
                    });
                }
            }
        },

        hide: function (spinnerId = '@id') {
            const spinner = document.getElementById(spinnerId);
            if (spinner) {
                spinner.classList.add('hidden');

                // Re-enable form inputs
                if (spinner.classList.contains('loading-overlay')) {
                    document.body.style.overflow = '';
                    const formElements = document.querySelectorAll('[data-was-enabled="true"]');
                    formElements.forEach(el => {
                        el.disabled = false;
                        el.removeAttribute('data-was-enabled');
                    });
                }
            }
        },

        toggle: function (spinnerId = '@id', message = null) {
            const spinner = document.getElementById(spinnerId);
            if (spinner) {
                if (spinner.classList.contains('hidden')) {
                    this.show(spinnerId, message);
                } else {
                    this.hide(spinnerId);
                }
            }
        }
    };

    // Auto-hide spinner on page unload
    window.addEventListener('beforeunload', function () {
        LoadingSpinner.hide('@id');
    });
</script>

<style>
    .loading-overlay {
        @@apply fixed inset-0 z-50 flex items-center justify-center;
    }

    .overlay-backdrop {
        @@apply absolute inset-0 bg-white bg-opacity-90 backdrop-blur-sm;
    }

    .loading-content {
        @@apply relative z-50 bg-white p-8 rounded-lg shadow-lg min-w-[200px];
    }

    .loading-inline {
        @@apply p-4 text-center;
    }

    /* Animation for smooth appearance */
    .loading-overlay:not(.hidden) {
        animation: fadeIn 0.3s ease-in-out;
    }

    .loading-inline:not(.hidden) {
        animation: slideDown 0.3s ease-in-out;
    }

    @@keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    @@keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Responsive adjustments */
    @@media (max-width: 576px) {
        .loading-content {
            @@apply p-6 m-4 min-w-0 w-auto;
        }
    }
</style>
