namespace Forms.ktech.ViewModels.Admin
{
    /// <summary>
    /// Contains information about SIS integration status for a submission
    /// </summary>
    public class SisIntegrationStatus
    {
        /// <summary>
        /// Whether the submission is integrated with SIS
        /// </summary>
        public bool IsIntegrated { get; set; }

        /// <summary>
        /// Whether the submission is linked to a SIS student record
        /// </summary>
        public bool IsLinkedToSis { get; set; }

        /// <summary>
        /// SIS student ID
        /// </summary>
        public string? SisStudentId { get; set; }

        /// <summary>
        /// Full name in English from SIS
        /// </summary>
        public string? SisFullNameEN { get; set; }

        /// <summary>
        /// Full name in Arabic from SIS
        /// </summary>
        public string? SisFullNameAR { get; set; }

        /// <summary>
        /// Last synchronization date with SIS
        /// </summary>
        public DateTime? LastSyncDate { get; set; }

        /// <summary>
        /// List of fields that were pre-filled from SIS
        /// </summary>
        public List<string> PreFilledFields { get; set; } = new List<string>();

        /// <summary>
        /// Error message if integration failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Whether there was an error during integration
        /// </summary>
        public bool HasError => !string.IsNullOrEmpty(ErrorMessage);

        /// <summary>
        /// Integration status display text
        /// </summary>
        public string StatusDisplayText
        {
            get
            {
                if (HasError)
                    return "Integration Error / خطأ في التكامل";
                
                if (IsLinkedToSis)
                    return "Linked to SIS / مرتبط بنظام الطلاب";
                
                return "Not Linked / غير مرتبط";
            }
        }

        /// <summary>
        /// CSS class for status badge
        /// </summary>
        public string StatusBadgeClass
        {
            get
            {
                if (HasError)
                    return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
                
                if (IsLinkedToSis)
                    return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
                
                return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
            }
        }
    }
}
