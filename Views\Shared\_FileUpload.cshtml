@using System.IO
@*
    Reusable file upload component with drag-and-drop functionality
    Parameters:
    - name: The input name/id
    - label: Display label for the file input
    - required: Whether the file is required
    - accept: Accepted file types (e.g., ".pdf,.jpg,.png")
    - maxSize: Maximum file size in MB (default: 5)
    - helpText: Additional help text to display
*@

@{
    var name = ViewData["name"]?.ToString() ?? "file";
    var label = ViewData["label"]?.ToString() ?? "Choose File";
    var required = (bool)(ViewData["required"] ?? false);
    var accept = ViewData["accept"]?.ToString() ?? ".pdf,.jpg,.jpeg,.png";
    var maxSize = (int)(ViewData["maxSize"] ?? 5);
    var helpText = ViewData["helpText"]?.ToString();
    var currentFile = ViewData["currentFile"]?.ToString();
    var inputId = $"file-{name}";
    var dropZoneId = $"drop-zone-{name}";
}

<div class="mb-6">
    <label for="@inputId" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
        @label
        @if (required)
        {
            <span class="text-red-500">*</span>
        }
    </label>

    <div class="file-upload-container">
        <!-- Drop Zone -->
        <div id="@dropZoneId" class="flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-bray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-600">
            <div class="flex flex-col items-center justify-center pt-5 pb-6">
                <svg class="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
                </svg>
                <p class="mb-2 text-sm text-gray-500 dark:text-gray-400">
                    <span class="font-semibold">Click to upload</span> or drag and drop
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400">
                    @accept.Replace(",", ", ") (MAX. @maxSize MB)
                </p>
            </div>

            <!-- File Input -->
            <input type="file"
                   id="@inputId"
                   name="@name"
                   accept="@accept"
                   class="hidden"
                   @(required ? "required" : "")
                   data-max-size="@maxSize" />
        </div>
        
        <!-- File Preview -->
        <div id="@(inputId)-preview" class="file-preview mt-4 hidden">
            <div class="flex items-center p-4 text-sm text-blue-800 border border-blue-300 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400 dark:border-blue-800">
                <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                </svg>
                <div class="flex-grow">
                    <span class="font-medium file-name"></span>
                    <div class="text-xs file-size"></div>
                </div>
                <button type="button" class="ms-auto -mx-1.5 -my-1.5 bg-blue-50 text-blue-500 rounded-lg focus:ring-2 focus:ring-blue-400 p-1.5 hover:bg-blue-200 inline-flex items-center justify-center h-8 w-8 dark:bg-gray-800 dark:text-blue-400 dark:hover:bg-gray-700" onclick="clearFile('@inputId')">
                    <span class="sr-only">Remove file</span>
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Current File (for edit scenarios) -->
        @if (!string.IsNullOrEmpty(currentFile))
        {
            <div class="current-file mt-4">
                <div class="flex items-center p-4 text-sm text-green-800 border border-green-300 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400 dark:border-green-800">
                    <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z"/>
                    </svg>
                    <div class="flex-grow">
                        <span class="font-medium">Current file:</span> @System.IO.Path.GetFileName(currentFile)
                    </div>
                    <a href="@Url.Action("DownloadFile", "FileDownload", new { path = currentFile })"
                       class="text-green-800 bg-transparent border border-green-800 hover:bg-green-900 hover:text-white focus:ring-4 focus:outline-none focus:ring-green-300 font-medium rounded-lg text-xs px-3 py-1.5 text-center dark:hover:bg-green-600 dark:border-green-600 dark:text-green-400 dark:hover:text-white dark:focus:ring-green-800" target="_blank">
                        <i class="fas fa-download me-1"></i> Download
                    </a>
                </div>
            </div>
        }

        <!-- Validation Message -->
        <div class="mt-2">
            <span asp-validation-for="@name" class="text-sm text-red-600 dark:text-red-500"></span>
        </div>
    </div>
    
    @if (!string.IsNullOrEmpty(helpText))
    {
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-300">@helpText</p>
    }
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeFileUpload('@inputId', '@dropZoneId', @maxSize);
});

function initializeFileUpload(inputId, dropZoneId, maxSize) {
    const fileInput = document.getElementById(inputId);
    const dropZone = document.getElementById(dropZoneId);
    const preview = document.getElementById(inputId + '-preview');

    // Click to upload
    dropZone.addEventListener('click', function() {
        fileInput.click();
    });

    // File input change event
    fileInput.addEventListener('change', function(e) {
        handleFileSelect(e.target.files[0], inputId, maxSize);
    });

    // Drag and drop events
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        dropZone.classList.add('border-blue-500', 'bg-blue-50');
        dropZone.classList.remove('border-gray-300', 'bg-gray-50');
    });

    dropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        dropZone.classList.remove('border-blue-500', 'bg-blue-50');
        dropZone.classList.add('border-gray-300', 'bg-gray-50');
    });

    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        dropZone.classList.remove('border-blue-500', 'bg-blue-50');
        dropZone.classList.add('border-gray-300', 'bg-gray-50');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect(files[0], inputId, maxSize);
        }
    });
}

function handleFileSelect(file, inputId, maxSize) {
    const preview = document.getElementById(inputId + '-preview');
    const fileName = preview.querySelector('.file-name');
    const fileSize = preview.querySelector('.file-size');

    if (file) {
        // Validate file size
        const fileSizeInMB = file.size / (1024 * 1024);
        if (fileSizeInMB > maxSize) {
            alert(`File size (${fileSizeInMB.toFixed(2)} MB) exceeds the maximum allowed size of ${maxSize} MB.`);
            clearFile(inputId);
            return;
        }

        // Show preview
        fileName.textContent = file.name;
        fileSize.textContent = `${fileSizeInMB.toFixed(2)} MB`;
        preview.classList.remove('hidden');
    } else {
        preview.classList.add('hidden');
    }
}

function clearFile(inputId) {
    const fileInput = document.getElementById(inputId);
    const preview = document.getElementById(inputId + '-preview');

    fileInput.value = '';
    preview.classList.add('hidden');
}
</script>
