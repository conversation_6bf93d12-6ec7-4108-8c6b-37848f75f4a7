/* 
 * Shared Forms JavaScript - forms.ktech
 * Common JavaScript utilities used across multiple forms
 */

// ===== NAMESPACE =====
window.SharedForms = window.SharedForms || {};

// ===== FILE VALIDATION UTILITIES =====
SharedForms.FileValidation = {
    /**
     * Validates a file input based on size and type constraints
     * @param {HTMLInputElement} input - The file input element
     * @param {number} maxSizeMB - Maximum file size in MB
     * @returns {boolean} - True if valid, false otherwise
     */
    validateFileInput: function(input, maxSizeMB) {
        const file = input.files[0];
        const maxSize = maxSizeMB * 1024 * 1024; // Convert MB to bytes
        const allowedTypes = input.getAttribute('accept').split(',').map(type => type.trim());
        
        if (!file) {
            if (input.hasAttribute('required') || input.hasAttribute('data-conditionally-required')) {
                this.setValidationState(input, false);
                return false;
            }
            return true;
        }

        // Check file size
        if (file.size > maxSize) {
            alert(`File size must be less than ${maxSizeMB}MB`);
            input.value = '';
            this.setValidationState(input, false);
            return false;
        }

        // Check file type
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        if (!allowedTypes.includes(fileExtension)) {
            alert(`File type not allowed. Please upload: ${allowedTypes.join(', ')}`);
            input.value = '';
            this.setValidationState(input, false);
            return false;
        }

        this.setValidationState(input, true);
        return true;
    },

    /**
     * Sets the validation state visual feedback for an input
     * @param {HTMLInputElement} input - The input element
     * @param {boolean} isValid - Whether the input is valid
     */
    setValidationState: function(input, isValid) {
        input.classList.remove('border-green-500', 'border-red-500', 'focus:border-green-500', 'focus:border-red-500');
        if (isValid) {
            input.classList.add('border-green-500', 'focus:border-green-500');
        } else {
            input.classList.add('border-red-500', 'focus:border-red-500');
        }
    },

    /**
     * Validates a text field
     * @param {HTMLInputElement} field - The field to validate
     * @returns {boolean} - True if valid, false otherwise
     */
    validateField: function(field) {
        const value = field.value.trim();
        const isRequired = field.hasAttribute('required') || field.hasAttribute('data-conditionally-required');
        
        if (isRequired && !value) {
            this.setValidationState(field, false);
            return false;
        } else if (value) {
            this.setValidationState(field, true);
            return true;
        } else {
            field.classList.remove('border-green-500', 'border-red-500', 'focus:border-green-500', 'focus:border-red-500');
            return true;
        }
    }
};

// ===== TOAST NOTIFICATIONS =====
SharedForms.Toast = {
    /**
     * Shows a toast notification
     * @param {string} message - The message to display
     * @param {string} type - The type of toast (success, error, info, warning)
     * @param {number} duration - Duration in milliseconds (default: 5000)
     */
    show: function(message, type = 'success', duration = 5000) {
        const bgClass = {
            'success': 'bg-green-500',
            'error': 'bg-red-500',
            'info': 'bg-blue-500',
            'warning': 'bg-yellow-500'
        }[type] || 'bg-green-500';

        const icon = {
            'success': 'fas fa-check-circle',
            'error': 'fas fa-exclamation-triangle',
            'info': 'fas fa-info-circle',
            'warning': 'fas fa-exclamation-triangle'
        }[type] || 'fas fa-check-circle';

        const toast = $(`
            <div class="fixed top-5 right-5 z-50 flex items-center w-full max-w-xs p-4 text-white ${bgClass} rounded-lg shadow-lg" role="alert">
                <div class="flex items-center">
                    <i class="${icon} me-3"></i>
                    <div class="text-sm font-normal">${message}</div>
                </div>
                <button type="button" class="ms-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex items-center justify-center h-8 w-8" onclick="this.parentElement.remove()">
                    <span class="sr-only">Close</span>
                    <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
                    </svg>
                </button>
            </div>
        `);

        $('body').append(toast);

        // Auto-remove toast after duration
        setTimeout(() => {
            toast.fadeOut(300, function() {
                $(this).remove();
            });
        }, duration);
    }
};

// ===== ACCESSIBILITY HELPERS =====
SharedForms.Accessibility = {
    /**
     * Initializes accessibility features for forms
     */
    initialize: function() {
        // Add ARIA labels for screen readers
        $('.nationality-checkbox').attr('aria-describedby', 'eligibility-info');
        
        // Announce dynamic changes to screen readers
        $('.conditional-field').attr('aria-live', 'polite');
        
        // Keyboard navigation for file uploads
        $('.file-drop-zone').on('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                $(this).find('input[type="file"]').click();
            }
        }).attr('tabindex', '0').attr('role', 'button');

        // Timeline accessibility
        $('.timeline-item').attr('role', 'listitem');
        $('.timeline').attr('role', 'list').attr('aria-label', 'Application process timeline');
        
        // Download link keyboard navigation
        $('a[href*="DownloadFile"]').on('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                this.click();
            }
        });
    }
};

// ===== FORM UTILITIES =====
SharedForms.Utils = {
    /**
     * Scrolls to the first error on the page
     */
    scrollToFirstError: function() {
        const firstError = $('.border-red-500, [role="alert"]:not(.hidden)').first();
        if (firstError.length) {
            $('html, body').animate({
                scrollTop: firstError.offset().top - 100
            }, 500);
        }
    },

    /**
     * Smooth scrolling for anchor links
     */
    initializeSmoothScrolling: function() {
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            const target = $(this.getAttribute('href'));
            if (target.length) {
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 500);
            }
        });
    },

    /**
     * Initializes print functionality
     */
    initializePrintHandlers: function() {
        // Add print-friendly class when printing
        window.addEventListener('beforeprint', function() {
            document.body.classList.add('printing');
        });
        
        window.addEventListener('afterprint', function() {
            document.body.classList.remove('printing');
        });
    },

    /**
     * PDF download functionality
     * @param {string} referenceNumber - Reference number for the document
     */
    downloadPDF: function(referenceNumber) {
        // Simple PDF generation using browser's print to PDF
        const originalTitle = document.title;
        document.title = `Application Summary - ${referenceNumber}`;
        
        // Add PDF-specific styling
        document.body.classList.add('pdf-export');
        
        window.print();
        
        // Restore original state
        setTimeout(() => {
            document.title = originalTitle;
            document.body.classList.remove('pdf-export');
        }, 1000);
    }
};

// ===== CONDITIONAL FIELDS UTILITIES =====
SharedForms.ConditionalFields = {
    /**
     * Toggles visibility of conditional fields
     * @param {string} condition - The condition identifier
     * @param {boolean} show - Whether to show or hide the fields
     */
    toggle: function(condition, show) {
        const fields = $(`[data-condition="${condition}"]`);

        if (show) {
            fields.removeClass('hidden').hide().slideDown(300);
            // Mark file inputs as required if they should be visible
            fields.find('input[type="file"]').attr('data-conditionally-required', 'true');
        } else {
            fields.slideUp(300, function() {
                $(this).addClass('hidden');
            });
            // Remove required attribute and clear file inputs
            fields.find('input[type="file"]').removeAttr('data-conditionally-required').val('');
            // Clear any file previews
            fields.find('.file-preview').addClass('hidden');
            fields.find('.drop-zone-content').removeClass('hidden');
        }
    }
};

// ===== SIS INTEGRATION UTILITIES =====
// Phase 5: SIS Pre-filling and Read-only Field Management
SharedForms.SisIntegration = {
    /**
     * Initializes SIS integration features for forms
     * @param {Object} options - Configuration options
     */
    initialize: function(options = {}) {
        const defaults = {
            readOnlyFields: [],
            preFilledFields: [],
            dataFreshnessThreshold: 24, // hours
            enableTooltips: true
        };

        this.config = { ...defaults, ...options };
        this.setupReadOnlyFields();
        this.setupSisBadges();
        this.setupDataFreshnessIndicators();
        this.setupAccessibility();

        if (this.config.enableTooltips) {
            this.setupTooltips();
        }
    },

    /**
     * Sets up read-only styling and behavior for SIS pre-filled fields
     */
    setupReadOnlyFields: function() {
        this.config.readOnlyFields.forEach(fieldName => {
            const field = $(`[name="${fieldName}"], #${fieldName}`);
            if (field.length) {
                field.addClass('sis-readonly')
                     .prop('readonly', true)
                     .attr('tabindex', '-1')
                     .attr('aria-describedby', `${fieldName}-sis-info`);

                // Prevent editing attempts
                field.on('keydown paste cut', function(e) {
                    e.preventDefault();
                    SharedForms.Toast.show('This field is automatically filled from student records and cannot be edited.', 'info', 3000);
                });

                // Add visual feedback on focus
                field.on('focus', function() {
                    $(this).blur();
                    SharedForms.Toast.show('This field contains verified information from student records.', 'info', 2000);
                });
            }
        });
    },

    /**
     * Adds SIS badges to pre-filled fields
     */
    setupSisBadges: function() {
        this.config.preFilledFields.forEach(fieldName => {
            const field = $(`[name="${fieldName}"], #${fieldName}`);
            const fieldContainer = field.closest('.sis-field-container, div');

            if (field.length && !fieldContainer.find('.sis-badge').length) {
                const badge = $(`
                    <span class="sis-badge" id="${fieldName}-sis-badge" role="img" aria-label="Pre-filled from Student Information System">
                        <i class="fas fa-database" aria-hidden="true"></i>
                        From SIS
                    </span>
                `);

                // Position badge appropriately
                if (fieldContainer.hasClass('sis-field-container')) {
                    fieldContainer.append(badge);
                } else {
                    field.after(badge);
                }
            }
        });
    },

    /**
     * Sets up data freshness indicators
     * @param {Date} lastSyncDate - When the data was last synchronized
     */
    setupDataFreshnessIndicators: function(lastSyncDate = null) {
        if (!lastSyncDate) return;

        const now = new Date();
        const syncDate = new Date(lastSyncDate);
        const hoursDiff = (now - syncDate) / (1000 * 60 * 60);

        let freshnessClass = 'fresh';
        let freshnessText = 'Recently updated';
        let freshnessIcon = 'fas fa-check-circle';

        if (hoursDiff > this.config.dataFreshnessThreshold * 2) {
            freshnessClass = 'very-stale';
            freshnessText = 'Data may be outdated';
            freshnessIcon = 'fas fa-exclamation-triangle';
        } else if (hoursDiff > this.config.dataFreshnessThreshold) {
            freshnessClass = 'stale';
            freshnessText = 'Data is somewhat old';
            freshnessIcon = 'fas fa-clock';
        }

        const indicator = $(`
            <div class="data-freshness ${freshnessClass}" role="status" aria-live="polite">
                <i class="${freshnessIcon}" aria-hidden="true"></i>
                ${freshnessText} (${this.formatRelativeTime(syncDate)})
            </div>
        `);

        // Add to form header or first SIS field
        const targetContainer = $('.sis-prefill-alert, .sis-field-container').first();
        if (targetContainer.length) {
            targetContainer.append(indicator);
        }
    },

    /**
     * Sets up accessibility features for SIS fields
     */
    setupAccessibility: function() {
        // Add ARIA descriptions for SIS fields
        this.config.preFilledFields.forEach(fieldName => {
            const field = $(`[name="${fieldName}"], #${fieldName}`);
            if (field.length) {
                // Create hidden description for screen readers
                const description = $(`
                    <div id="${fieldName}-sis-info" class="sr-only">
                        This field has been automatically filled from the Student Information System and cannot be edited.
                    </div>
                `);
                field.after(description);

                // Update aria-describedby
                const existingDescribedBy = field.attr('aria-describedby') || '';
                const newDescribedBy = existingDescribedBy ?
                    `${existingDescribedBy} ${fieldName}-sis-info` :
                    `${fieldName}-sis-info`;
                field.attr('aria-describedby', newDescribedBy);
            }
        });
    },

    /**
     * Sets up tooltips for SIS elements
     */
    setupTooltips: function() {
        // Add tooltips to SIS badges
        $('.sis-badge').each(function() {
            $(this).attr('title', 'This information was automatically retrieved from the Student Information System')
                   .attr('data-tooltip', 'This information was automatically retrieved from the Student Information System');
        });

        // Add tooltips to read-only fields
        $('.sis-readonly').each(function() {
            $(this).attr('title', 'This field is read-only because it contains verified information from student records')
                   .attr('data-tooltip', 'This field is read-only because it contains verified information from student records');
        });
    },

    /**
     * Shows loading state for SIS operations
     * @param {string} fieldName - Name of the field being loaded
     */
    showLoadingState: function(fieldName) {
        const field = $(`[name="${fieldName}"], #${fieldName}`);
        if (field.length) {
            field.addClass('sis-loading').prop('disabled', true);
        }
    },

    /**
     * Hides loading state for SIS operations
     * @param {string} fieldName - Name of the field that finished loading
     */
    hideLoadingState: function(fieldName) {
        const field = $(`[name="${fieldName}"], #${fieldName}`);
        if (field.length) {
            field.removeClass('sis-loading').prop('disabled', false);
        }
    },

    /**
     * Formats a date as relative time (e.g., "2 hours ago")
     * @param {Date} date - The date to format
     * @returns {string} - Formatted relative time
     */
    formatRelativeTime: function(date) {
        const now = new Date();
        const diff = now - date;
        const minutes = Math.floor(diff / (1000 * 60));
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));

        if (days > 0) {
            return `${days} day${days > 1 ? 's' : ''} ago`;
        } else if (hours > 0) {
            return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        } else if (minutes > 0) {
            return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        } else {
            return 'Just now';
        }
    },

    /**
     * Validates that read-only fields haven't been tampered with
     * @returns {boolean} - True if validation passes
     */
    validateReadOnlyFields: function() {
        let isValid = true;

        this.config.readOnlyFields.forEach(fieldName => {
            const field = $(`[name="${fieldName}"], #${fieldName}`);
            if (field.length && !field.prop('readonly')) {
                console.warn(`SIS field ${fieldName} is not properly read-only`);
                isValid = false;
            }
        });

        return isValid;
    }
};

// ===== INITIALIZATION =====
$(document).ready(function() {
    // Initialize accessibility features
    SharedForms.Accessibility.initialize();

    // Initialize smooth scrolling
    SharedForms.Utils.initializeSmoothScrolling();

    // Initialize print handlers
    SharedForms.Utils.initializePrintHandlers();

    // Initialize Flowbite tooltips if available
    if (typeof Flowbite !== 'undefined') {
        // Flowbite tooltips are automatically initialized
    }
});
