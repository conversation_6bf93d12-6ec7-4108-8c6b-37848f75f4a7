namespace Forms.ktech.Shared.ViewModels
{
    /// <summary>
    /// Interface for ViewModels that contain file uploads
    /// Provides common functionality for handling file uploads across forms
    /// </summary>
    public interface IFileUploadViewModel : IFormViewModel
    {
        /// <summary>
        /// Gets all file upload properties from the ViewModel
        /// </summary>
        /// <returns>Dictionary of property names and their IFormFile values</returns>
        Dictionary<string, IFormFile?> GetFileUploads();

        /// <summary>
        /// Gets the file upload properties that are required based on current form state
        /// </summary>
        /// <returns>List of property names that should have files uploaded</returns>
        List<string> GetRequiredFileUploads();

        /// <summary>
        /// Validates that all required files are uploaded
        /// </summary>
        /// <returns>True if all required files are present, false otherwise</returns>
        bool HasAllRequiredFiles();

        /// <summary>
        /// Gets a list of missing required files
        /// </summary>
        /// <returns>List of property names for missing required files</returns>
        List<string> GetMissingRequiredFiles();
    }
}
