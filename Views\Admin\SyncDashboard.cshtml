@{
    ViewData["Title"] = "SIS Sync Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@section Breadcrumb {
    <li class="inline-flex items-center">
        <a asp-area="" asp-controller="Home" asp-action="Index" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
            <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
            </svg>
            Home
        </a>
    </li>
    <li>
        <div class="flex items-center">
            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
            </svg>
            <a asp-controller="Admin" asp-action="Dashboard" class="ms-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ms-2 dark:text-gray-400 dark:hover:text-white">Admin</a>
        </div>
    </li>
    <li aria-current="page">
        <div class="flex items-center">
            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
            </svg>
            <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400">SIS Sync Dashboard</span>
        </div>
    </li>
}

<!-- Header Section -->
<div class="bg-white rounded-lg shadow-lg p-6 mb-6 dark:bg-gray-800">
    <div class="flex items-center justify-between">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white">
                    <i class="fas fa-sync-alt text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">SIS Sync Dashboard</h1>
                <p class="text-gray-600 dark:text-gray-300">Monitor and manage student data synchronization</p>
            </div>
        </div>
        <div class="flex space-x-3">
            <button id="refreshBtn" class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600">
                <i class="fas fa-refresh me-2"></i>Refresh
            </button>
            <button id="triggerFullSyncBtn" class="bg-blue-700 hover:bg-blue-800 text-white font-medium py-2 px-4 rounded-lg focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-600">
                <i class="fas fa-download me-2"></i>Full Sync
            </button>
            <button id="triggerIncrementalSyncBtn" class="bg-green-700 hover:bg-green-800 text-white font-medium py-2 px-4 rounded-lg focus:ring-4 focus:ring-green-300 dark:focus:ring-green-600">
                <i class="fas fa-sync me-2"></i>Incremental Sync
            </button>
            <button id="cancelSyncBtn" class="bg-red-700 hover:bg-red-800 text-white font-medium py-2 px-4 rounded-lg focus:ring-4 focus:ring-red-300 dark:focus:ring-red-600 hidden">
                <i class="fas fa-stop me-2"></i>Cancel Sync
            </button>
        </div>
    </div>
</div>

<!-- Alert Messages -->
@if (TempData["SuccessMessage"] != null)
{
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6 dark:bg-green-900 dark:border-green-700 dark:text-green-300" role="alert">
        <div class="flex">
            <div class="py-1">
                <i class="fas fa-check-circle me-2"></i>
            </div>
            <div>
                <p class="font-bold">Success!</p>
                <p class="text-sm">@TempData["SuccessMessage"]</p>
            </div>
        </div>
    </div>
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6 dark:bg-red-900 dark:border-red-700 dark:text-red-300" role="alert">
        <div class="flex">
            <div class="py-1">
                <i class="fas fa-exclamation-circle me-2"></i>
            </div>
            <div>
                <p class="font-bold">Error!</p>
                <p class="text-sm">@TempData["ErrorMessage"]</p>
            </div>
        </div>
    </div>
}

<!-- Real-time Status Alert -->
<div id="realTimeAlert" class="hidden mb-6">
    <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded dark:bg-blue-900 dark:border-blue-700 dark:text-blue-300" role="alert">
        <div class="flex items-center">
            <div class="py-1">
                <i class="fas fa-info-circle me-2"></i>
            </div>
            <div class="flex-1">
                <p class="font-bold">Sync in Progress</p>
                <p class="text-sm" id="realTimeMessage">Synchronization is currently running...</p>
            </div>
            <div class="ml-4">
                <div class="w-32 bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                    <div id="realTimeProgress" class="bg-blue-600 h-2.5 rounded-full transition-all duration-300" style="width: 0%"></div>
                </div>
                <p class="text-xs mt-1 text-center" id="realTimePercentage">0%</p>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <!-- Total Students Card -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white">
                    <i class="fas fa-users"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Students</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white" id="totalStudentsCount">@ViewBag.TotalStudents</p>
            </div>
        </div>
    </div>

    <!-- Last Sync Card -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-12 w-12 rounded-md bg-green-500 text-white">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Last Sync</p>
                <p class="text-lg font-bold text-gray-900 dark:text-white" id="lastSyncTime">
                    @if (ViewBag.LastSyncDate != null)
                    {
                        @(((DateTime)ViewBag.LastSyncDate).ToString("MMM dd, HH:mm"))
                    }
                    else
                    {
                        <span class="text-gray-500">Never</span>
                    }
                </p>
            </div>
        </div>
    </div>

    <!-- Sync Status Card -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-12 w-12 rounded-md bg-yellow-500 text-white">
                    <i class="fas fa-info-circle"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Current Status</p>
                <p class="text-lg font-bold text-gray-900 dark:text-white" id="currentSyncStatus">@ViewBag.CurrentSyncStatus</p>
            </div>
        </div>
    </div>

    <!-- API Health Card -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-12 w-12 rounded-md bg-purple-500 text-white" id="apiHealthIcon">
                    <i class="fas fa-heartbeat"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">API Health</p>
                <p class="text-lg font-bold text-gray-900 dark:text-white" id="apiHealthStatus">
                    @if (ViewBag.ApiHealthStatus != null)
                    {
                        var health = ViewBag.ApiHealthStatus;
                        @health.Status
                    }
                    else
                    {
                        <span class="text-gray-500">Unknown</span>
                    }
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Recent Sync History -->
<div class="bg-white rounded-lg shadow-lg dark:bg-gray-800">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Sync Operations</h2>
            <a asp-controller="Admin" asp-action="SyncHistory" class="text-blue-600 hover:text-blue-800 text-sm font-medium dark:text-blue-400 dark:hover:text-blue-300">
                View all history
                <i class="fas fa-arrow-right ml-1"></i>
            </a>
        </div>
    </div>
    <div class="overflow-x-auto">
        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="px-6 py-3">Type</th>
                    <th scope="col" class="px-6 py-3">Status</th>
                    <th scope="col" class="px-6 py-3">Start Time</th>
                    <th scope="col" class="px-6 py-3">Duration</th>
                    <th scope="col" class="px-6 py-3">Records</th>
                    <th scope="col" class="px-6 py-3">Progress</th>
                </tr>
            </thead>
            <tbody id="recentSyncsTable">
                @if (ViewBag.RecentSyncs != null)
                {
                    foreach (var sync in (List<Forms.ktech.Data.SyncHistory>)ViewBag.RecentSyncs)
                    {
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                            <td class="px-6 py-4">
                                <span class="bg-@(sync.SyncType == Forms.ktech.Data.SyncType.Full ? "blue" : "green")-100 text-@(sync.SyncType == Forms.ktech.Data.SyncType.Full ? "blue" : "green")-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-@(sync.SyncType == Forms.ktech.Data.SyncType.Full ? "blue" : "green")-900 dark:text-@(sync.SyncType == Forms.ktech.Data.SyncType.Full ? "blue" : "green")-300">
                                    @sync.SyncType
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                @{
                                    var statusColor = sync.Status switch
                                    {
                                        Forms.ktech.Data.SyncStatus.Completed => "green",
                                        Forms.ktech.Data.SyncStatus.Failed => "red",
                                        Forms.ktech.Data.SyncStatus.InProgress => "yellow",
                                        Forms.ktech.Data.SyncStatus.Cancelled => "gray",
                                        _ => "gray"
                                    };
                                }
                                <span class="bg-@(statusColor)-100 text-@(statusColor)-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-@(statusColor)-900 dark:text-@(statusColor)-300">
                                    @sync.Status
                                </span>
                            </td>
                            <td class="px-6 py-4">@sync.StartTime.ToString("MMM dd, HH:mm")</td>
                            <td class="px-6 py-4">@sync.GetDurationString()</td>
                            <td class="px-6 py-4">@sync.RecordsProcessed</td>
                            <td class="px-6 py-4">
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: @(sync.ProgressPercentage)%"></div>
                                </div>
                                <span class="text-xs text-gray-500 dark:text-gray-400">@(sync.ProgressPercentage)%</span>
                            </td>
                        </tr>
                    }
                }
                else
                {
                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                            No sync operations found
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>

<!-- Sync Trigger Modal -->
<div id="syncModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden" style="z-index: 1000;">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 dark:bg-yellow-900">
                <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-300"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mt-4" id="modalTitle">Confirm Synchronization</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500 dark:text-gray-400" id="modalMessage">
                    Are you sure you want to start a synchronization? This will update student data from the SIS.
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <button id="confirmSyncBtn" class="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-300">
                    Confirm
                </button>
                <button id="cancelSyncModalBtn" class="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-24 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/lib/microsoft-signalr/signalr.min.js"></script>
    <script>
        // SignalR connection for real-time sync updates
        const connection = new signalR.HubConnectionBuilder()
            .withUrl("/hubs/sissync")
            .build();

        // Global variables
        let currentSyncType = '';
        let isPolling = false;
        let pollingInterval = null;

        // Start SignalR connection
        connection.start().then(function () {
            console.log("Connected to SIS Sync Hub");

            // Join the sync updates group
            connection.invoke("JoinSyncUpdatesGroup");

            // Start polling for status updates
            startStatusPolling();
        }).catch(function (err) {
            console.error("Failed to connect to SIS Sync Hub:", err);

            // Fallback to polling if SignalR fails
            startStatusPolling();
        });

        // SignalR event handlers
        connection.on("SyncStarted", function (data) {
            console.log("Sync started:", data);
            showRealTimeAlert(`${data.syncType} sync started by ${data.triggeredBy}`, 0);
            updateSyncButtons(true);
        });

        connection.on("SyncProgressUpdate", function (data) {
            console.log("Sync progress:", data);
            showRealTimeAlert(data.currentStep, data.progress);
            updateProgressDisplay(data.progress, data.currentStep, data.recordsProcessed);
        });

        connection.on("SyncCompleted", function (data) {
            console.log("Sync completed:", data);
            hideRealTimeAlert();
            updateSyncButtons(false);

            if (data.status === "Completed") {
                showSuccessMessage(`Sync completed successfully! Processed ${data.recordsProcessed} records (${data.recordsAdded} added, ${data.recordsUpdated} updated).`);
            } else {
                showErrorMessage(`Sync failed: ${data.errorMessage || 'Unknown error'}`);
            }

            // Refresh the dashboard data
            refreshDashboard();
        });

        connection.on("ApiHealthUpdate", function (data) {
            console.log("API health update:", data);
            updateApiHealthStatus(data.isHealthy, data.status, data.responseTimeMs);
        });

        // Status polling fallback
        function startStatusPolling() {
            if (isPolling) return;

            isPolling = true;
            pollingInterval = setInterval(async function() {
                try {
                    const response = await fetch('/Admin/SyncStatus');
                    const data = await response.json();

                    if (data.error) {
                        console.error("Error getting sync status:", data.error);
                        return;
                    }

                    updateDashboardFromStatus(data);
                } catch (error) {
                    console.error("Error polling sync status:", error);
                }
            }, 2000); // Poll every 2 seconds
        }

        function stopStatusPolling() {
            if (pollingInterval) {
                clearInterval(pollingInterval);
                pollingInterval = null;
                isPolling = false;
            }
        }

        // Update dashboard from status data
        function updateDashboardFromStatus(data) {
            if (data.lastSync) {
                const sync = data.lastSync;

                // Update current status
                document.getElementById('currentSyncStatus').textContent = sync.status;

                // Show real-time progress if sync is in progress
                if (sync.status === 'InProgress') {
                    showRealTimeAlert(sync.currentStep || 'Synchronization in progress...', sync.progress);
                    updateSyncButtons(true);
                } else {
                    hideRealTimeAlert();
                    updateSyncButtons(false);
                }
            }
        }

        // UI update functions
        function showRealTimeAlert(message, progress) {
            const alert = document.getElementById('realTimeAlert');
            const messageEl = document.getElementById('realTimeMessage');
            const progressBar = document.getElementById('realTimeProgress');
            const percentageEl = document.getElementById('realTimePercentage');

            messageEl.textContent = message;
            progressBar.style.width = progress + '%';
            percentageEl.textContent = progress + '%';
            alert.classList.remove('hidden');
        }

        function hideRealTimeAlert() {
            const alert = document.getElementById('realTimeAlert');
            alert.classList.add('hidden');
        }

        function updateProgressDisplay(progress, currentStep, recordsProcessed) {
            const progressBar = document.getElementById('realTimeProgress');
            const percentageEl = document.getElementById('realTimePercentage');
            const messageEl = document.getElementById('realTimeMessage');

            progressBar.style.width = progress + '%';
            percentageEl.textContent = progress + '%';
            messageEl.textContent = `${currentStep} (${recordsProcessed} records processed)`;
        }

        function updateSyncButtons(disabled) {
            document.getElementById('triggerFullSyncBtn').disabled = disabled;
            document.getElementById('triggerIncrementalSyncBtn').disabled = disabled;

            if (disabled) {
                document.getElementById('triggerFullSyncBtn').classList.add('opacity-50', 'cursor-not-allowed');
                document.getElementById('triggerIncrementalSyncBtn').classList.add('opacity-50', 'cursor-not-allowed');
                document.getElementById('cancelSyncBtn').classList.remove('hidden');
            } else {
                document.getElementById('triggerFullSyncBtn').classList.remove('opacity-50', 'cursor-not-allowed');
                document.getElementById('triggerIncrementalSyncBtn').classList.remove('opacity-50', 'cursor-not-allowed');
                document.getElementById('cancelSyncBtn').classList.add('hidden');
            }
        }

        function updateApiHealthStatus(isHealthy, status, responseTimeMs) {
            const statusEl = document.getElementById('apiHealthStatus');
            const iconEl = document.getElementById('apiHealthIcon');

            statusEl.textContent = status;

            if (isHealthy) {
                iconEl.className = 'flex items-center justify-center h-12 w-12 rounded-md bg-green-500 text-white';
            } else {
                iconEl.className = 'flex items-center justify-center h-12 w-12 rounded-md bg-red-500 text-white';
            }
        }

        // Helper function to safely set text content and prevent XSS
        function safeSetTextContent(element, text) {
            element.textContent = text || '';
        }

        // Helper function to create alert elements safely using DOM methods
        function createAlertElement(type, title, message) {
            // Main alert container
            const alertDiv = document.createElement('div');
            alertDiv.setAttribute('role', 'alert');
            alertDiv.className = 'px-4 py-3 rounded mb-6';
            
            // Set type-specific classes
            if (type === 'success') {
                alertDiv.className += ' bg-green-100 border border-green-400 text-green-700 dark:bg-green-900 dark:border-green-700 dark:text-green-300';
            } else if (type === 'error') {
                alertDiv.className += ' bg-red-100 border border-red-400 text-red-700 dark:bg-red-900 dark:border-red-700 dark:text-red-300';
            }

            // Flex container
            const flexDiv = document.createElement('div');
            flexDiv.className = 'flex';

            // Icon container
            const iconDiv = document.createElement('div');
            iconDiv.className = 'py-1';
            const icon = document.createElement('i');
            icon.className = type === 'success' ? 'fas fa-check-circle me-2' : 'fas fa-exclamation-circle me-2';
            iconDiv.appendChild(icon);

            // Content container
            const contentDiv = document.createElement('div');
            
            // Title paragraph
            const titleP = document.createElement('p');
            titleP.className = 'font-bold';
            safeSetTextContent(titleP, title);
            
            // Message paragraph
            const messageP = document.createElement('p');
            messageP.className = 'text-sm';
            safeSetTextContent(messageP, message);

            // Assemble the structure
            contentDiv.appendChild(titleP);
            contentDiv.appendChild(messageP);
            flexDiv.appendChild(iconDiv);
            flexDiv.appendChild(contentDiv);
            alertDiv.appendChild(flexDiv);

            return alertDiv;
        }

        function showSuccessMessage(message) {
            // Create alert element safely using DOM methods
            const alertElement = createAlertElement('success', 'Success!', message);

            // Insert after header section
            const header = document.querySelector('.bg-white.rounded-lg.shadow-lg.p-6.mb-6');
            if (header && header.nextElementSibling) {
                header.parentNode.insertBefore(alertElement, header.nextElementSibling);
            } else if (header) {
                header.insertAdjacentElement('afterend', alertElement);
            }

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (alertElement && alertElement.parentNode) {
                    alertElement.remove();
                }
            }, 5000);
        }

        function showErrorMessage(message) {
            // Create alert element safely using DOM methods
            const alertElement = createAlertElement('error', 'Error!', message);

            // Insert after header section
            const header = document.querySelector('.bg-white.rounded-lg.shadow-lg.p-6.mb-6');
            if (header && header.nextElementSibling) {
                header.parentNode.insertBefore(alertElement, header.nextElementSibling);
            } else if (header) {
                header.insertAdjacentElement('afterend', alertElement);
            }

            // Auto-remove after 10 seconds
            setTimeout(() => {
                if (alertElement && alertElement.parentNode) {
                    alertElement.remove();
                }
            }, 10000);
        }

        async function refreshDashboard() {
            try {
                // Reload the page to get fresh data
                window.location.reload();
            } catch (error) {
                console.error("Error refreshing dashboard:", error);
            }
        }

        // Event handlers
        document.getElementById('refreshBtn').addEventListener('click', function() {
            refreshDashboard();
        });

        document.getElementById('triggerFullSyncBtn').addEventListener('click', function() {
            currentSyncType = 'Full';
            document.getElementById('modalTitle').textContent = 'Confirm Full Synchronization';
            document.getElementById('modalMessage').textContent = 'Are you sure you want to start a full synchronization? This will process all student records from the SIS and may take several minutes.';
            document.getElementById('syncModal').classList.remove('hidden');
        });

        document.getElementById('triggerIncrementalSyncBtn').addEventListener('click', function() {
            currentSyncType = 'Incremental';
            document.getElementById('modalTitle').textContent = 'Confirm Incremental Synchronization';
            document.getElementById('modalMessage').textContent = 'Are you sure you want to start an incremental synchronization? This will process only changed records since the last sync.';
            document.getElementById('syncModal').classList.remove('hidden');
        });

        document.getElementById('confirmSyncBtn').addEventListener('click', async function() {
            document.getElementById('syncModal').classList.add('hidden');

            try {
                const formData = new FormData();
                formData.append('syncType', currentSyncType);
                formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

                const response = await fetch('/Admin/TriggerSync', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    showRealTimeAlert(`${currentSyncType} synchronization started...`, 0);
                    updateSyncButtons(true);
                } else {
                    showErrorMessage('Failed to start synchronization. Please try again.');
                }
            } catch (error) {
                console.error("Error triggering sync:", error);
                showErrorMessage('Failed to start synchronization. Please try again.');
            }
        });

        document.getElementById('cancelSyncBtn').addEventListener('click', async function() {
            if (confirm('Are you sure you want to cancel the running sync operation?')) {
                try {
                    const formData = new FormData();
                    formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

                    const response = await fetch('/Admin/CancelSync', {
                        method: 'POST',
                        body: formData
                    });

                    if (response.ok) {
                        const result = await response.json();
                        if (result.success) {
                            showRealTimeAlert('Sync operation cancelled successfully', 1);
                            updateSyncButtons(false);
                            // Refresh status after a short delay
                            setTimeout(() => {
                                refreshDashboard();
                            }, 1000);
                        } else {
                            showErrorMessage(result.message || 'Failed to cancel sync operation');
                        }
                    } else {
                        showErrorMessage('Failed to cancel sync operation. Please try again.');
                    }
                } catch (error) {
                    console.error("Error cancelling sync:", error);
                    showErrorMessage('Failed to cancel sync operation. Please try again.');
                }
            }
        });

        // Keep the existing modal cancel button handler
        document.getElementById('cancelSyncModalBtn').addEventListener('click', function() {
            document.getElementById('syncModal').classList.add('hidden');
        });

        // Close modal when clicking outside
        document.getElementById('syncModal').addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.add('hidden');
            }
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            stopStatusPolling();
            if (connection.state === signalR.HubConnectionState.Connected) {
                connection.stop();
            }
        });
    </script>
}

@Html.AntiForgeryToken()
