using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Forms.ktech.Data
{
    /// <summary>
    /// Entity class for storing synchronized student data from the SIS (Student Information System)
    /// This is a pure lookup table that mirrors the SIS API response structure
    /// Used as a reference table for form pre-filling and student data lookup
    /// </summary>
    public class SisStudent
    {
        #region Primary Key

        /// <summary>
        /// Primary key for the entity
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        #endregion

        #region SIS Student Data (Direct API Response Mirror)

        /// <summary>
        /// Student ID from SIS - Unique business identifier from SIS system
        /// Example: "140100002"
        /// </summary>
        [Required]
        [StringLength(50)]
        public string StudentID { get; set; } = string.Empty;

        /// <summary>
        /// Full name in Arabic from SIS
        /// Example: "BOTHAINAH S SAEID سعيد"
        /// </summary>
        [StringLength(200)]
        public string? FullNameAR { get; set; }

        /// <summary>
        /// Full name in English from SIS
        /// Example: "BOTHAINAH S J M SAEID"
        /// </summary>
        [Required]
        [StringLength(200)]
        public string FullNameEN { get; set; } = string.Empty;

        /// <summary>
        /// Student gender from SIS
        /// Example: "Female", "Male"
        /// </summary>
        [StringLength(20)]
        public string? Gender { get; set; }

        /// <summary>
        /// Student nationality from SIS
        /// Example: "KUWAIT", "EGYPT", "JORDAN"
        /// </summary>
        [StringLength(50)]
        public string? Nationality { get; set; }

        /// <summary>
        /// Kuwait National ID (Civil ID) from SIS
        /// Example: "296010100297"
        /// </summary>
        [StringLength(20)]
        public string? NationalID { get; set; }

        /// <summary>
        /// Student birth date from SIS
        /// Parsed from SIS API format: "1996-01-01T00:00:00"
        /// </summary>
        public DateTime BirthDate { get; set; }

        /// <summary>
        /// Student email address from SIS - Used for Azure AD matching
        /// Example: "<EMAIL>"
        /// This is the key field for matching Azure AD users to SIS records
        /// </summary>
        [Required]
        [StringLength(100)]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Student mobile phone number from SIS
        /// Example: "99399023"
        /// </summary>
        [StringLength(20)]
        public string? MobileNo { get; set; }

        #endregion

        #region Academic Information

        /// <summary>
        /// Current enrollment status from SIS
        /// Example: "Graduated", "Active", "Suspended", "Withdrawn"
        /// </summary>
        [StringLength(50)]
        public string? EnrollmentStatus { get; set; }

        /// <summary>
        /// Student's major/program from SIS
        /// Example: "Sales and Marketing", "Computer Science"
        /// </summary>
        [StringLength(100)]
        public string? Major { get; set; }

        /// <summary>
        /// Student's current academic level from SIS
        /// Example: "Second Year", "Third Year", "Graduate"
        /// </summary>
        [StringLength(50)]
        public string? Level { get; set; }

        #endregion

        #region Sync Tracking

        /// <summary>
        /// Timestamp when this record was last synchronized from SIS
        /// Used for data freshness tracking and incremental sync logic
        /// </summary>
        [Required]
        public DateTime LastSyncDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Hash of the student data for change detection during incremental syncs
        /// Used to determine if a record has been modified since last sync
        /// </summary>
        [StringLength(64)]
        public string? DataHash { get; set; }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Generates a hash of the student data for change detection during incremental syncs
        /// </summary>
        /// <returns>SHA256 hash of the student data</returns>
        public string GenerateDataHash()
        {
            var dataString = $"{StudentID}|{FullNameEN}|{FullNameAR}|{Gender}|{Nationality}|{NationalID}|{BirthDate:yyyy-MM-dd}|{Email}|{MobileNo}|{EnrollmentStatus}|{Major}|{Level}";

            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var hashBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(dataString));
            return Convert.ToHexString(hashBytes);
        }

        /// <summary>
        /// Updates the sync tracking information when data is synchronized
        /// </summary>
        /// <param name="syncDate">The date/time of the sync operation</param>
        public void UpdateSyncInfo(DateTime? syncDate = null)
        {
            LastSyncDate = syncDate ?? DateTime.UtcNow;
            DataHash = GenerateDataHash();
        }

        #endregion
    }
}
