# SIS API Endpoints

## Base URL

```
https://sisapi.ktech.edu.kw/api
```

<div id="bkmrk-">---

</div>## Endpoints Overview

### 1. **Students**

#### **GET /api/Students**

**Description:** Fetch all student data.

**Request:**

- **Method:** `GET`
- **Headers:** Requires `Authorization: Basic <base64-encoded-credentials>`

**Response:**

- **Status Code:** `200 OK` (on success)
- **Body:**

```
{
  "status": true,
  "message": "Students fetched successfully.",
  "data": [
    {
      "StudentID": "140100002",
      "FullNameAR": "BOTHAINAH S SAEID سعيد",
      "FullNameEN": "BOTHAINAH S J M SAEID",
      "Gender": "Female",
      "Nationality": "KUWAIT",
      "NationalID": "296010100297",
      "BirthDate": "1996-01-01T00:00:00",
      "Email": "<EMAIL>",
      "MobileNo": "99399023",
      "EnrollmentStatus": "Graduated",
      "Major": "Sales and Marketing",
      "Level": "Second Year"
    }
  ]
}
```

<div id="bkmrk--1">---

</div>#### **POST /api/Students/filter**

**Description:** Filter students based on dynamic criteria.

**Request:**

- **Method:** `POST`
- **Headers:** Requires `Authorization: Basic <base64-encoded-credentials>`
- **Body:**

```
{
  "Nationality": "KUWAIT",
  "Major": "Sales and Marketing"
}
```

**Response:**

- **Status Code:** `200 OK` (on success)
- **Body:**

```
{
  "status": true,
  "message": "Filtered students fetched successfully.",
  "data": [
    {
      "StudentID": "140100002",
      "FullNameAR": "BOTHAINAH S SAEID سعيد",
      "FullNameEN": "BOTHAINAH S J M SAEID",
      "Gender": "Female",
      "Nationality": "KUWAIT",
      "NationalID": "296010100297",
      "BirthDate": "1996-01-01T00:00:00",
      "Email": "<EMAIL>",
      "MobileNo": "99399023",
      "EnrollmentStatus": "Graduated",
      "Major": "Sales and Marketing",
      "Level": "Second Year"
    }
  ]
}
```

<div id="bkmrk--2">---

</div>### 2. **Enrolled Users**

#### **GET /api/EnrolledUsers**

**Description:** Fetch all enrolled users.

**Request:**

- **Method:** `GET`
- **Headers:** Requires `Authorization: Basic <base64-encoded-credentials>`

**Response:**

- **Status Code:** `200 OK` (on success)
- **Body:**

```
{
  "status": true,
  "message": "Enrolled users fetched successfully.",
  "data": [
    {
      "CourseID": 1,
      "CourseFullName": "ENGL101-01-FAL2024",
      "UserName": "<EMAIL>",
      "Role": "Student"
    }
  ]
}
```

<div id="bkmrk--3">---

</div>#### **POST /api/EnrolledUsers/filter**

**Description:** Filter enrolled users based on dynamic criteria.

**Request:**

- **Method:** `POST`
- **Headers:** Requires `Authorization: Basic <base64-encoded-credentials>`
- **Body:**

```
{
  "UserName": "<EMAIL>",
  "Role": "Student"
}
```

**Response:**

- **Status Code:** `200 OK` (on success)
- **Body:**

```
{
  "status": true,
  "message": "Filtered enrolled users fetched successfully.",
  "data": [
    {
      "CourseID": 1,
      "CourseFullName": "ENGL101-01-FAL2024",
      "UserName": "<EMAIL>",
      "Role": "Student"
    }
  ]
}
```

<div id="bkmrk--4">---

</div>### 3. **Courses**

#### **GET /api/Courses**

**Description:** Fetch all courses.

**Request:**

- **Method:** `GET`
- **Headers:** Requires `Authorization: Basic <base64-encoded-credentials>`

**Response:**

- **Status Code:** `200 OK` (on success)
- **Body:**

```
{
  "status": true,
  "message": "Courses fetched successfully.",
  "data": [
    {
      "CourseID": 1,
      "CourseFullName": "ENGL101-01-FAL2024",
      "Slot": "Su,T (18:30 - 19:45)",
      "Hall": "Building 1/C 373"
    }
  ]
}
```

<div id="bkmrk--5">---

</div>#### **POST /api/Courses/filter**

**Description:** Filter courses based on dynamic criteria.

**Request:**

- **Method:** `POST`
- **Headers:** Requires `Authorization: Basic <base64-encoded-credentials>`
- **Body:**

```
{
  "CourseFullName": "ENGL101",
  "Slot": "Su,T"
}
```

**Response:**

- **Status Code:** `200 OK` (on success)
- **Body:**

```
{
  "status": true,
  "message": "Filtered courses fetched successfully.",
  "data": [
    {
      "CourseID": 1,
      "CourseFullName": "ENGL101-01-FAL2024",
      "Slot": "Su,T (18:30 - 19:45)",
      "Hall": "Building 1/C 373"
    }
  ]
}
```

<div id="bkmrk--6">---

</div>## Error Responses

### **General Error Response**

- **Status Code:** `500 Internal Server Error`
- **Body:**

```
{
  "status": false,
  "message": "An error occurred while processing the request.",
  "details": "<Error details>"
}
```

<div id="bkmrk--7">---

</div>## Authorization

This API uses Basic Authentication. Include the `Authorization` header in every request with the following format:

```
Authorization: Basic <base64-encoded-credentials>
```

**Example:**

```
Authorization: Basic YWRtaW46YWRtaW5fcGFzc3dvcmQ=
```

<div id="bkmrk--8">---

</div>## Notes

- Ensure proper authorization headers are included in all requests.
- Use dynamic filtering to retrieve specific data subsets based on your requirements.
- Handle error responses gracefully in client applications.