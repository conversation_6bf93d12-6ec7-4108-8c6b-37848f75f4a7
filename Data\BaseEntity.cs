using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Forms.ktech.Data
{
    /// <summary>
    /// Base entity class that provides common properties for all form entities.
    /// Implements soft delete pattern and audit trail functionality.
    /// </summary>
    public abstract class BaseEntity
    {
        /// <summary>
        /// Primary key for the entity
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// UTC timestamp when the entity was created
        /// </summary>
        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// UTC timestamp when the entity was last updated
        /// </summary>
        public DateTime? UpdatedDate { get; set; }

        /// <summary>
        /// UTC timestamp when the entity was soft deleted
        /// </summary>
        public DateTime? DeletedDate { get; set; }

        /// <summary>
        /// Indicates if the entity is soft deleted
        /// </summary>
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// Unique identifier for the form submission
        /// Used for organizing uploaded files and tracking submissions
        /// </summary>
        public Guid SubmissionGuid { get; set; } = Guid.NewGuid();

        /// <summary>
        /// User identifier from Azure AD who created this submission
        /// </summary>
        [StringLength(450)] // Standard length for ASP.NET Identity user IDs
        public string? CreatedByUserId { get; set; }

        /// <summary>
        /// User identifier from Azure AD who last updated this submission
        /// </summary>
        [StringLength(450)]
        public string? UpdatedByUserId { get; set; }

        /// <summary>
        /// Marks the entity as updated and sets the timestamp
        /// </summary>
        /// <param name="userId">The user ID making the update</param>
        public virtual void MarkAsUpdated(string? userId = null)
        {
            UpdatedDate = DateTime.UtcNow;
            UpdatedByUserId = userId;
        }

        /// <summary>
        /// Marks the entity as soft deleted
        /// </summary>
        /// <param name="userId">The user ID performing the deletion</param>
        public virtual void MarkAsDeleted(string? userId = null)
        {
            IsDeleted = true;
            DeletedDate = DateTime.UtcNow;
            UpdatedByUserId = userId;
        }

        /// <summary>
        /// Restores a soft deleted entity
        /// </summary>
        /// <param name="userId">The user ID performing the restoration</param>
        public virtual void Restore(string? userId = null)
        {
            IsDeleted = false;
            DeletedDate = null;
            MarkAsUpdated(userId);
        }
    }
}
