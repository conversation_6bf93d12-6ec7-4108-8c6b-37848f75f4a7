@*
    Enhanced validation summary component with better styling and categorization
    Parameters:
    - title: Custom title for the validation summary (optional)
    - showPropertyErrors: Whether to show individual property errors (default: true)
    - showModelErrors: Whether to show model-level errors (default: true)
    - cssClass: Additional CSS classes to apply
*@

@{
    var title = ViewData["title"]?.ToString() ?? "Please correct the following errors:";
    var showPropertyErrors = (bool)(ViewData["showPropertyErrors"] ?? true);
    var showModelErrors = (bool)(ViewData["showModelErrors"] ?? true);
    var cssClass = ViewData["cssClass"]?.ToString() ?? "";

    var hasErrors = !ViewData.ModelState.IsValid;
    var modelErrors = ViewData.ModelState.Where(x => x.Key == string.Empty).SelectMany(x => x.Value.Errors);
    var propertyErrors = ViewData.ModelState.Where(x => x.Key != string.Empty).SelectMany(x => x.Value.Errors);
}

@if (hasErrors)
{
    <div class="flex items-center p-4 mb-4 text-sm text-red-800 border border-red-300 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400 dark:border-red-800 @cssClass" role="alert">
        <svg class="flex-shrink-0 inline w-4 h-4 me-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
        </svg>
        <span class="sr-only">Validation Error</span>
        <div class="flex-grow">
            <div class="font-medium">
                @title
            </div>

            @if (showModelErrors && modelErrors.Any())
            {
                <div class="mt-2">
                    <ul class="mt-1.5 list-disc list-inside">
                        @foreach (var error in modelErrors)
                        {
                            <li>@error.ErrorMessage</li>
                        }
                    </ul>
                </div>
            }

            @if (showPropertyErrors && propertyErrors.Any())
            {
                <div class="mt-2">
                    <ul class="mt-1.5 list-disc list-inside">
                        @foreach (var kvp in ViewData.ModelState.Where(x => x.Key != string.Empty && x.Value.Errors.Any()))
                        {
                            var fieldDisplayName = Html.DisplayNameFor(m => kvp.Key).ToString();
                            if (string.IsNullOrEmpty(fieldDisplayName))
                            {
                                fieldDisplayName = kvp.Key;
                            }

                            foreach (var error in kvp.Value.Errors)
                            {
                                <li>
                                    <span class="font-medium">@fieldDisplayName:</span> @error.ErrorMessage
                                </li>
                            }
                        }
                    </ul>
                </div>
            }

            <div class="mt-3 text-xs text-red-600 dark:text-red-400">
                <i class="fas fa-info-circle me-1"></i>
                Please review the form and correct the highlighted fields before submitting.
            </div>
        </div>

        <button type="button" class="ms-auto -mx-1.5 -my-1.5 bg-red-50 text-red-500 rounded-lg focus:ring-2 focus:ring-red-400 p-1.5 hover:bg-red-200 inline-flex items-center justify-center h-8 w-8 dark:bg-gray-800 dark:text-red-400 dark:hover:bg-gray-700" data-dismiss-target="#validation-alert" aria-label="Close">
            <span class="sr-only">Close</span>
            <svg class="w-3 h-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"/>
            </svg>
        </button>
    </div>

    <script>
        // Auto-scroll to validation summary
        document.addEventListener('DOMContentLoaded', function () {
            const validationSummary = document.querySelector('[role="alert"]');
            if (validationSummary) {
                validationSummary.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });

                // Add focus to first invalid field
                setTimeout(() => {
                    const firstInvalidField = document.querySelector('.text-red-900, .border-red-500');
                    if (firstInvalidField) {
                        const input = firstInvalidField.querySelector('input, select, textarea') || firstInvalidField;
                        if (input && input.focus) {
                            input.focus();
                        }
                    }
                }, 500);
            }
        });
    </script>
}
