using Forms.ktech.Data;
using Forms.ktech.Models;
using Forms.ktech.ViewModels.Admin;
using Microsoft.EntityFrameworkCore;
using System.Text;

namespace Forms.ktech.Services
{
    /// <summary>
    /// Service for email preview and batch notification operations
    /// </summary>
    public class EmailPreviewService : IEmailPreviewService
    {
        private readonly FormsKTechContext _context;
        private readonly IEmailNotificationService _emailService;
        private readonly IEmailTemplateService _templateService;
        private readonly ILogger<EmailPreviewService> _logger;

        public EmailPreviewService(
            FormsKTechContext context,
            IEmailNotificationService emailService,
            IEmailTemplateService templateService,
            ILogger<EmailPreviewService> logger)
        {
            _context = context;
            _emailService = emailService;
            _templateService = templateService;
            _logger = logger;
        }

        /// <summary>
        /// Gets all submissions with pending email notifications
        /// </summary>
        public async Task<List<EmailPreviewSubmission>> GetSubmissionsWithPendingEmailsAsync()
        {
            try
            {
                // UPDATED LOGIC: Show all submissions with disapproved documents, regardless of email status
                // This allows admins to re-send emails and see all submissions that need attention
                var submissions = await _context.StudentInfos
                    .Include(s => s.SisStudent)
                    .Where(s => _context.DocumentApprovals
                        .Any(da => da.SubmissionId == s.Id &&
                                  da.Status == DocumentApprovalStatus.Disapproved))
                    .Select(s => new EmailPreviewSubmission
                    {
                        SubmissionId = s.Id,
                        StudentName = s.GetStudentName(),
                        StudentEmail = s.GetStudentEmail(),
                        CivilId = s.GetStudentCivilId(),
                        DisapprovedDocumentCount = _context.DocumentApprovals
                            .Count(da => da.SubmissionId == s.Id && da.Status == DocumentApprovalStatus.Disapproved),
                        LastDisapprovalDate = _context.DocumentApprovals
                            .Where(da => da.SubmissionId == s.Id && da.Status == DocumentApprovalStatus.Disapproved)
                            .Max(da => (DateTime?)da.ApprovalDate) ?? DateTime.MinValue,
                        DisapprovedDocumentTypes = _context.DocumentApprovals
                            .Where(da => da.SubmissionId == s.Id && da.Status == DocumentApprovalStatus.Disapproved)
                            .Select(da => da.DocumentType)
                            .ToList(),
                        HasCustomContent = _context.EmailNotifications
                            .Any(en => en.SubmissionId == s.Id &&
                                     en.EmailType == "DocumentDisapprovalPreview" &&
                                     en.Status == EmailStatus.Draft)
                    })
                    .OrderByDescending(s => s.LastDisapprovalDate)
                    .ToListAsync();

                _logger.LogDebug("Found {SubmissionCount} submissions with pending email notifications", submissions.Count);

                return submissions;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting submissions with pending emails");
                throw;
            }
        }

        /// <summary>
        /// Generates email preview for a specific submission
        /// </summary>
        public async Task<EmailPreviewData> GenerateEmailPreviewAsync(int submissionId)
        {
            try
            {
                var submission = await _context.StudentInfos
                    .Include(s => s.SisStudent)
                    .FirstOrDefaultAsync(s => s.Id == submissionId);

                if (submission == null)
                {
                    throw new ArgumentException($"Submission {submissionId} not found");
                }

                var disapprovedDocs = await GetDisapprovedDocumentsAsync(submissionId);

                // DEBUG: Log what we found
                _logger.LogInformation("DEBUG EmailPreview: Found {Count} disapproved documents for submission {SubmissionId}",
                    disapprovedDocs.Count, submissionId);

                foreach (var doc in disapprovedDocs)
                {
                    _logger.LogInformation("DEBUG EmailPreview: Document {DocType}, EmailSent={EmailSent}",
                        doc.DocumentType, doc.EmailSent);
                }

                // UPDATED LOGIC: Always show email preview if there are disapproved documents
                // This allows admins to re-send emails even if they were sent before
                var hasPendingEmails = disapprovedDocs.Any(); // Any disapproved documents = show email preview
                _logger.LogInformation("DEBUG EmailPreview: HasPendingEmails calculated as {HasPendingEmails} (any disapproved docs: {HasDisapprovedDocs})",
                    hasPendingEmails, disapprovedDocs.Any());

                // Check for existing custom content
                var existingPreview = await _context.EmailNotifications
                    .FirstOrDefaultAsync(en => en.SubmissionId == submissionId &&
                                             en.EmailType == "DocumentDisapprovalPreview" &&
                                             en.Status == EmailStatus.Draft);

                var defaultSubject = GenerateDefaultSubject(submissionId);
                var defaultBody = await GenerateDefaultBodyAsync(submission, disapprovedDocs);

                return new EmailPreviewData
                {
                    SubmissionId = submissionId,
                    StudentName = submission.GetStudentName(),
                    StudentEmail = submission.GetStudentEmail(),
                    CivilId = submission.GetStudentCivilId(),
                    DefaultSubject = defaultSubject,
                    DefaultBody = defaultBody,
                    CustomSubject = existingPreview?.Subject,
                    CustomBody = existingPreview?.Body,
                    DisapprovedDocuments = disapprovedDocs,
                    CreatedDate = DateTime.UtcNow,
                    HasPendingEmails = hasPendingEmails
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating email preview for submission {SubmissionId}", submissionId);
                throw;
            }
        }

        /// <summary>
        /// Updates email content for a submission
        /// </summary>
        public async Task<EmailPreviewData> UpdateEmailContentAsync(int submissionId, string customSubject, string customBody)
        {
            try
            {
                var submission = await _context.StudentInfos
                    .FirstOrDefaultAsync(s => s.Id == submissionId);

                if (submission == null)
                {
                    throw new ArgumentException($"Submission {submissionId} not found");
                }

                // Find or create preview email record
                var previewEmail = await _context.EmailNotifications
                    .FirstOrDefaultAsync(en => en.SubmissionId == submissionId && 
                                             en.EmailType == "DocumentDisapprovalPreview" && 
                                             en.Status == EmailStatus.Draft);

                if (previewEmail == null)
                {
                    previewEmail = new EmailNotification
                    {
                        SubmissionId = submissionId,
                        EmailType = "DocumentDisapprovalPreview",
                        RecipientEmail = submission.GetStudentEmail(),
                        Status = EmailStatus.Draft,
                        CreatedDate = DateTime.UtcNow
                    };
                    _context.EmailNotifications.Add(previewEmail);
                }

                previewEmail.Subject = customSubject;
                previewEmail.Body = customBody;
                previewEmail.UpdatedDate = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return await GenerateEmailPreviewAsync(submissionId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating email content for submission {SubmissionId}", submissionId);
                throw;
            }
        }

        /// <summary>
        /// Gets disapproved documents for a submission
        /// </summary>
        public async Task<List<DisapprovedDocumentInfo>> GetDisapprovedDocumentsAsync(int submissionId)
        {
            try
            {
                _logger.LogInformation("DEBUG GetDisapprovedDocs: Starting for submission {SubmissionId}", submissionId);

                // DEBUG: Check what's actually in the database
                var allApprovalsForSubmission = await _context.DocumentApprovals
                    .Where(da => da.SubmissionId == submissionId)
                    .ToListAsync();

                _logger.LogInformation("DEBUG: Found {Count} total DocumentApproval records for submission {SubmissionId}",
                    allApprovalsForSubmission.Count, submissionId);

                foreach (var approval in allApprovalsForSubmission)
                {
                    _logger.LogInformation("DEBUG: DocumentApproval - Id={Id}, DocumentType={DocType}, Status={Status} ({StatusInt}), Reason={Reason}",
                        approval.Id, approval.DocumentType, approval.Status, (int)approval.Status, approval.DisapprovalReason);
                }

                // First, check if ANY email has been sent for this submission (single email per submission logic)
                var hasEmailBeenSent = await _context.EmailNotifications
                    .AnyAsync(en => en.SubmissionId == submissionId &&
                                  en.EmailType == "DocumentDisapproval" &&
                                  en.Status == EmailStatus.Sent);

                _logger.LogInformation("DEBUG GetDisapprovedDocs: hasEmailBeenSent = {HasEmailBeenSent}", hasEmailBeenSent);

                // DEBUG: Check what EmailNotification records exist
                var allEmailNotifications = await _context.EmailNotifications
                    .Where(en => en.SubmissionId == submissionId)
                    .ToListAsync();

                _logger.LogInformation("DEBUG: Found {Count} EmailNotification records for submission {SubmissionId}",
                    allEmailNotifications.Count, submissionId);

                foreach (var email in allEmailNotifications)
                {
                    _logger.LogInformation("DEBUG: EmailNotification - Id={Id}, Type={Type}, Status={Status} ({StatusInt}), SentDate={SentDate}",
                        email.Id, email.EmailType, email.Status, (int)email.Status, email.SentDate);
                }

                // Check how many DocumentApproval records exist
                var totalApprovals = await _context.DocumentApprovals
                    .CountAsync(da => da.SubmissionId == submissionId);

                var disapprovedCount = await _context.DocumentApprovals
                    .CountAsync(da => da.SubmissionId == submissionId && da.Status == DocumentApprovalStatus.Disapproved);

                _logger.LogInformation("DEBUG GetDisapprovedDocs: Total DocumentApprovals={Total}, Disapproved={Disapproved}",
                    totalApprovals, disapprovedCount);

                var documents = await _context.DocumentApprovals
                    .Where(da => da.SubmissionId == submissionId && da.Status == DocumentApprovalStatus.Disapproved)
                    .Select(da => new DisapprovedDocumentInfo
                    {
                        DocumentType = da.DocumentType,
                        DocumentName = da.GetDocumentDisplayName(),
                        Reason = da.DisapprovalReason ?? "No reason provided",
                        Comments = da.Comments,
                        DisapprovalDate = da.ApprovalDate ?? DateTime.MinValue,
                        DisapprovedBy = ExtractDisapprovedBy(da.Comments),
                        // FIXED: Single email per submission logic - if ANY email was sent, ALL documents are considered notified
                        EmailSent = hasEmailBeenSent
                    })
                    .OrderByDescending(d => d.DisapprovalDate)
                    .ToListAsync();

                _logger.LogInformation("DEBUG GetDisapprovedDocs: Returning {Count} documents, all with EmailSent={EmailSent}",
                    documents.Count, hasEmailBeenSent);

                return documents;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting disapproved documents for submission {SubmissionId}", submissionId);
                throw;
            }
        }

        /// <summary>
        /// Sends batch email notification for a submission (single email per submission logic)
        /// </summary>
        public async Task<EmailNotification> SendBatchEmailNotificationAsync(int submissionId, string? customSubject = null, string? customBody = null)
        {
            try
            {
                // CRITICAL: Check if email already sent for this submission to prevent duplicates
                var existingEmail = await _context.EmailNotifications
                    .FirstOrDefaultAsync(en => en.SubmissionId == submissionId &&
                                             en.EmailType == "DocumentDisapproval" &&
                                             en.Status == EmailStatus.Sent);

                if (existingEmail != null)
                {
                    _logger.LogWarning("Email already sent for submission {SubmissionId} on {SentDate}. Skipping duplicate.",
                        submissionId, existingEmail.SentDate);
                    return existingEmail;
                }

                // Get all disapproved documents for this submission
                var disapprovedDocs = await GetDisapprovedDocumentsAsync(submissionId);

                if (!disapprovedDocs.Any())
                {
                    throw new InvalidOperationException($"No disapproved documents found for submission {submissionId}");
                }

                // Get submission details
                var submission = await _context.StudentInfos
                    .Include(s => s.SisStudent)
                    .FirstOrDefaultAsync(s => s.Id == submissionId);

                if (submission == null)
                {
                    throw new ArgumentException($"Submission {submissionId} not found");
                }

                // Determine email content
                string subject, body;
                if (!string.IsNullOrEmpty(customSubject) && !string.IsNullOrEmpty(customBody))
                {
                    // Use custom content - generate HTML from plain text
                    subject = customSubject;
                    body = await _templateService.GenerateCustomEmailContentAsync(submissionId, customSubject, customBody);
                }
                else
                {
                    // Check for saved custom content
                    var previewEmail = await _context.EmailNotifications
                        .FirstOrDefaultAsync(en => en.SubmissionId == submissionId &&
                                                 en.EmailType == "DocumentDisapprovalPreview" &&
                                                 en.Status == EmailStatus.Draft);

                    if (previewEmail != null && !string.IsNullOrEmpty(previewEmail.Body))
                    {
                        // Use saved custom content - generate HTML from plain text
                        subject = previewEmail.Subject;
                        body = await _templateService.GenerateCustomEmailContentAsync(submissionId, previewEmail.Subject, previewEmail.Body);
                    }
                    else
                    {
                        // Generate default content using template service
                        subject = GenerateDefaultSubject(submissionId);
                        body = await GenerateDefaultBodyAsync(submission, disapprovedDocs);
                    }
                }

                // Get student email - must be available for email sending
                var studentEmail = submission.GetStudentEmail();
                if (string.IsNullOrEmpty(studentEmail))
                {
                    throw new InvalidOperationException($"No student email found for submission {submissionId}. Cannot send email notification.");
                }

                _logger.LogInformation("Sending email to student: {StudentEmail} for submission {SubmissionId}", studentEmail, submissionId);

                // Create consolidated email notification record
                var emailNotification = new EmailNotification
                {
                    SubmissionId = submissionId,
                    EmailType = "DocumentDisapproval",
                    RecipientEmail = studentEmail,
                    Subject = subject,
                    Body = body,
                    Status = EmailStatus.Pending,
                    CreatedDate = DateTime.UtcNow
                };

                _context.EmailNotifications.Add(emailNotification);
                await _context.SaveChangesAsync();

                // Send the email
                var success = await _emailService.SendEmailAsync(emailNotification);

                if (!success)
                {
                    throw new InvalidOperationException($"Failed to send email for submission {submissionId}");
                }

                // Clean up preview email
                var previewToDelete = await _context.EmailNotifications
                    .FirstOrDefaultAsync(en => en.SubmissionId == submissionId &&
                                             en.EmailType == "DocumentDisapprovalPreview" &&
                                             en.Status == EmailStatus.Draft);
                if (previewToDelete != null)
                {
                    _context.EmailNotifications.Remove(previewToDelete);
                    await _context.SaveChangesAsync();
                }

                _logger.LogInformation("Consolidated email notification sent for submission {SubmissionId} with {DocumentCount} disapproved documents",
                    submissionId, disapprovedDocs.Count);

                return emailNotification;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending batch email notification for submission {SubmissionId}", submissionId);
                throw;
            }
        }

        /// <summary>
        /// Sends bulk email notifications for multiple submissions
        /// </summary>
        public async Task<List<EmailNotification>> SendBulkEmailNotificationsAsync(int[] submissionIds)
        {
            var results = new List<EmailNotification>();

            foreach (var submissionId in submissionIds)
            {
                try
                {
                    var emailNotification = await SendBatchEmailNotificationAsync(submissionId);
                    results.Add(emailNotification);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error sending bulk email for submission {SubmissionId}", submissionId);
                    // Continue with other submissions
                }
            }

            return results;
        }

        /// <summary>
        /// Marks documents as email notification sent
        /// </summary>
        public async Task<bool> MarkDocumentsAsEmailSentAsync(int submissionId, string[] documentTypes)
        {
            try
            {
                // This is handled by the email notification record creation
                // No additional marking needed as we check EmailNotifications table
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking documents as email sent for submission {SubmissionId}", submissionId);
                return false;
            }
        }

        #region Private Helper Methods

        private string GenerateDefaultSubject(int submissionId)
        {
            return $"Document Review Required - Submission #{submissionId} / مراجعة المستندات مطلوبة - طلب رقم #{submissionId}";
        }

        private async Task<string> GenerateDefaultBodyAsync(Features.KuwaitiStudentInfo.Models.StudentInfo submission, List<DisapprovedDocumentInfo> disapprovedDocs)
        {
            try
            {
                // Use the new template service to generate email content with the existing KTECH template
                return await _templateService.GenerateEmailContentAsync(submission.Id, disapprovedDocs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating email content using template service for submission {SubmissionId}", submission.Id);

                // Fallback to basic content if template service fails
                var studentName = submission.GetStudentName();
                var submissionId = submission.Id;

                return $@"
                <div style=""font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;"">
                    <h2>Document Review Required / مراجعة المستندات مطلوبة</h2>
                    <p>Dear {studentName} / عزيزي {studentName},</p>
                    <p>Your submission #{submissionId} requires document updates.</p>
                    <p style=""direction: rtl;"">طلبك رقم #{submissionId} يحتاج إلى تحديث المستندات.</p>
                    <p><NAME_EMAIL> for assistance.</p>
                </div>";
            }
        }

        private static string ExtractDisapprovedBy(string? comments)
        {
            if (string.IsNullOrEmpty(comments))
                return "Admin";

            var index = comments.IndexOf("Disapproved by:");
            if (index >= 0)
            {
                var start = index + "Disapproved by:".Length;
                var end = comments.IndexOf(" - ", start);
                if (end > start)
                {
                    return comments.Substring(start, end - start).Trim();
                }
                return comments.Substring(start).Trim();
            }

            return "Admin";
        }

        #endregion
    }
}
