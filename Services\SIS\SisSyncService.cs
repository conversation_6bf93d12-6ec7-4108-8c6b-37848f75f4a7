using Forms.ktech.Configuration;
using Forms.ktech.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Globalization;
using Microsoft.AspNetCore.SignalR;
using Forms.ktech.Hubs;

namespace Forms.ktech.Services.SIS
{
    /// <summary>
    /// Implementation of SIS data synchronization service
    /// Handles synchronization of student data from SIS API to local database
    /// </summary>
    public class SisSyncService : ISisSyncService
    {
        private readonly ISisApiClient _sisApiClient;
        private readonly FormsKTechContext _context;
        private readonly SisApiOptions _options;
        private readonly ILogger<SisSyncService> _logger;
        private readonly IHubContext<SisSyncHub> _hubContext;
        private readonly SemaphoreSlim _syncSemaphore;
        private readonly ConcurrentDictionary<Guid, CancellationTokenSource> _activeSyncs;

        // Events for progress tracking
        public event EventHandler<SyncProgressEventArgs>? SyncProgressUpdated;
        public event EventHandler<SyncCompletedEventArgs>? SyncCompleted;

        public SisSyncService(
            ISisApiClient sisApiClient,
            FormsKTechContext context,
            IOptions<SisApiOptions> options,
            ILogger<SisSyncService> logger,
            IHubContext<SisSyncHub> hubContext)
        {
            _sisApiClient = sisApiClient ?? throw new ArgumentNullException(nameof(sisApiClient));
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _options = options?.Value ?? throw new ArgumentNullException(nameof(options));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _hubContext = hubContext ?? throw new ArgumentNullException(nameof(hubContext));

            _syncSemaphore = new SemaphoreSlim(1, 1); // Only allow one sync at a time
            _activeSyncs = new ConcurrentDictionary<Guid, CancellationTokenSource>();
        }

        #region Sync Operations

        /// <summary>
        /// Starts a full synchronization of all student data from SIS
        /// </summary>
        public async Task<SyncResult> StartFullSyncAsync(string? triggeredByUserId = null, CancellationToken cancellationToken = default)
        {
            if (!await _syncSemaphore.WaitAsync(100, cancellationToken))
            {
                throw new InvalidOperationException("Another sync operation is already in progress");
            }

            try
            {
                _logger.LogInformation("Starting full SIS synchronization. Triggered by: {UserId}", triggeredByUserId ?? "System");

                var syncHistory = await CreateSyncHistoryAsync(SyncType.Full, triggeredByUserId);
                var syncCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                _activeSyncs[syncHistory.SyncId] = syncCts;

                // Send SignalR notification that sync started
                await _hubContext.SendSyncStarted(syncHistory.SyncId, "Full", triggeredByUserId ?? "System");

                try
                {
                    var result = await PerformFullSyncAsync(syncHistory, syncCts.Token);
                    await CompleteSyncHistoryAsync(syncHistory, result);

                    _logger.LogInformation("Full SIS synchronization completed. SyncId: {SyncId}, Status: {Status}, Records: {Records}",
                        syncHistory.SyncId, result.Status, result.RecordsProcessed);

                    // Send SignalR notification that sync completed
                    await _hubContext.SendSyncCompleted(syncHistory.SyncId, result.Status.ToString(),
                        result.RecordsProcessed, result.RecordsAdded, result.RecordsUpdated, result.ErrorMessage);

                    SyncCompleted?.Invoke(this, new SyncCompletedEventArgs { Result = result });
                    return result;
                }
                catch (OperationCanceledException)
                {
                    _logger.LogWarning("Full SIS synchronization was cancelled. SyncId: {SyncId}", syncHistory.SyncId);
                    syncHistory.MarkAsCancelled("Operation was cancelled by user or system");
                    await _context.SaveChangesAsync(CancellationToken.None);
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Full SIS synchronization failed. SyncId: {SyncId}", syncHistory.SyncId);
                    syncHistory.MarkAsFailed(ex.Message, ex.ToString());
                    await _context.SaveChangesAsync(CancellationToken.None);
                    throw;
                }
                finally
                {
                    _activeSyncs.TryRemove(syncHistory.SyncId, out _);
                }
            }
            finally
            {
                _syncSemaphore.Release();
            }
        }

        /// <summary>
        /// Starts an incremental synchronization of changed student data from SIS
        /// </summary>
        public async Task<SyncResult> StartIncrementalSyncAsync(string? triggeredByUserId = null, CancellationToken cancellationToken = default)
        {
            if (!_options.EnableIncrementalSync)
            {
                _logger.LogWarning("Incremental sync is disabled in configuration, falling back to full sync");
                return await StartFullSyncAsync(triggeredByUserId, cancellationToken);
            }

            if (!await _syncSemaphore.WaitAsync(100, cancellationToken))
            {
                throw new InvalidOperationException("Another sync operation is already in progress");
            }

            try
            {
                _logger.LogInformation("Starting incremental SIS synchronization. Triggered by: {UserId}", triggeredByUserId ?? "System");

                var syncHistory = await CreateSyncHistoryAsync(SyncType.Incremental, triggeredByUserId);
                var syncCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
                _activeSyncs[syncHistory.SyncId] = syncCts;

                // Send SignalR notification that sync started
                await _hubContext.SendSyncStarted(syncHistory.SyncId, "Incremental", triggeredByUserId ?? "System");

                try
                {
                    var result = await PerformIncrementalSyncAsync(syncHistory, syncCts.Token);
                    await CompleteSyncHistoryAsync(syncHistory, result);

                    _logger.LogInformation("Incremental SIS synchronization completed. SyncId: {SyncId}, Status: {Status}, Records: {Records}",
                        syncHistory.SyncId, result.Status, result.RecordsProcessed);

                    // Send SignalR notification that sync completed
                    await _hubContext.SendSyncCompleted(syncHistory.SyncId, result.Status.ToString(),
                        result.RecordsProcessed, result.RecordsAdded, result.RecordsUpdated, result.ErrorMessage);

                    SyncCompleted?.Invoke(this, new SyncCompletedEventArgs { Result = result });
                    return result;
                }
                catch (OperationCanceledException)
                {
                    _logger.LogWarning("Incremental SIS synchronization was cancelled. SyncId: {SyncId}", syncHistory.SyncId);
                    syncHistory.MarkAsCancelled("Operation was cancelled by user or system");
                    await _context.SaveChangesAsync(CancellationToken.None);
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Incremental SIS synchronization failed. SyncId: {SyncId}", syncHistory.SyncId);
                    syncHistory.MarkAsFailed(ex.Message, ex.ToString());
                    await _context.SaveChangesAsync(CancellationToken.None);
                    throw;
                }
                finally
                {
                    _activeSyncs.TryRemove(syncHistory.SyncId, out _);
                }
            }
            finally
            {
                _syncSemaphore.Release();
            }
        }

        /// <summary>
        /// Gets the current status of any running sync operation
        /// </summary>
        public async Task<SyncStatus?> GetCurrentSyncStatusAsync()
        {
            var runningSyncs = await _context.SyncHistories
                .Where(sh => sh.Status == Data.SyncStatus.InProgress)
                .OrderByDescending(sh => sh.StartTime)
                .FirstOrDefaultAsync();

            return runningSyncs?.Status;
        }

        /// <summary>
        /// Cancels a running sync operation
        /// </summary>
        public async Task<bool> CancelSyncAsync(Guid syncId)
        {
            if (_activeSyncs.TryGetValue(syncId, out var cancellationTokenSource))
            {
                _logger.LogInformation("Cancelling sync operation: {SyncId}", syncId);
                cancellationTokenSource.Cancel();

                // Update the sync history
                var syncHistory = await _context.SyncHistories
                    .FirstOrDefaultAsync(sh => sh.SyncId == syncId);

                if (syncHistory != null)
                {
                    syncHistory.MarkAsCancelled("Cancelled by user request");
                    await _context.SaveChangesAsync();
                }

                return true;
            }

            _logger.LogWarning("Attempted to cancel sync operation that is not active: {SyncId}", syncId);

            // Try to force-cancel stuck sync from database
            return await ForceCancelStuckSyncAsync(syncId);
        }

        /// <summary>
        /// Force-cancels a stuck sync operation by updating the database directly
        /// This is used when the sync is stuck and not responding to normal cancellation
        /// </summary>
        public async Task<bool> ForceCancelStuckSyncAsync(Guid syncId)
        {
            try
            {
                var syncHistory = await _context.SyncHistories
                    .FirstOrDefaultAsync(sh => sh.SyncId == syncId && sh.Status == Data.SyncStatus.InProgress);

                if (syncHistory != null)
                {
                    _logger.LogWarning("Force-cancelling stuck sync operation: {SyncId}", syncId);
                    syncHistory.MarkAsCancelled("Force-cancelled due to stuck operation");
                    await _context.SaveChangesAsync();

                    // Remove from active syncs if it exists
                    _activeSyncs.TryRemove(syncId, out _);

                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error force-cancelling stuck sync {SyncId}", syncId);
                return false;
            }
        }

        /// <summary>
        /// Cancels all stuck sync operations that have been running for too long
        /// </summary>
        public async Task<int> CancelStuckSyncsAsync(TimeSpan maxRunTime)
        {
            try
            {
                var cutoffTime = DateTime.UtcNow - maxRunTime;
                var stuckSyncs = await _context.SyncHistories
                    .Where(sh => sh.Status == Data.SyncStatus.InProgress && sh.StartTime < cutoffTime)
                    .ToListAsync();

                var cancelledCount = 0;
                foreach (var stuckSync in stuckSyncs)
                {
                    _logger.LogWarning("Found stuck sync operation: {SyncId}, started at {StartTime}",
                        stuckSync.SyncId, stuckSync.StartTime);

                    if (await ForceCancelStuckSyncAsync(stuckSync.SyncId))
                    {
                        cancelledCount++;
                    }
                }

                if (cancelledCount > 0)
                {
                    _logger.LogInformation("Force-cancelled {Count} stuck sync operations", cancelledCount);
                }

                return cancelledCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling stuck sync operations");
                return 0;
            }
        }

        #endregion

        #region Sync History and Monitoring

        /// <summary>
        /// Gets the history of sync operations
        /// </summary>
        public async Task<IEnumerable<SyncHistory>> GetSyncHistoryAsync(int pageSize = 50, int pageNumber = 1)
        {
            return await _context.SyncHistories
                .OrderByDescending(sh => sh.StartTime)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        /// <summary>
        /// Gets detailed information about a specific sync operation
        /// </summary>
        public async Task<SyncHistory?> GetSyncDetailsAsync(Guid syncId)
        {
            return await _context.SyncHistories
                .FirstOrDefaultAsync(sh => sh.SyncId == syncId);
        }

        /// <summary>
        /// Gets the last successful sync date
        /// </summary>
        public async Task<DateTime?> GetLastSuccessfulSyncDateAsync()
        {
            var lastSuccessfulSync = await _context.SyncHistories
                .Where(sh => sh.Status == Data.SyncStatus.Completed)
                .OrderByDescending(sh => sh.EndTime)
                .FirstOrDefaultAsync();

            return lastSuccessfulSync?.EndTime;
        }

        /// <summary>
        /// Gets statistics about the current state of synchronized data
        /// </summary>
        public async Task<SyncStatistics> GetSyncStatisticsAsync()
        {
            var totalStudents = await _context.SisStudents.CountAsync();
            var lastSyncDate = await GetLastSuccessfulSyncDateAsync();
            var dataAge = lastSyncDate.HasValue ? DateTime.UtcNow - lastSyncDate.Value : (TimeSpan?)null;
            var isDataFresh = dataAge.HasValue && dataAge.Value <= _options.DataFreshnessThreshold;

            var studentsByMajor = await _context.SisStudents
                .Where(s => !string.IsNullOrEmpty(s.Major))
                .GroupBy(s => s.Major)
                .Select(g => new { Major = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Major!, x => x.Count);

            var studentsByLevel = await _context.SisStudents
                .Where(s => !string.IsNullOrEmpty(s.Level))
                .GroupBy(s => s.Level)
                .Select(g => new { Level = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Level!, x => x.Count);

            var studentsByStatus = await _context.SisStudents
                .Where(s => !string.IsNullOrEmpty(s.EnrollmentStatus))
                .GroupBy(s => s.EnrollmentStatus)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToDictionaryAsync(x => x.Status!, x => x.Count);

            return new SyncStatistics
            {
                TotalStudents = totalStudents,
                ActiveStudents = studentsByStatus.GetValueOrDefault("Active", 0),
                GraduatedStudents = studentsByStatus.GetValueOrDefault("Graduated", 0),
                LastSyncDate = lastSyncDate,
                IsDataFresh = isDataFresh,
                DataAge = dataAge,
                StudentsByMajor = studentsByMajor,
                StudentsByLevel = studentsByLevel,
                StudentsByEnrollmentStatus = studentsByStatus
            };
        }

        #endregion

        #region Data Validation and Health

        /// <summary>
        /// Validates the connection to the SIS API
        /// </summary>
        public async Task<bool> ValidateSisConnectionAsync()
        {
            try
            {
                return await _sisApiClient.TestConnectivityAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to validate SIS connection");
                return false;
            }
        }

        /// <summary>
        /// Checks if the local student data is considered fresh
        /// </summary>
        public async Task<bool> IsDataFreshAsync()
        {
            var lastSyncDate = await GetLastSuccessfulSyncDateAsync();
            if (!lastSyncDate.HasValue)
                return false;

            var dataAge = DateTime.UtcNow - lastSyncDate.Value;
            return dataAge <= _options.DataFreshnessThreshold;
        }

        /// <summary>
        /// Gets the health status of the sync service
        /// </summary>
        public async Task<SyncServiceHealthStatus> GetHealthStatusAsync()
        {
            var healthStatus = new SyncServiceHealthStatus
            {
                LastChecked = DateTime.UtcNow
            };

            try
            {
                // Check SIS API connectivity
                healthStatus.SisApiConnected = await ValidateSisConnectionAsync();

                // Check database connectivity
                healthStatus.DatabaseConnected = await _context.Database.CanConnectAsync();

                // Check if auto sync is enabled
                healthStatus.AutoSyncEnabled = _options.AutoSyncEnabled;

                // Get last successful sync
                healthStatus.LastSuccessfulSync = await GetLastSuccessfulSyncDateAsync();

                // Count pending sync operations
                healthStatus.PendingSyncOperations = await _context.SyncHistories
                    .CountAsync(sh => sh.Status == Data.SyncStatus.InProgress);

                // Determine overall health
                healthStatus.IsHealthy = healthStatus.SisApiConnected &&
                                       healthStatus.DatabaseConnected &&
                                       healthStatus.PendingSyncOperations == 0;

                healthStatus.Status = healthStatus.IsHealthy ? "Healthy" : "Unhealthy";

                // Add additional info
                healthStatus.AdditionalInfo["TotalStudents"] = await _context.SisStudents.CountAsync();
                healthStatus.AdditionalInfo["DataFresh"] = await IsDataFreshAsync();
                healthStatus.AdditionalInfo["ConfiguredSyncSchedule"] = _options.SyncSchedule;
            }
            catch (Exception ex)
            {
                healthStatus.IsHealthy = false;
                healthStatus.Status = "Error";
                healthStatus.ErrorMessage = ex.Message;
                _logger.LogError(ex, "Error checking sync service health");
            }

            return healthStatus;
        }

        #endregion

        #region Background Job Management

        /// <summary>
        /// Schedules automatic sync operations based on configuration
        /// </summary>
        public Task<bool> ScheduleAutomaticSyncAsync()
        {
            try
            {
                if (!_options.AutoSyncEnabled)
                {
                    _logger.LogInformation("Automatic sync is disabled in configuration");
                    return Task.FromResult(false);
                }

                // TODO: Implement background job scheduling using Hangfire or similar
                // For now, just log the intent
                _logger.LogInformation("Automatic sync scheduling requested with schedule: {Schedule}", _options.SyncSchedule);

                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to schedule automatic sync");
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// Unschedules automatic sync operations
        /// </summary>
        public Task<bool> UnscheduleAutomaticSyncAsync()
        {
            try
            {
                // TODO: Implement background job unscheduling
                _logger.LogInformation("Automatic sync unscheduling requested");
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to unschedule automatic sync");
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// Gets the status of automatic sync scheduling
        /// </summary>
        public Task<bool> IsAutomaticSyncScheduledAsync()
        {
            // TODO: Check actual background job status
            return Task.FromResult(_options.AutoSyncEnabled);
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Creates a new sync history record
        /// </summary>
        private async Task<SyncHistory> CreateSyncHistoryAsync(SyncType syncType, string? triggeredByUserId)
        {
            var syncHistory = new SyncHistory
            {
                SyncType = syncType,
                Status = Data.SyncStatus.InProgress,
                StartTime = DateTime.UtcNow,
                TriggeredByUserId = triggeredByUserId,
                IsAutomated = string.IsNullOrEmpty(triggeredByUserId),
                BatchSize = _options.BatchSize,
                ApiEndpoint = _options.StudentsUrl
            };

            _context.SyncHistories.Add(syncHistory);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Created sync history record: {SyncId}, Type: {SyncType}",
                syncHistory.SyncId, syncType);

            return syncHistory;
        }

        /// <summary>
        /// Performs a full synchronization of all student data
        /// </summary>
        private async Task<SyncResult> PerformFullSyncAsync(SyncHistory syncHistory, CancellationToken cancellationToken)
        {
            var result = new SyncResult
            {
                SyncId = syncHistory.SyncId,
                SyncType = SyncType.Full,
                Status = Data.SyncStatus.InProgress,
                StartTime = syncHistory.StartTime
            };

            try
            {
                // Update progress
                syncHistory.UpdateProgress(0, "Fetching student data from SIS API");
                await _context.SaveChangesAsync(cancellationToken);

                // Fetch all students from SIS API
                var sisStudents = await _sisApiClient.GetAllStudentsAsync(cancellationToken);
                var studentsList = sisStudents.ToList();

                result.RecordsProcessed = studentsList.Count;
                syncHistory.RecordsProcessed = studentsList.Count;

                _logger.LogInformation("Fetched {Count} students from SIS API for full sync", studentsList.Count);

                // Process students in batches
                var batches = studentsList.Chunk(_options.BatchSize);
                var processedCount = 0;
                var batchNumber = 0;
                var totalBatches = (int)Math.Ceiling((double)studentsList.Count / _options.BatchSize);

                _logger.LogInformation("Processing {TotalStudents} students in {TotalBatches} batches of {BatchSize}",
                    studentsList.Count, totalBatches, _options.BatchSize);

                foreach (var batch in batches)
                {
                    batchNumber++;
                    var batchStartTime = DateTime.UtcNow;

                    try
                    {
                        cancellationToken.ThrowIfCancellationRequested();

                        _logger.LogInformation("Processing batch {BatchNumber}/{TotalBatches} with {BatchSize} students",
                            batchNumber, totalBatches, batch.Length);

                        var batchResult = await ProcessStudentBatchAsync(batch, cancellationToken);

                        result.RecordsAdded += batchResult.Added;
                        result.RecordsUpdated += batchResult.Updated;
                        result.RecordsSkipped += batchResult.Skipped;
                        result.RecordsErrored += batchResult.Errored;

                        processedCount += batch.Length;
                        var progressPercentage = (int)((double)processedCount / studentsList.Count * 100);

                        syncHistory.UpdateProgress(progressPercentage, $"Processed {processedCount}/{studentsList.Count} students");
                        syncHistory.RecordsAdded = result.RecordsAdded;
                        syncHistory.RecordsUpdated = result.RecordsUpdated;
                        syncHistory.RecordsSkipped = result.RecordsSkipped;
                        syncHistory.RecordsErrored = result.RecordsErrored;

                        await _context.SaveChangesAsync(cancellationToken);

                        var batchDuration = DateTime.UtcNow - batchStartTime;
                        _logger.LogInformation("Batch {BatchNumber}/{TotalBatches} completed in {Duration}ms. Progress: {Progress}% ({Processed}/{Total})",
                            batchNumber, totalBatches, batchDuration.TotalMilliseconds, progressPercentage, processedCount, studentsList.Count);

                        // Send SignalR progress update
                        await _hubContext.SendSyncProgressUpdate(syncHistory.SyncId, progressPercentage,
                            $"Processed {processedCount}/{studentsList.Count} students", processedCount);

                        // Fire progress event
                        SyncProgressUpdated?.Invoke(this, new SyncProgressEventArgs
                        {
                            Progress = new SyncProgress
                            {
                                SyncId = syncHistory.SyncId,
                                SyncType = SyncType.Full,
                                TotalRecords = studentsList.Count,
                                ProcessedRecords = processedCount,
                                PercentageComplete = progressPercentage,
                                CurrentStep = $"Processed {processedCount}/{studentsList.Count} students",
                                StartTime = syncHistory.StartTime,
                                ElapsedTime = DateTime.UtcNow - syncHistory.StartTime
                            }
                        });
                    }
                    catch (OperationCanceledException)
                    {
                        _logger.LogWarning("Sync operation was cancelled at batch {BatchNumber}/{TotalBatches}", batchNumber, totalBatches);
                        throw;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing batch {BatchNumber}/{TotalBatches}: {ErrorMessage}",
                            batchNumber, totalBatches, ex.Message);

                        // Update error count and continue with next batch
                        result.RecordsErrored += batch.Length;
                        processedCount += batch.Length;

                        // Update progress even for failed batch
                        var progressPercentage = (int)((double)processedCount / studentsList.Count * 100);
                        syncHistory.UpdateProgress(progressPercentage, $"Processed {processedCount}/{studentsList.Count} students (batch {batchNumber} failed)");
                        syncHistory.RecordsErrored = result.RecordsErrored;

                        try
                        {
                            await _context.SaveChangesAsync(cancellationToken);
                        }
                        catch (Exception saveEx)
                        {
                            _logger.LogError(saveEx, "Failed to save progress after batch error");
                        }

                        // Continue with next batch instead of failing entire sync
                        continue;
                    }
                }

                result.Status = Data.SyncStatus.Completed;
                result.EndTime = DateTime.UtcNow;

                _logger.LogInformation("Full sync completed successfully. Added: {Added}, Updated: {Updated}, Skipped: {Skipped}, Errors: {Errors}",
                    result.RecordsAdded, result.RecordsUpdated, result.RecordsSkipped, result.RecordsErrored);

                return result;
            }
            catch (Exception ex)
            {
                result.Status = Data.SyncStatus.Failed;
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.UtcNow;
                throw;
            }
        }

        /// <summary>
        /// Performs an incremental synchronization of changed student data
        /// </summary>
        private async Task<SyncResult> PerformIncrementalSyncAsync(SyncHistory syncHistory, CancellationToken cancellationToken)
        {
            // For now, incremental sync will fetch all data and compare hashes
            // In a real implementation, this would use timestamps or change tracking
            return await PerformFullSyncAsync(syncHistory, cancellationToken);
        }

        /// <summary>
        /// Processes a batch of students from SIS API
        /// </summary>
        private async Task<BatchProcessResult> ProcessStudentBatchAsync(SisStudentApiResponse[] batch, CancellationToken cancellationToken)
        {
            var result = new BatchProcessResult();
            var batchStartTime = DateTime.UtcNow;

            _logger.LogDebug("Processing batch of {BatchSize} students", batch.Length);

            // Get all student IDs in this batch
            var batchStudentIds = batch
                .Where(s => !string.IsNullOrWhiteSpace(s.StudentID))
                .Select(s => s.StudentID)
                .Distinct() // Ensure no duplicates in the batch
                .ToList();

            if (!batchStudentIds.Any())
            {
                _logger.LogWarning("No valid student IDs found in batch");
                result.Errored = batch.Length;
                return result;
            }

            _logger.LogDebug("Batch contains {UniqueStudentCount} unique student IDs: {StudentIds}",
                batchStudentIds.Count, string.Join(", ", batchStudentIds));

            // Load all existing students for this batch in one query with explicit tracking
            var existingStudents = await _context.SisStudents
                .AsTracking() // Ensure EF tracks these entities
                .Where(s => batchStudentIds.Contains(s.StudentID))
                .ToDictionaryAsync(s => s.StudentID, cancellationToken);

            _logger.LogDebug("Found {ExistingCount} existing students out of {BatchCount} in batch: {ExistingStudentIds}",
                existingStudents.Count, batchStudentIds.Count, string.Join(", ", existingStudents.Keys));

            // Process each unique student ID only once
            var processedStudentIds = new HashSet<string>();

            foreach (var sisStudent in batch)
            {
                try
                {
                    cancellationToken.ThrowIfCancellationRequested();

                    // Validate student data
                    if (string.IsNullOrWhiteSpace(sisStudent.StudentID))
                    {
                        _logger.LogWarning("Skipping student with empty StudentID");
                        result.Errored++;
                        continue;
                    }

                    // Skip if we've already processed this student ID in this batch
                    if (processedStudentIds.Contains(sisStudent.StudentID))
                    {
                        _logger.LogDebug("Skipping duplicate StudentID in batch: {StudentId}", sisStudent.StudentID);
                        result.Skipped++;
                        continue;
                    }

                    processedStudentIds.Add(sisStudent.StudentID);

                    // Check if student already exists in our loaded dictionary
                    if (!existingStudents.TryGetValue(sisStudent.StudentID, out var existingStudent))
                    {
                        // Double-check: Query database directly to ensure student doesn't exist
                        var dbStudent = await _context.SisStudents
                            .AsNoTracking()
                            .FirstOrDefaultAsync(s => s.StudentID == sisStudent.StudentID, cancellationToken);

                        if (dbStudent != null)
                        {
                            _logger.LogWarning("Student {StudentId} exists in database but not in dictionary - potential concurrency issue", sisStudent.StudentID);
                            // Treat as existing student and update
                            var trackedStudent = await _context.SisStudents
                                .FirstOrDefaultAsync(s => s.StudentID == sisStudent.StudentID, cancellationToken);

                            if (trackedStudent != null)
                            {
                                var newHash = GenerateStudentHash(sisStudent);
                                if (trackedStudent.DataHash != newHash)
                                {
                                    UpdateSisStudentEntity(trackedStudent, sisStudent);
                                    result.Updated++;
                                    _logger.LogDebug("Updated existing student (concurrency fix): {StudentId}", sisStudent.StudentID);
                                }
                                else
                                {
                                    result.Skipped++;
                                }
                            }
                        }
                        else
                        {
                            // Truly new student - safe to add
                            var newStudent = MapSisStudentApiResponseToEntity(sisStudent);
                            _context.SisStudents.Add(newStudent);
                            result.Added++;
                            _logger.LogDebug("Added new student: {StudentId}", sisStudent.StudentID);
                        }
                    }
                    else
                    {
                        // Check if data has changed
                        var newHash = GenerateStudentHash(sisStudent);
                        if (existingStudent.DataHash != newHash)
                        {
                            // Update existing student
                            UpdateSisStudentEntity(existingStudent, sisStudent);
                            result.Updated++;
                            _logger.LogDebug("Updated student: {StudentId}", sisStudent.StudentID);
                        }
                        else
                        {
                            // No changes
                            result.Skipped++;
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing student {StudentId}: {ErrorMessage}",
                        sisStudent?.StudentID ?? "Unknown", ex.Message);
                    result.Errored++;
                }
            }

            // Save changes with retry logic for duplicate key errors
            await SaveBatchChangesWithRetryAsync(result, batchStudentIds, batchStartTime, cancellationToken);

            return result;
        }

        /// <summary>
        /// Saves batch changes with retry logic for duplicate key errors
        /// </summary>
        private async Task SaveBatchChangesWithRetryAsync(BatchProcessResult result, List<string> batchStudentIds, DateTime batchStartTime, CancellationToken cancellationToken)
        {
            const int maxRetries = 3;
            var retryCount = 0;

            while (retryCount <= maxRetries)
            {
                try
                {
                    _logger.LogDebug("Saving batch changes to database (attempt {Attempt}/{MaxAttempts})...", retryCount + 1, maxRetries + 1);
                    await _context.SaveChangesAsync(cancellationToken);

                    var batchDuration = DateTime.UtcNow - batchStartTime;
                    _logger.LogDebug("Batch processed successfully in {Duration}ms. Added: {Added}, Updated: {Updated}, Skipped: {Skipped}, Errors: {Errors}",
                        batchDuration.TotalMilliseconds, result.Added, result.Updated, result.Skipped, result.Errored);

                    return; // Success - exit retry loop
                }
                catch (DbUpdateException dbEx) when (dbEx.InnerException?.Message?.Contains("duplicate key") == true && retryCount < maxRetries)
                {
                    retryCount++;
                    _logger.LogWarning("Duplicate key error on attempt {Attempt}, retrying in {DelayMs}ms. Error: {Error}",
                        retryCount, retryCount * 1000, dbEx.InnerException.Message);

                    // Clear the context to reset tracking state
                    _context.ChangeTracker.Clear();

                    // Wait before retry with exponential backoff
                    await Task.Delay(retryCount * 1000, cancellationToken);

                    // Re-process the batch with fresh context state
                    _logger.LogDebug("Re-processing batch after duplicate key error...");
                    // Note: The batch will be re-processed by the calling method if this fails
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to save batch changes to database: {ErrorMessage}", ex.Message);

                    // Log detailed information about the failed batch
                    _logger.LogError("Failed batch details - Added: {Added}, Updated: {Updated}, Skipped: {Skipped}, StudentIDs: {StudentIds}",
                        result.Added, result.Updated, result.Skipped, string.Join(", ", batchStudentIds));

                    throw; // Re-throw non-duplicate-key errors immediately
                }
            }

            // If we get here, all retries failed
            _logger.LogError("Failed to save batch after {MaxRetries} retries due to persistent duplicate key errors", maxRetries);
            throw new InvalidOperationException($"Failed to save batch after {maxRetries} retries due to persistent duplicate key errors");
        }

        /// <summary>
        /// Maps SIS API response to SisStudent entity
        /// </summary>
        private static SisStudent MapSisStudentApiResponseToEntity(SisStudentApiResponse apiResponse)
        {
            var entity = new SisStudent
            {
                StudentID = apiResponse.StudentID,
                FullNameAR = apiResponse.FullNameAR,
                FullNameEN = apiResponse.FullNameEN,
                Gender = apiResponse.Gender,
                Nationality = apiResponse.Nationality,
                NationalID = apiResponse.NationalID,
                BirthDate = ParseBirthDate(apiResponse.BirthDate),
                Email = apiResponse.Email,
                MobileNo = apiResponse.MobileNo,
                EnrollmentStatus = apiResponse.EnrollmentStatus,
                Major = apiResponse.Major,
                Level = apiResponse.Level
            };

            entity.UpdateSyncInfo();
            return entity;
        }

        /// <summary>
        /// Updates an existing SisStudent entity with new data
        /// </summary>
        private static void UpdateSisStudentEntity(SisStudent entity, SisStudentApiResponse apiResponse)
        {
            entity.FullNameAR = apiResponse.FullNameAR;
            entity.FullNameEN = apiResponse.FullNameEN;
            entity.Gender = apiResponse.Gender;
            entity.Nationality = apiResponse.Nationality;
            entity.NationalID = apiResponse.NationalID;
            entity.BirthDate = ParseBirthDate(apiResponse.BirthDate);
            entity.Email = apiResponse.Email;
            entity.MobileNo = apiResponse.MobileNo;
            entity.EnrollmentStatus = apiResponse.EnrollmentStatus;
            entity.Major = apiResponse.Major;
            entity.Level = apiResponse.Level;

            entity.UpdateSyncInfo();
        }

        /// <summary>
        /// Parses birth date string from SIS API format to DateTime
        /// </summary>
        /// <param name="birthDateString">Birth date in MM/dd/yyyy format from SIS API</param>
        /// <returns>Parsed DateTime or default value if parsing fails</returns>
        private static DateTime ParseBirthDate(string birthDateString)
        {
            if (string.IsNullOrWhiteSpace(birthDateString))
                return DateTime.MinValue;

            // Try to parse MM/dd/yyyy format first (SIS API format)
            if (DateTime.TryParseExact(birthDateString, "MM/dd/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedDate))
            {
                return parsedDate;
            }

            // Try to parse dd/MM/yyyy format as fallback
            if (DateTime.TryParseExact(birthDateString, "dd/MM/yyyy", CultureInfo.InvariantCulture, DateTimeStyles.None, out parsedDate))
            {
                return parsedDate;
            }

            // Try general DateTime parsing as last resort
            if (DateTime.TryParse(birthDateString, CultureInfo.InvariantCulture, DateTimeStyles.None, out parsedDate))
            {
                return parsedDate;
            }

            // If all parsing attempts fail, log warning and return minimum value
            // Note: In a real application, you might want to inject a logger here
            Console.WriteLine($"Warning: Failed to parse birth date '{birthDateString}', using default value");
            return DateTime.MinValue;
        }

        /// <summary>
        /// Generates a hash for student data comparison
        /// </summary>
        private static string GenerateStudentHash(SisStudentApiResponse apiResponse)
        {
            var dataString = $"{apiResponse.StudentID}|{apiResponse.FullNameEN}|{apiResponse.FullNameAR}|{apiResponse.Gender}|{apiResponse.Nationality}|{apiResponse.NationalID}|{apiResponse.BirthDate}|{apiResponse.Email}|{apiResponse.MobileNo}|{apiResponse.EnrollmentStatus}|{apiResponse.Major}|{apiResponse.Level}";

            using var sha256 = System.Security.Cryptography.SHA256.Create();
            var hashBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(dataString));
            return Convert.ToHexString(hashBytes);
        }

        /// <summary>
        /// Completes the sync history record with final results
        /// </summary>
        private async Task CompleteSyncHistoryAsync(SyncHistory syncHistory, SyncResult result)
        {
            syncHistory.Status = result.Status;
            syncHistory.EndTime = result.EndTime;
            syncHistory.RecordsProcessed = result.RecordsProcessed;
            syncHistory.RecordsAdded = result.RecordsAdded;
            syncHistory.RecordsUpdated = result.RecordsUpdated;
            syncHistory.RecordsSkipped = result.RecordsSkipped;
            syncHistory.RecordsErrored = result.RecordsErrored;

            if (result.Status == Data.SyncStatus.Completed)
            {
                syncHistory.MarkAsCompleted();
            }
            else if (!string.IsNullOrEmpty(result.ErrorMessage))
            {
                syncHistory.ErrorMessage = result.ErrorMessage;
            }

            await _context.SaveChangesAsync();
        }

        #endregion

        #region Helper Classes

        /// <summary>
        /// Result of processing a batch of students
        /// </summary>
        private class BatchProcessResult
        {
            public int Added { get; set; }
            public int Updated { get; set; }
            public int Skipped { get; set; }
            public int Errored { get; set; }
        }

        #endregion
    }
}
