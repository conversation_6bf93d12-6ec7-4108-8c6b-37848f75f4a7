/**
 * Admin Dashboard Enhancements - forms.ktech
 * Phase 5: Enhanced JavaScript for admin dashboard with improved UX
 * Adds data visualization, mobile responsiveness, and enhanced interactions
 */

// ===== ADMIN DASHBOARD ENHANCEMENTS =====
window.AdminDashboard = window.AdminDashboard || {};

AdminDashboard.Enhancements = {
    /**
     * Initializes dashboard enhancements
     */
    init: function() {
        this.setupMobileResponsiveness();
        this.setupDataVisualization();
        this.setupEnhancedInteractions();
        this.setupKeyboardNavigation();
    },

    /**
     * Sets up mobile responsiveness improvements
     */
    setupMobileResponsiveness: function() {
        // Add mobile-friendly table scrolling
        $('.overflow-x-auto table').wrap('<div class="table-container"></div>');
        
        // Add mobile navigation for tabs
        if (window.innerWidth <= 768) {
            this.setupMobileTabNavigation();
        }
        
        // Handle window resize
        $(window).on('resize', () => {
            if (window.innerWidth <= 768) {
                this.setupMobileTabNavigation();
            } else {
                this.removeMobileTabNavigation();
            }
        });
    },

    /**
     * Sets up mobile tab navigation
     */
    setupMobileTabNavigation: function() {
        const tabContainer = $('.tab-container');
        if (tabContainer.length && !tabContainer.hasClass('mobile-tabs')) {
            tabContainer.addClass('mobile-tabs');
            
            // Add dropdown for tab selection on mobile
            const activeTab = tabContainer.find('.active').text().trim();
            const dropdown = $(`
                <div class="mobile-tab-dropdown md:hidden mb-4">
                    <select class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        <option value="dashboard">Dashboard</option>
                        <option value="history">Sync History</option>
                        <option value="settings">Settings</option>
                    </select>
                </div>
            `);
            
            tabContainer.before(dropdown);
            
            // Define whitelist of valid routes
            const validRoutes = {
                'dashboard': '/Admin/SyncDashboard',
                'history': '/Admin/SyncHistory',
                'settings': '/Admin/SyncSettings'
            };
            
            // Handle dropdown change
            dropdown.find('select').on('change', function() {
                const selectedTab = $(this).val();
                const targetUrl = validRoutes[selectedTab];
                
                // Only redirect if the selected value exists in the whitelist
                if (targetUrl) {
                    window.location.href = targetUrl;
                } else {
                    console.warn('Invalid route selected:', selectedTab);
                }
            });
        }
    },

    /**
     * Removes mobile tab navigation
     */
    removeMobileTabNavigation: function() {
        $('.mobile-tab-dropdown').remove();
        $('.tab-container').removeClass('mobile-tabs');
    },

    /**
     * Sets up data visualization enhancements
     */
    setupDataVisualization: function() {
        // Add progress animations
        this.animateProgressBars();
        
        // Add hover effects for statistics cards
        this.setupStatisticsCardEffects();
        
        // Add real-time data updates
        this.setupRealTimeDataUpdates();
    },

    /**
     * Animates progress bars
     */
    animateProgressBars: function() {
        $('.progress-bar').each(function() {
            const $bar = $(this);
            const targetWidth = $bar.data('progress') || $bar.attr('style').match(/width:\s*(\d+)%/)?.[1] || 0;
            
            $bar.css('width', '0%');
            setTimeout(() => {
                $bar.css({
                    'width': targetWidth + '%',
                    'transition': 'width 1s ease-in-out'
                });
            }, 100);
        });
    },

    /**
     * Sets up statistics card hover effects
     */
    setupStatisticsCardEffects: function() {
        $('.stats-card').hover(
            function() {
                $(this).addClass('transform scale-105 shadow-lg transition-all duration-200');
            },
            function() {
                $(this).removeClass('transform scale-105 shadow-lg transition-all duration-200');
            }
        );
    },

    /**
     * Sets up real-time data updates
     */
    setupRealTimeDataUpdates: function() {
        // Update timestamps to relative time
        this.updateRelativeTimestamps();
        
        // Set up periodic updates
        setInterval(() => {
            this.updateRelativeTimestamps();
        }, 60000); // Update every minute
    },

    /**
     * Updates timestamps to show relative time
     */
    updateRelativeTimestamps: function() {
        $('.timestamp').each(function() {
            const $timestamp = $(this);
            const dateStr = $timestamp.data('timestamp') || $timestamp.text();
            const date = new Date(dateStr);
            
            if (!isNaN(date.getTime())) {
                const relativeTime = this.getRelativeTime(date);
                $timestamp.text(relativeTime);
                $timestamp.attr('title', date.toLocaleString());
            }
        }.bind(this));
    },

    /**
     * Gets relative time string
     * @param {Date} date - The date to convert
     * @returns {string} - Relative time string
     */
    getRelativeTime: function(date) {
        const now = new Date();
        const diff = now - date;
        const minutes = Math.floor(diff / (1000 * 60));
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        
        if (days > 0) {
            return `${days} day${days > 1 ? 's' : ''} ago`;
        } else if (hours > 0) {
            return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        } else if (minutes > 0) {
            return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        } else {
            return 'Just now';
        }
    },

    /**
     * Sets up enhanced interactions
     */
    setupEnhancedInteractions: function() {
        // Add confirmation dialogs with better UX
        this.setupConfirmationDialogs();
        
        // Add loading states for buttons
        this.setupButtonLoadingStates();
        
        // Add tooltips for complex elements
        this.setupTooltips();
    },

    /**
     * Sets up enhanced confirmation dialogs
     */
    setupConfirmationDialogs: function() {
        $('[data-confirm]').on('click', function(e) {
            e.preventDefault();
            
            const message = $(this).data('confirm');
            const action = $(this).attr('href') || $(this).data('action');
            
            // Create custom confirmation modal
            const modal = $(`
                <div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                    <div class="bg-white rounded-lg p-6 max-w-md mx-4 dark:bg-gray-800">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Confirm Action</h3>
                        <p class="text-gray-600 dark:text-gray-300 mb-6">${message}</p>
                        <div class="flex justify-end space-x-3">
                            <button class="cancel-btn px-4 py-2 text-gray-600 bg-gray-200 rounded-lg hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500">
                                Cancel
                            </button>
                            <button class="confirm-btn px-4 py-2 text-white bg-red-600 rounded-lg hover:bg-red-700">
                                Confirm
                            </button>
                        </div>
                    </div>
                </div>
            `);
            
            $('body').append(modal);
            
            modal.find('.cancel-btn').on('click', () => modal.remove());
            modal.find('.confirm-btn').on('click', () => {
                modal.remove();
                if (action) {
                    window.location.href = action;
                }
            });
            
            // Close on backdrop click
            modal.on('click', function(e) {
                if (e.target === this) {
                    $(this).remove();
                }
            });
        });
    },

    /**
     * Sets up button loading states
     */
    setupButtonLoadingStates: function() {
        $('button[type="submit"], .action-btn').on('click', function() {
            const $btn = $(this);
            const originalText = $btn.text();
            
            $btn.prop('disabled', true)
                .html('<i class="fas fa-spinner fa-spin mr-2"></i>Processing...');
            
            // Reset after 5 seconds if no form submission
            setTimeout(() => {
                if ($btn.prop('disabled')) {
                    $btn.prop('disabled', false).text(originalText);
                }
            }, 5000);
        });
    },

    /**
     * Sets up tooltips for complex elements
     */
    setupTooltips: function() {
        // Add tooltips to status indicators
        $('.status-indicator').each(function() {
            const status = $(this).text().trim();
            let tooltip = '';
            
            switch (status.toLowerCase()) {
                case 'completed':
                    tooltip = 'Synchronization completed successfully';
                    break;
                case 'failed':
                    tooltip = 'Synchronization failed - check error details';
                    break;
                case 'inprogress':
                    tooltip = 'Synchronization is currently running';
                    break;
                case 'cancelled':
                    tooltip = 'Synchronization was cancelled by user';
                    break;
            }
            
            if (tooltip) {
                $(this).attr('title', tooltip);
            }
        });
    },

    /**
     * Sets up keyboard navigation
     */
    setupKeyboardNavigation: function() {
        // Add keyboard shortcuts
        $(document).on('keydown', function(e) {
            // Ctrl/Cmd + R for refresh
            if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
                e.preventDefault();
                $('#refreshBtn').click();
            }
            
            // Escape to close modals
            if (e.key === 'Escape') {
                $('.modal, .confirmation-modal').remove();
            }
        });
        
        // Add focus management for modals
        $(document).on('shown.modal', '.modal', function() {
            $(this).find('button, input, select').first().focus();
        });
    }
};

// ===== AUTO-INITIALIZATION =====
$(document).ready(function() {
    // Initialize enhancements if on admin pages
    const currentPath = window.location.pathname.toLowerCase();
    if (currentPath.includes('/admin/sync')) {
        AdminDashboard.Enhancements.init();
    }
});
