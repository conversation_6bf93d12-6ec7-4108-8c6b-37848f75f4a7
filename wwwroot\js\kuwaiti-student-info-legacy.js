/* 
 * Kuwaiti Student Info Form JavaScript - forms.ktech
 * Form-specific JavaScript functionality for the Kuwaiti Student Information form
 */

// ===== NAMESPACE =====
window.KuwaitiStudentInfo = window.KuwaitiStudentInfo || {};

// ===== FORM INITIALIZATION =====
KuwaitiStudentInfo.init = function() {
    // Initialize form functionality
    this.initializeConditionalFields();
    this.initializeFormValidation();
    this.initializeFormSubmission();
    
    // Set initial state
    this.updateConditionalFields();
    this.checkEligibility();
};

// ===== CONDITIONAL FIELDS LOGIC =====
KuwaitiStudentInfo.initializeConditionalFields = function() {
    // Listen for nationality checkbox changes
    $('.nationality-checkbox').on('change', () => {
        this.updateConditionalFields();
        this.checkEligibility();
    });
};

KuwaitiStudentInfo.updateConditionalFields = function() {
    const studentKuwaiti = $('#studentKuwaiti').is(':checked');
    const fatherKuwaiti = $('#fatherKuwaiti').is(':checked');
    const motherKuwaiti = $('#motherKuwaiti').is(':checked');

    // Student documents
    SharedForms.ConditionalFields.toggle('student-not-kuwaiti', !studentKuwaiti);

    // Father documents
    SharedForms.ConditionalFields.toggle('father-kuwaiti', fatherKuwaiti);
    SharedForms.ConditionalFields.toggle('father-not-kuwaiti', !fatherKuwaiti);

    // Mother documents
    SharedForms.ConditionalFields.toggle('mother-kuwaiti', motherKuwaiti);
    SharedForms.ConditionalFields.toggle('mother-not-kuwaiti', !motherKuwaiti);

    // Update required attributes on file inputs
    this.updateFileRequirements();
};

KuwaitiStudentInfo.updateFileRequirements = function() {
    // Reset all file input requirements
    $('input[type="file"]').removeAttr('required');

    // Student Civil ID is always required
    $('input[name="StudentCivilIdFile"]').attr('required', 'required');

    // Set conditional requirements based on visibility (using :not(.hidden) instead of :visible)
    $('.conditional-field:not(.hidden) input[type="file"]').each(function() {
        $(this).attr('required', 'required');
    });
};

KuwaitiStudentInfo.checkEligibility = function() {
    const studentKuwaiti = $('#studentKuwaiti').is(':checked');
    const fatherKuwaiti = $('#fatherKuwaiti').is(':checked');
    const motherKuwaiti = $('#motherKuwaiti').is(':checked');

    const isEligible = studentKuwaiti || fatherKuwaiti || motherKuwaiti;

    if (isEligible) {
        $('#eligibility-alert').addClass('hidden');
        $('#submit-btn').prop('disabled', false);
    } else {
        $('#eligibility-alert').removeClass('hidden');
        $('#submit-btn').prop('disabled', true);
    }
};

// ===== FORM VALIDATION =====
KuwaitiStudentInfo.initializeFormValidation = function() {
    // Custom validation for Civil ID format
    $('input[name="StudentCivilId"]').on('input', function() {
        const value = $(this).val().replace(/\D/g, ''); // Remove non-digits
        $(this).val(value);
        
        if (value.length === 12) {
            SharedForms.FileValidation.setValidationState(this, true);
        } else if (value.length > 0) {
            SharedForms.FileValidation.setValidationState(this, false);
        } else {
            $(this).removeClass('border-green-500 border-red-500');
        }
    });

    // Real-time validation for required fields
    $('input[required], select[required]').on('blur', function() {
        SharedForms.FileValidation.validateField(this);
    });

    // File upload validation
    $('input[type="file"]').on('change', function() {
        const maxSize = parseInt($(this).data('max-size')) || 5;
        SharedForms.FileValidation.validateFileInput(this, maxSize);
    });
};

// ===== FORM SUBMISSION =====
KuwaitiStudentInfo.initializeFormSubmission = function() {
    $('#student-form').on('submit', (e) => {
        e.preventDefault();
        
        // Show loading spinner
        if (window.LoadingSpinner) {
            window.LoadingSpinner.show('form-loading', 'Processing your application...');
        }
        
        // Validate all fields
        let isValid = this.validateAllFields();

        if (isValid) {
            // Submit the form
            e.target.submit();
        } else {
            // Hide loading spinner
            if (window.LoadingSpinner) {
                window.LoadingSpinner.hide('form-loading');
            }
            
            // Scroll to first error
            SharedForms.Utils.scrollToFirstError();
        }
    });
};

KuwaitiStudentInfo.validateAllFields = function() {
    let isValid = true;
    
    // Validate required text fields
    $('input[required], select[required]').each(function() {
        if (!SharedForms.FileValidation.validateField(this)) {
            isValid = false;
        }
    });

    // Validate conditionally required file fields
    $('.conditional-field:not(.hidden) input[type="file"]').each(function() {
        const maxSize = parseInt($(this).data('max-size')) || 5;
        if (!SharedForms.FileValidation.validateFileInput(this, maxSize)) {
            isValid = false;
        }
    });

    // Validate always required file fields
    const studentCivilIdFile = $('input[name="StudentCivilIdFile"]')[0];
    if (studentCivilIdFile) {
        const maxSize = parseInt($(studentCivilIdFile).data('max-size')) || 5;
        if (!SharedForms.FileValidation.validateFileInput(studentCivilIdFile, maxSize)) {
            isValid = false;
        }
    }

    // Check eligibility
    const studentKuwaiti = $('#studentKuwaiti').is(':checked');
    const fatherKuwaiti = $('#fatherKuwaiti').is(':checked');
    const motherKuwaiti = $('#motherKuwaiti').is(':checked');
    const isEligible = studentKuwaiti || fatherKuwaiti || motherKuwaiti;

    if (!isEligible) {
        isValid = false;
        $('#eligibility-alert').removeClass('hidden');
    }

    return isValid;
};

// ===== SUMMARY PAGE FUNCTIONALITY =====
KuwaitiStudentInfo.Summary = {
    init: function() {
        this.initializeDownloadTracking();
    },

    initializeDownloadTracking: function() {
        // Track download clicks for analytics
        $('a[href*="DownloadFile"]').on('click', function() {
            const fileName = $(this).attr('href').split('fileName=')[1];
            
            // Show download confirmation
            const documentName = $(this).closest('.card').find('h6').text();
            SharedForms.Toast.show(`${documentName} download started`, 'success');
        });
    }
};

// ===== NOT ELIGIBLE PAGE FUNCTIONALITY =====
KuwaitiStudentInfo.NotEligible = {
    init: function() {
        this.initializeAnimations();
        this.initializeTracking();
    },

    initializeAnimations: function() {
        // Add animation to cards on page load
        $('.card').addClass('animate__fadeInUp');
    },

    initializeTracking: function() {
        // Track user interactions for analytics (if needed)
        $('.btn').on('click', function() {
            const action = $(this).text().trim();
            // Add analytics tracking here if needed
        });
    }
};

// ===== AUTO-INITIALIZATION =====
$(document).ready(function() {
    // Initialize based on current page
    const currentPath = window.location.pathname.toLowerCase();
    
    if (currentPath.includes('collectinfo')) {
        KuwaitiStudentInfo.init();
    } else if (currentPath.includes('summary')) {
        KuwaitiStudentInfo.Summary.init();
    } else if (currentPath.includes('noteligible')) {
        KuwaitiStudentInfo.NotEligible.init();
    }
});
