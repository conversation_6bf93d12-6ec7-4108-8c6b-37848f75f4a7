/**
 * SIS Integration Test Suite - forms.ktech
 * Phase 5: Testing utilities for SIS integration features
 * Provides automated testing and validation for SIS functionality
 */

// ===== SIS INTEGRATION TESTING =====
window.SisIntegrationTest = window.SisIntegrationTest || {};

SisIntegrationTest = {
    /**
     * Runs all SIS integration tests
     */
    runAllTests: function() {
        
        const tests = [
            this.testSisFieldDetection,
            this.testReadOnlyFieldBehavior,
            this.testSisBadgeDisplay,
            this.testAccessibilityFeatures,
            this.testDataFreshnessIndicators,
            this.testFormValidation,
            this.testMobileResponsiveness
        ];
        
        let passed = 0;
        let failed = 0;
        
        tests.forEach((test, index) => {
            try {
                const result = test.call(this);
                if (result) {
                    passed++;
                } else {
                    failed++;
                }
            } catch (error) {
                failed++;
            }
        });
        
        return { passed, failed, total: tests.length };
    },

    /**
     * Tests SIS field detection
     */
    testSisFieldDetection: function() {
        
        // Check if SIS fields are properly detected
        const sisFields = $('.sis-readonly, .sis-field-container input');
        const sisBadges = $('.sis-badge');
        
        if (sisFields.length === 0) {
            return true; // Not a failure if no SIS data
        }
        
        return sisFields.length > 0;
    },

    /**
     * Tests read-only field behavior
     */
    testReadOnlyFieldBehavior: function() {
        
        const readOnlyFields = $('.sis-readonly');
        if (readOnlyFields.length === 0) {
            return true; // Not a failure if no read-only fields
        }
        
        let allReadOnly = true;
        readOnlyFields.each(function() {
            const isReadOnly = $(this).prop('readonly') || $(this).attr('readonly') !== undefined;
            if (!isReadOnly) {
                allReadOnly = false;
            }
        });
        
        return allReadOnly;
    },

    /**
     * Tests SIS badge display
     */
    testSisBadgeDisplay: function() {
        
        const badges = $('.sis-badge');
        if (badges.length === 0) {
            return true; // Not a failure if no badges
        }
        
        let allBadgesValid = true;
        badges.each(function() {
            const hasIcon = $(this).find('i.fas').length > 0;
            const hasText = $(this).text().trim().length > 0;
            const hasAriaLabel = $(this).attr('aria-label') !== undefined;
            
            if (!hasIcon || !hasText || !hasAriaLabel) {
                allBadgesValid = false;
            }
        });
        
        return allBadgesValid;
    },

    /**
     * Tests accessibility features
     */
    testAccessibilityFeatures: function() {
        
        const sisFields = $('.sis-readonly');
        if (sisFields.length === 0) {
            return true;
        }
        
        let accessibilityValid = true;
        sisFields.each(function() {
            const fieldName = $(this).attr('name') || $(this).attr('id');
            const ariaDescribedBy = $(this).attr('aria-describedby');
            const hasScreenReaderInfo = $(`#${fieldName}-sis-info`).length > 0;
            
            if (!ariaDescribedBy || !hasScreenReaderInfo) {
                accessibilityValid = false;
            }
        });
        
        return accessibilityValid;
    },

    /**
     * Tests data freshness indicators
     */
    testDataFreshnessIndicators: function() {
        
        const freshnessIndicators = $('.data-freshness');
        if (freshnessIndicators.length === 0) {
            return true; // Not a failure if no indicators
        }
        
        let indicatorsValid = true;
        freshnessIndicators.each(function() {
            const hasIcon = $(this).find('i').length > 0;
            const hasText = $(this).text().trim().length > 0;
            const hasValidClass = $(this).hasClass('fresh') || $(this).hasClass('stale') || $(this).hasClass('very-stale');
            
            if (!hasIcon || !hasText || !hasValidClass) {
                indicatorsValid = false;
            }
        });
        
        return indicatorsValid;
    },

    /**
     * Tests form validation
     */
    testFormValidation: function() {
        
        // Test that SIS validation is integrated
        if (typeof SharedForms !== 'undefined' && SharedForms.SisIntegration) {
            const validationResult = SharedForms.SisIntegration.validateReadOnlyFields();
            return validationResult;
        } else {
            return true; // Not a failure if not loaded
        }
    },

    /**
     * Tests mobile responsiveness
     */
    testMobileResponsiveness: function() {
        
        // Simulate mobile viewport
        const originalWidth = window.innerWidth;
        
        // Check if SIS elements are responsive
        const sisElements = $('.sis-badge, .sis-field-container, .data-freshness');
        if (sisElements.length === 0) {
            return true;
        }
        
        let responsiveValid = true;
        sisElements.each(function() {
            const element = $(this);
            const hasResponsiveClasses = element.attr('class').includes('sm:') || 
                                       element.attr('class').includes('md:') || 
                                       element.attr('class').includes('lg:');
            
            // Check if element is visible and properly sized
            const isVisible = element.is(':visible');
            const hasReasonableSize = element.outerWidth() > 0 && element.outerHeight() > 0;
            
            if (!isVisible || !hasReasonableSize) {
                responsiveValid = false;
            }
        });
        
        return responsiveValid;
    },

    /**
     * Tests SIS integration with mock data
     */
    testWithMockData: function() {
        
        const mockStudentData = {
            studentId: 'TEST123',
            fullNameEN: 'Test Student Name',
            nationalId: '123456789012',
            isKuwaiti: true,
            lastSyncDate: new Date()
        };
        
        // Test pre-filling
        if (typeof SisIntegration !== 'undefined' && SisIntegration.StudentLookup) {
            try {
                SisIntegration.StudentLookup.preFillForm(mockStudentData);
                
                // Verify fields were filled
                const nameField = $('#StudentName');
                const civilIdField = $('#StudentCivilId');
                
                if (nameField.val() === mockStudentData.fullNameEN && 
                    civilIdField.val() === mockStudentData.nationalId) {
                    return true;
                } else {
                    return false;
                }
            } catch (error) {
                return false;
            }
        } else {
            return true;
        }
    },

    /**
     * Generates a test report
     */
    generateReport: function() {
        const results = this.runAllTests();
        
        const report = {
            timestamp: new Date().toISOString(),
            results: results,
            environment: {
                userAgent: navigator.userAgent,
                viewport: `${window.innerWidth}x${window.innerHeight}`,
                url: window.location.href
            },
            sisFeatures: {
                hasSharedForms: typeof SharedForms !== 'undefined',
                hasSisIntegration: typeof SisIntegration !== 'undefined',
                sisFieldsCount: $('.sis-readonly, .sis-field-container input').length,
                sisBadgesCount: $('.sis-badge').length,
                dataFreshnessCount: $('.data-freshness').length
            }
        };
        
        return report;
    }
};

// ===== CONSOLE COMMANDS =====
// Make testing functions available in console for manual testing
if (typeof window !== 'undefined') {
    window.testSis = SisIntegrationTest.runAllTests.bind(SisIntegrationTest);
    window.testSisReport = SisIntegrationTest.generateReport.bind(SisIntegrationTest);
    window.testSisMock = SisIntegrationTest.testWithMockData.bind(SisIntegrationTest);
}