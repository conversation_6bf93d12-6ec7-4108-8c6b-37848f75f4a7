using Forms.ktech.Data;
using Forms.ktech.Shared.ViewModels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using Forms.ktech.Configuration;

namespace Forms.ktech.Services.SIS
{
    /// <summary>
    /// Implementation of student lookup service that queries local SIS student data
    /// Provides efficient lookup with caching and performance optimization
    /// </summary>
    public class StudentLookupService : IStudentLookupService
    {
        private readonly FormsKTechContext _context;
        private readonly IMemoryCache _cache;
        private readonly ILogger<StudentLookupService> _logger;
        private readonly SisApiOptions _sisOptions;

        // Cache configuration
        private static readonly TimeSpan DefaultCacheExpiry = TimeSpan.FromMinutes(15);
        private static readonly TimeSpan DataFreshnessCacheExpiry = TimeSpan.FromMinutes(5);
        private const string CacheKeyPrefix = "StudentLookup_";
        private const string StatsCacheKey = "StudentLookup_Stats";

        public StudentLookupService(
            FormsKTechContext context,
            IMemoryCache cache,
            ILogger<StudentLookupService> logger,
            IOptions<SisApiOptions> sisOptions)
        {
            _context = context;
            _cache = cache;
            _logger = logger;
            _sisOptions = sisOptions.Value;
        }

        #region Student Lookup Methods

        /// <summary>
        /// Gets a student by their email address (used for Azure AD matching)
        /// </summary>
        public async Task<SisStudent?> GetStudentByEmailAsync(string email, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(email))
            {
                _logger.LogWarning("GetStudentByEmailAsync called with null or empty email");
                return null;
            }

            var cacheKey = $"{CacheKeyPrefix}Email_{email.ToLowerInvariant()}";
            
            if (_cache.TryGetValue(cacheKey, out SisStudent? cachedStudent))
            {
                _logger.LogDebug("Student found in cache for email: {Email}", email);
                return cachedStudent;
            }

            try
            {
                var student = await _context.SisStudents
                    .AsNoTracking()
                    .FirstOrDefaultAsync(s => s.Email.ToLower() == email.ToLower(), cancellationToken);

                // Cache the result (including null results to avoid repeated database queries)
                _cache.Set(cacheKey, student, DefaultCacheExpiry);

                if (student != null)
                {
                    _logger.LogInformation("Student found by email: {Email}, StudentID: {StudentId}", 
                        email, student.StudentID);
                }
                else
                {
                    _logger.LogInformation("No student found for email: {Email}", email);
                }

                return student;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error looking up student by email: {Email}", email);
                throw;
            }
        }

        /// <summary>
        /// Gets a student by their Student ID from SIS
        /// </summary>
        public async Task<SisStudent?> GetStudentByIdAsync(string studentId, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(studentId))
            {
                _logger.LogWarning("GetStudentByIdAsync called with null or empty studentId");
                return null;
            }

            var cacheKey = $"{CacheKeyPrefix}StudentId_{studentId}";
            
            if (_cache.TryGetValue(cacheKey, out SisStudent? cachedStudent))
            {
                _logger.LogDebug("Student found in cache for StudentID: {StudentId}", studentId);
                return cachedStudent;
            }

            try
            {
                var student = await _context.SisStudents
                    .AsNoTracking()
                    .FirstOrDefaultAsync(s => s.StudentID == studentId, cancellationToken);

                _cache.Set(cacheKey, student, DefaultCacheExpiry);

                if (student != null)
                {
                    _logger.LogInformation("Student found by StudentID: {StudentId}, Email: {Email}", 
                        studentId, student.Email);
                }
                else
                {
                    _logger.LogInformation("No student found for StudentID: {StudentId}", studentId);
                }

                return student;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error looking up student by StudentID: {StudentId}", studentId);
                throw;
            }
        }

        /// <summary>
        /// Gets a student by their National ID (Civil ID)
        /// </summary>
        public async Task<SisStudent?> GetStudentByNationalIdAsync(string nationalId, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(nationalId))
            {
                _logger.LogWarning("GetStudentByNationalIdAsync called with null or empty nationalId");
                return null;
            }

            var cacheKey = $"{CacheKeyPrefix}NationalId_{nationalId}";
            
            if (_cache.TryGetValue(cacheKey, out SisStudent? cachedStudent))
            {
                _logger.LogDebug("Student found in cache for NationalID: {NationalId}", nationalId);
                return cachedStudent;
            }

            try
            {
                var student = await _context.SisStudents
                    .AsNoTracking()
                    .FirstOrDefaultAsync(s => s.NationalID == nationalId, cancellationToken);

                _cache.Set(cacheKey, student, DefaultCacheExpiry);

                if (student != null)
                {
                    _logger.LogInformation("Student found by NationalID: {NationalId}, StudentID: {StudentId}", 
                        nationalId, student.StudentID);
                }
                else
                {
                    _logger.LogInformation("No student found for NationalID: {NationalId}", nationalId);
                }

                return student;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error looking up student by NationalID: {NationalId}", nationalId);
                throw;
            }
        }

        /// <summary>
        /// Searches for students by name pattern (supports partial matching)
        /// </summary>
        public async Task<IEnumerable<SisStudent>> SearchStudentsByNameAsync(string namePattern, int maxResults = 10, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(namePattern))
            {
                _logger.LogWarning("SearchStudentsByNameAsync called with null or empty namePattern");
                return Enumerable.Empty<SisStudent>();
            }

            if (maxResults <= 0 || maxResults > 100)
            {
                maxResults = 10; // Default to reasonable limit
            }

            var cacheKey = $"{CacheKeyPrefix}NameSearch_{namePattern.ToLowerInvariant()}_{maxResults}";
            
            if (_cache.TryGetValue(cacheKey, out IEnumerable<SisStudent>? cachedResults))
            {
                _logger.LogDebug("Name search results found in cache for pattern: {NamePattern}", namePattern);
                return cachedResults!;
            }

            try
            {
                var searchPattern = namePattern.ToLower();
                var students = await _context.SisStudents
                    .AsNoTracking()
                    .Where(s => s.FullNameEN.ToLower().Contains(searchPattern) || 
                               (s.FullNameAR != null && s.FullNameAR.ToLower().Contains(searchPattern)))
                    .OrderBy(s => s.FullNameEN)
                    .Take(maxResults)
                    .ToListAsync(cancellationToken);

                // Cache for shorter time since search results can be large
                _cache.Set(cacheKey, students, TimeSpan.FromMinutes(5));

                _logger.LogInformation("Name search completed for pattern: {NamePattern}, Found: {Count} students", 
                    namePattern, students.Count);

                return students;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching students by name pattern: {NamePattern}", namePattern);
                throw;
            }
        }

        /// <summary>
        /// Gets multiple students by their email addresses (batch lookup)
        /// </summary>
        public async Task<Dictionary<string, SisStudent?>> GetStudentsByEmailsAsync(IEnumerable<string> emails, CancellationToken cancellationToken = default)
        {
            var emailList = emails?.Where(e => !string.IsNullOrWhiteSpace(e)).ToList();
            if (emailList == null || !emailList.Any())
            {
                _logger.LogWarning("GetStudentsByEmailsAsync called with null or empty email list");
                return new Dictionary<string, SisStudent?>();
            }

            var result = new Dictionary<string, SisStudent?>();
            var emailsToQuery = new List<string>();

            // Check cache first
            foreach (var email in emailList)
            {
                var cacheKey = $"{CacheKeyPrefix}Email_{email.ToLowerInvariant()}";
                if (_cache.TryGetValue(cacheKey, out SisStudent? cachedStudent))
                {
                    result[email] = cachedStudent;
                }
                else
                {
                    emailsToQuery.Add(email);
                }
            }

            // Query database for emails not in cache
            if (emailsToQuery.Any())
            {
                try
                {
                    var lowerEmails = emailsToQuery.Select(e => e.ToLower()).ToList();
                    var students = await _context.SisStudents
                        .AsNoTracking()
                        .Where(s => lowerEmails.Contains(s.Email.ToLower()))
                        .ToListAsync(cancellationToken);

                    // Map results and update cache
                    foreach (var email in emailsToQuery)
                    {
                        var student = students.FirstOrDefault(s => 
                            string.Equals(s.Email, email, StringComparison.OrdinalIgnoreCase));
                        
                        result[email] = student;
                        
                        var cacheKey = $"{CacheKeyPrefix}Email_{email.ToLowerInvariant()}";
                        _cache.Set(cacheKey, student, DefaultCacheExpiry);
                    }

                    _logger.LogInformation("Batch lookup completed for {RequestedCount} emails, found {FoundCount} students", 
                        emailList.Count, students.Count);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in batch lookup for emails");
                    throw;
                }
            }

            return result;
        }

        #endregion

        #region Data Freshness and Statistics

        /// <summary>
        /// Checks if the student data is fresh (within acceptable age)
        /// </summary>
        public async Task<bool> IsDataFreshAsync(string email, TimeSpan maxAge, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(email))
            {
                return false;
            }

            try
            {
                var student = await GetStudentByEmailAsync(email, cancellationToken);
                if (student == null)
                {
                    return false;
                }

                var dataAge = DateTime.UtcNow - student.LastSyncDate;
                var isFresh = dataAge <= maxAge;

                _logger.LogDebug("Data freshness check for {Email}: Age={DataAge}, MaxAge={MaxAge}, Fresh={IsFresh}", 
                    email, dataAge, maxAge, isFresh);

                return isFresh;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking data freshness for email: {Email}", email);
                return false;
            }
        }

        /// <summary>
        /// Gets the total number of students in the local database
        /// </summary>
        public async Task<int> GetTotalStudentCountAsync(CancellationToken cancellationToken = default)
        {
            const string cacheKey = $"{CacheKeyPrefix}TotalCount";
            
            if (_cache.TryGetValue(cacheKey, out int cachedCount))
            {
                return cachedCount;
            }

            try
            {
                var count = await _context.SisStudents.CountAsync(cancellationToken);
                _cache.Set(cacheKey, count, DataFreshnessCacheExpiry);
                
                _logger.LogDebug("Total student count: {Count}", count);
                return count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting total student count");
                throw;
            }
        }

        /// <summary>
        /// Gets the date of the last successful sync operation
        /// </summary>
        public async Task<DateTime?> GetLastSyncDateAsync(CancellationToken cancellationToken = default)
        {
            const string cacheKey = $"{CacheKeyPrefix}LastSyncDate";

            if (_cache.TryGetValue(cacheKey, out DateTime? cachedDate))
            {
                return cachedDate;
            }

            try
            {
                var lastSyncDate = await _context.SyncHistories
                    .Where(h => h.Status == SyncStatus.Completed)
                    .OrderByDescending(h => h.EndTime)
                    .Select(h => h.EndTime)
                    .FirstOrDefaultAsync(cancellationToken);

                _cache.Set(cacheKey, lastSyncDate, DataFreshnessCacheExpiry);

                _logger.LogDebug("Last sync date: {LastSyncDate}", lastSyncDate);
                return lastSyncDate;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting last sync date");
                throw;
            }
        }

        /// <summary>
        /// Gets data freshness statistics for monitoring
        /// </summary>
        public async Task<DataFreshnessStats> GetDataFreshnessStatsAsync(CancellationToken cancellationToken = default)
        {
            if (_cache.TryGetValue(StatsCacheKey, out DataFreshnessStats? cachedStats))
            {
                return cachedStats!;
            }

            try
            {
                var totalStudents = await GetTotalStudentCountAsync(cancellationToken);
                var lastSyncDate = await GetLastSyncDateAsync(cancellationToken);

                var stats = new DataFreshnessStats
                {
                    TotalStudents = totalStudents,
                    LastSyncDate = lastSyncDate
                };

                if (lastSyncDate.HasValue)
                {
                    stats.DataAge = DateTime.UtcNow - lastSyncDate.Value;

                    // Calculate freshness based on configured data freshness hours
                    var freshnessThreshold = TimeSpan.FromHours(_sisOptions.DataFreshnessHours);
                    var cutoffDate = DateTime.UtcNow - freshnessThreshold;

                    stats.StudentsWithFreshData = await _context.SisStudents
                        .CountAsync(s => s.LastSyncDate >= cutoffDate, cancellationToken);

                    stats.StudentsWithStaleData = totalStudents - stats.StudentsWithFreshData;
                    stats.FreshDataPercentage = totalStudents > 0
                        ? (double)stats.StudentsWithFreshData / totalStudents * 100
                        : 0;
                }

                _cache.Set(StatsCacheKey, stats, DataFreshnessCacheExpiry);

                _logger.LogDebug("Data freshness stats calculated: {FreshPercentage:F1}% fresh data",
                    stats.FreshDataPercentage);

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating data freshness stats");
                throw;
            }
        }

        #endregion

        #region Form Pre-filling

        /// <summary>
        /// Pre-fills a form ViewModel with SIS student data using Azure AD email
        /// Uses student ID extraction from email for more reliable matching
        /// </summary>
        public async Task<T> PreFillFormAsync<T>(T viewModel, string userEmail, CancellationToken cancellationToken = default)
            where T : IFormViewModel, ISisPreFillable
        {
            if (string.IsNullOrWhiteSpace(userEmail))
            {
                _logger.LogWarning("PreFillFormAsync called with null or empty userEmail");
                return viewModel;
            }

            try
            {
                SisStudent? student = null;

                // First, try to extract student ID from Azure AD email format (<EMAIL>)
                var extractedStudentId = ExtractStudentIdFromEmail(userEmail);
                if (!string.IsNullOrWhiteSpace(extractedStudentId))
                {
                    _logger.LogInformation("Extracted student ID '{StudentId}' from Azure AD email: {Email}",
                        extractedStudentId, userEmail);

                    student = await GetStudentByIdAsync(extractedStudentId, cancellationToken);
                    if (student != null)
                    {
                        _logger.LogInformation("Student found using extracted ID: {StudentId} from email: {Email}",
                            extractedStudentId, userEmail);
                    }
                    else
                    {
                        _logger.LogInformation("No student found for extracted ID: {StudentId} from email: {Email}",
                            extractedStudentId, userEmail);
                    }
                }

                // Fallback to email-based lookup if student ID extraction failed or no match found
                if (student == null)
                {
                    _logger.LogInformation("Falling back to email-based lookup for: {Email}", userEmail);
                    student = await GetStudentByEmailAsync(userEmail, cancellationToken);

                    if (student != null)
                    {
                        _logger.LogInformation("Student found using email fallback: {Email} -> StudentID: {StudentId}",
                            userEmail, student.StudentID);
                    }
                    else
                    {
                        _logger.LogInformation("No student found using email fallback for: {Email}", userEmail);
                    }
                }

                if (student == null)
                {
                    _logger.LogInformation("No SIS student found for email: {Email}, form will not be pre-filled", userEmail);
                    return viewModel;
                }

                return await PreFillFormWithStudentAsync(viewModel, student, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error pre-filling form for user email: {Email}", userEmail);
                // Return original viewModel on error to allow manual entry
                return viewModel;
            }
        }

        /// <summary>
        /// Extracts student ID from Azure AD email format (<EMAIL>)
        /// </summary>
        /// <param name="email">Azure AD email address</param>
        /// <returns>Student ID if extraction successful, null otherwise</returns>
        private string? ExtractStudentIdFromEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return null;

            try
            {
                // Check if email matches the expected Azure AD format: <EMAIL>
                if (email.EndsWith("@ktech.edu.kw", StringComparison.OrdinalIgnoreCase))
                {
                    var studentId = email.Substring(0, email.LastIndexOf('@'));

                    // Validate that the extracted part looks like a student ID (numeric)
                    if (!string.IsNullOrWhiteSpace(studentId) && studentId.All(char.IsDigit) && studentId.Length >= 6)
                    {
                        _logger.LogDebug("Successfully extracted student ID '{StudentId}' from email: {Email}",
                            studentId, email);
                        return studentId;
                    }
                    else
                    {
                        _logger.LogDebug("Extracted part '{StudentId}' from email {Email} does not match student ID pattern",
                            studentId, email);
                    }
                }
                else
                {
                    _logger.LogDebug("Email {Email} does not match Azure AD format (@ktech.edu.kw)", email);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error extracting student ID from email: {Email}", email);
            }

            return null;
        }

        /// <summary>
        /// Pre-fills a form ViewModel with specific student data
        /// </summary>
        public Task<T> PreFillFormWithStudentAsync<T>(T viewModel, SisStudent student, CancellationToken cancellationToken = default)
            where T : IFormViewModel, ISisPreFillable
        {
            if (student == null)
            {
                _logger.LogWarning("PreFillFormWithStudentAsync called with null student");
                return Task.FromResult(viewModel);
            }

            try
            {
                // Map SIS data to the ViewModel
                viewModel.MapFromSisStudent(student);

                // Set pre-fill metadata
                viewModel.IsPreFilled = true;
                viewModel.PreFilledFromStudentId = student.StudentID;
                viewModel.PreFilledAt = DateTime.UtcNow;

                _logger.LogInformation("Form pre-filled successfully for student: {StudentId} ({Email})",
                    student.StudentID, student.Email);

                return Task.FromResult(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error pre-filling form with student data: {StudentId}", student.StudentID);
                // Return original viewModel on error
                return Task.FromResult(viewModel);
            }
        }

        /// <summary>
        /// Validates that pre-filled data is consistent with current SIS data
        /// </summary>
        public async Task<PreFillValidationResult> ValidatePreFilledDataAsync<T>(T viewModel, CancellationToken cancellationToken = default)
            where T : IFormViewModel, ISisPreFillable
        {
            var result = new PreFillValidationResult { IsValid = true };

            if (!viewModel.IsPreFilled || string.IsNullOrEmpty(viewModel.PreFilledFromStudentId))
            {
                result.Warnings.Add("Form was not pre-filled from SIS data");
                return result;
            }

            try
            {
                // Get current student data
                var currentStudent = await GetStudentByIdAsync(viewModel.PreFilledFromStudentId, cancellationToken);
                if (currentStudent == null)
                {
                    result.IsValid = false;
                    result.Errors.Add("Student record no longer exists in SIS database");
                    return result;
                }

                // Check data freshness
                var lastSyncDate = await GetLastSyncDateAsync(cancellationToken);
                result.LastSyncDate = lastSyncDate;

                if (lastSyncDate.HasValue)
                {
                    var dataAge = DateTime.UtcNow - lastSyncDate.Value;
                    var freshnessThreshold = TimeSpan.FromHours(_sisOptions.DataFreshnessHours);
                    result.DataIsStale = dataAge > freshnessThreshold;

                    if (result.DataIsStale)
                    {
                        result.Warnings.Add($"SIS data is {dataAge.TotalHours:F1} hours old and may be stale");
                    }
                }

                // Check if student data has changed since pre-fill
                if (viewModel.PreFilledAt.HasValue && currentStudent.LastSyncDate > viewModel.PreFilledAt.Value)
                {
                    result.Warnings.Add("Student data has been updated since form was pre-filled");
                }

                _logger.LogDebug("Pre-fill validation completed for student: {StudentId}, Valid: {IsValid}, Warnings: {WarningCount}",
                    currentStudent.StudentID, result.IsValid, result.Warnings.Count);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating pre-filled data for student: {StudentId}", viewModel.PreFilledFromStudentId);
                result.IsValid = false;
                result.Errors.Add("Error occurred while validating pre-filled data");
                return result;
            }
        }

        #endregion
    }
}
