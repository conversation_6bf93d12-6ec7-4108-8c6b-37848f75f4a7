﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Forms.ktech.Migrations
{
    /// <inheritdoc />
    public partial class AddSubmissionStatusTracking : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "ApprovedDate",
                table: "StudentInfos",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FlagNotes",
                table: "StudentInfos",
                type: "nvarchar(2000)",
                maxLength: 2000,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "FlaggedDate",
                table: "StudentInfos",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "RejectedDate",
                table: "StudentInfos",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RejectionReason",
                table: "StudentInfos",
                type: "nvarchar(1000)",
                maxLength: 1000,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Status",
                table: "StudentInfos",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "StatusChangedByUserId",
                table: "StudentInfos",
                type: "nvarchar(450)",
                maxLength: 450,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "StatusChangedDate",
                table: "StudentInfos",
                type: "datetime2",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ApprovedDate",
                table: "StudentInfos");

            migrationBuilder.DropColumn(
                name: "FlagNotes",
                table: "StudentInfos");

            migrationBuilder.DropColumn(
                name: "FlaggedDate",
                table: "StudentInfos");

            migrationBuilder.DropColumn(
                name: "RejectedDate",
                table: "StudentInfos");

            migrationBuilder.DropColumn(
                name: "RejectionReason",
                table: "StudentInfos");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "StudentInfos");

            migrationBuilder.DropColumn(
                name: "StatusChangedByUserId",
                table: "StudentInfos");

            migrationBuilder.DropColumn(
                name: "StatusChangedDate",
                table: "StudentInfos");
        }
    }
}
