{"ConnectionStrings": {"DefaultConnection": "Server=.;Database=FormsKTechDb;User Id=dbuser; Password=********; TrustServerCertificate=True;"}, "AzureAd": {"Instance": "https://login.microsoftonline.com/", "Domain": "ktech.edu.kw", "TenantId": "e536d3ca-d4dd-4dcc-9f83-58add1d31431", "ClientId": "4ec9a798-2ba2-4f75-96ee-e7ec4c0887b8", "CallbackPath": "/signin-oidc"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Email": {"SmtpHost": "smtp.office365.com", "SmtpPort": "587", "SmtpUsername": "<EMAIL>", "SmtpPassword": "Later2Know@ktech", "FromEmail": "<EMAIL>", "FromName": "KTECH Forms System", "TemplatePath": "wwwroot/Template/Email-Template.html", "MaxEmailsPerHour": 100, "EnableEmailQueue": true}, "Application": {"BaseUrl": "https://localhost:7045"}, "SisApi": {"BaseUrl": "https://sisapi.ktech.edu.kw/api", "Username": "UbHmHOf5MCiEv1sbimDE", "Password": "puSU7xxOBYV6CaIINSY1", "TimeoutSeconds": 30, "RetryAttempts": 3, "RetryDelayMs": 1000, "BatchSize": 100, "MaxConcurrentRequests": 5, "AutoSyncEnabled": true, "SyncSchedule": "0 2 * * *", "DataFreshnessHours": 24, "MaxSyncHistoryDays": 90, "EnableIncrementalSync": true, "CircuitBreakerFailureThreshold": 5, "CircuitBreakerTimeoutSeconds": 60, "CircuitBreakerSuccessThreshold": 3, "StudentsEndpoint": "/Students", "StudentsFilterEndpoint": "/Students/filter", "EnrolledUsersEndpoint": "/EnrolledUsers", "EnrolledUsersFilterEndpoint": "/EnrolledUsers/filter"}}