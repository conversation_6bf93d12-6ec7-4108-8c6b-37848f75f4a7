@model IEnumerable<Forms.ktech.Data.SyncHistory>

@{
    ViewData["Title"] = "SIS Sync History";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@section Breadcrumb {
    <li class="inline-flex items-center">
        <a asp-area="" asp-controller="Home" asp-action="Index" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
            <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
            </svg>
            Home
        </a>
    </li>
    <li>
        <div class="flex items-center">
            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
            </svg>
            <a asp-controller="Admin" asp-action="Dashboard" class="ms-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ms-2 dark:text-gray-400 dark:hover:text-white">Admin</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
            </svg>
            <a asp-controller="Admin" asp-action="SyncDashboard" class="ms-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ms-2 dark:text-gray-400 dark:hover:text-white">SIS Sync</a>
        </div>
    </li>
    <li aria-current="page">
        <div class="flex items-center">
            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
            </svg>
            <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400">Sync History</span>
        </div>
    </li>
}

<!-- Header Section -->
<div class="bg-white rounded-lg shadow-lg p-6 mb-6 dark:bg-gray-800">
    <div class="flex items-center justify-between">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-12 w-12 rounded-md bg-purple-500 text-white">
                    <i class="fas fa-history text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">SIS Sync History</h1>
                <p class="text-gray-600 dark:text-gray-300">View detailed history of all synchronization operations</p>
            </div>
        </div>
        <div class="flex space-x-3">
            <a asp-controller="Admin" asp-action="SyncDashboard" class="bg-blue-700 hover:bg-blue-800 text-white font-medium py-2 px-4 rounded-lg focus:ring-4 focus:ring-blue-300 dark:focus:ring-blue-600">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    </div>
</div>

<!-- Statistics Summary -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
    <!-- Total Syncs -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-10 w-10 rounded-md bg-blue-500 text-white">
                    <i class="fas fa-list"></i>
                </div>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Syncs</p>
                <p class="text-xl font-bold text-gray-900 dark:text-white">@ViewBag.TotalCount</p>
            </div>
        </div>
    </div>

    <!-- Successful Syncs -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-10 w-10 rounded-md bg-green-500 text-white">
                    <i class="fas fa-check"></i>
                </div>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Successful</p>
                <p class="text-xl font-bold text-gray-900 dark:text-white">
                    @Model.Count(s => s.Status == Forms.ktech.Data.SyncStatus.Completed)
                </p>
            </div>
        </div>
    </div>

    <!-- Failed Syncs -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-10 w-10 rounded-md bg-red-500 text-white">
                    <i class="fas fa-times"></i>
                </div>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Failed</p>
                <p class="text-xl font-bold text-gray-900 dark:text-white">
                    @Model.Count(s => s.Status == Forms.ktech.Data.SyncStatus.Failed)
                </p>
            </div>
        </div>
    </div>

    <!-- In Progress -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-10 w-10 rounded-md bg-yellow-500 text-white">
                    <i class="fas fa-spinner"></i>
                </div>
            </div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">In Progress</p>
                <p class="text-xl font-bold text-gray-900 dark:text-white">
                    @Model.Count(s => s.Status == Forms.ktech.Data.SyncStatus.InProgress)
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Sync History Table -->
<div class="bg-white rounded-lg shadow-lg dark:bg-gray-800">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Synchronization History</h2>
            <div class="flex items-center space-x-4">
                <!-- Filter Options -->
                <select id="statusFilter" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option value="">All Status</option>
                    <option value="Completed">Completed</option>
                    <option value="Failed">Failed</option>
                    <option value="InProgress">In Progress</option>
                    <option value="Cancelled">Cancelled</option>
                </select>
                <select id="typeFilter" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500">
                    <option value="">All Types</option>
                    <option value="Full">Full Sync</option>
                    <option value="Incremental">Incremental Sync</option>
                </select>
            </div>
        </div>
    </div>
    <div class="overflow-x-auto">
        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="px-6 py-3">Sync ID</th>
                    <th scope="col" class="px-6 py-3">Type</th>
                    <th scope="col" class="px-6 py-3">Status</th>
                    <th scope="col" class="px-6 py-3">Start Time</th>
                    <th scope="col" class="px-6 py-3">Duration</th>
                    <th scope="col" class="px-6 py-3">Records</th>
                    <th scope="col" class="px-6 py-3">Progress</th>
                    <th scope="col" class="px-6 py-3">Triggered By</th>
                    <th scope="col" class="px-6 py-3">Actions</th>
                </tr>
            </thead>
            <tbody>
                @if (Model.Any())
                {
                    foreach (var sync in Model)
                    {
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600" 
                            data-status="@sync.Status" data-type="@sync.SyncType">
                            <td class="px-6 py-4 font-mono text-xs">
                                @sync.SyncId.ToString()[..8]...
                            </td>
                            <td class="px-6 py-4">
                                <span class="bg-@(sync.SyncType == Forms.ktech.Data.SyncType.Full ? "blue" : "green")-100 text-@(sync.SyncType == Forms.ktech.Data.SyncType.Full ? "blue" : "green")-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-@(sync.SyncType == Forms.ktech.Data.SyncType.Full ? "blue" : "green")-900 dark:text-@(sync.SyncType == Forms.ktech.Data.SyncType.Full ? "blue" : "green")-300">
                                    @sync.SyncType
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                @{
                                    var statusColor = sync.Status switch
                                    {
                                        Forms.ktech.Data.SyncStatus.Completed => "green",
                                        Forms.ktech.Data.SyncStatus.Failed => "red",
                                        Forms.ktech.Data.SyncStatus.InProgress => "yellow",
                                        Forms.ktech.Data.SyncStatus.Cancelled => "gray",
                                        _ => "gray"
                                    };
                                    var statusIcon = sync.Status switch
                                    {
                                        Forms.ktech.Data.SyncStatus.Completed => "fas fa-check",
                                        Forms.ktech.Data.SyncStatus.Failed => "fas fa-times",
                                        Forms.ktech.Data.SyncStatus.InProgress => "fas fa-spinner fa-spin",
                                        Forms.ktech.Data.SyncStatus.Cancelled => "fas fa-ban",
                                        _ => "fas fa-question"
                                    };
                                }
                                <span class="bg-@(statusColor)-100 text-@(statusColor)-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-@(statusColor)-900 dark:text-@(statusColor)-300">
                                    <i class="@statusIcon me-1"></i>@sync.Status
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm">
                                    <div class="font-medium">@sync.StartTime.ToString("MMM dd, yyyy")</div>
                                    <div class="text-gray-500">@sync.StartTime.ToString("HH:mm:ss")</div>
                                </div>
                            </td>
                            <td class="px-6 py-4">@sync.GetDurationString()</td>
                            <td class="px-6 py-4">
                                <div class="text-sm">
                                    <div class="font-medium">@sync.RecordsProcessed total</div>
                                    @if (sync.RecordsAdded > 0 || sync.RecordsUpdated > 0)
                                    {
                                        <div class="text-gray-500">
                                            +@sync.RecordsAdded, ~@sync.RecordsUpdated
                                        </div>
                                    }
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                                    <div class="bg-@(statusColor)-600 h-2.5 rounded-full" style="width: @(sync.ProgressPercentage)%"></div>
                                </div>
                                <span class="text-xs text-gray-500 dark:text-gray-400">@(sync.ProgressPercentage)%</span>
                                @if (!string.IsNullOrEmpty(sync.CurrentStep))
                                {
                                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">@sync.CurrentStep</div>
                                }
                            </td>
                            <td class="px-6 py-4">
                                @if (!string.IsNullOrEmpty(sync.TriggeredByUserId))
                                {
                                    <span class="text-sm">@sync.TriggeredByUserId</span>
                                    <div class="text-xs text-gray-500">Manual</div>
                                }
                                else
                                {
                                    <span class="text-sm text-gray-500">System</span>
                                    <div class="text-xs text-gray-500">Automated</div>
                                }
                            </td>
                            <td class="px-6 py-4">
                                <button class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium text-sm"
                                        onclick="showSyncDetails('@sync.SyncId')">
                                    <i class="fas fa-eye me-1"></i>Details
                                </button>
                                @if (!string.IsNullOrEmpty(sync.ErrorMessage))
                                {
                                    <button class="ml-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 font-medium text-sm"
                                            onclick="showErrorDetails('@sync.SyncId', '@Html.Raw(Html.Encode(sync.ErrorMessage))', '@Html.Raw(Html.Encode(sync.ErrorDetails ?? ""))')">
                                        <i class="fas fa-exclamation-triangle me-1"></i>Error
                                    </button>
                                }
                            </td>
                        </tr>
                    }
                }
                else
                {
                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                        <td colspan="9" class="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                            <div class="flex flex-col items-center">
                                <i class="fas fa-history text-4xl mb-4 text-gray-300"></i>
                                <p class="text-lg font-medium">No sync operations found</p>
                                <p class="text-sm">Sync operations will appear here once they are performed</p>
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
</div>

<!-- Pagination -->
@if (ViewBag.TotalPages > 1)
{
    <div class="flex items-center justify-between mt-6">
        <div class="text-sm text-gray-700 dark:text-gray-400">
            Showing page @ViewBag.CurrentPage of @ViewBag.TotalPages (@ViewBag.TotalCount total records)
        </div>
        <nav aria-label="Page navigation">
            <ul class="inline-flex -space-x-px text-sm">
                @if (ViewBag.CurrentPage > 1)
                {
                    <li>
                        <a asp-action="SyncHistory" asp-route-page="@(ViewBag.CurrentPage - 1)" asp-route-pageSize="@ViewBag.PageSize" 
                           class="flex items-center justify-center px-3 h-8 ml-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            Previous
                        </a>
                    </li>
                }
                
                @for (int i = Math.Max(1, ViewBag.CurrentPage - 2); i <= Math.Min(ViewBag.TotalPages, ViewBag.CurrentPage + 2); i++)
                {
                    <li>
                        <a asp-action="SyncHistory" asp-route-page="@i" asp-route-pageSize="@ViewBag.PageSize" 
                           class="flex items-center justify-center px-3 h-8 leading-tight @(i == ViewBag.CurrentPage ? "text-blue-600 border border-gray-300 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 dark:border-gray-700 dark:bg-gray-700 dark:text-white" : "text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white")">
                            @i
                        </a>
                    </li>
                }
                
                @if (ViewBag.CurrentPage < ViewBag.TotalPages)
                {
                    <li>
                        <a asp-action="SyncHistory" asp-route-page="@(ViewBag.CurrentPage + 1)" asp-route-pageSize="@ViewBag.PageSize" 
                           class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                            Next
                        </a>
                    </li>
                }
            </ul>
        </nav>
    </div>
}

<!-- Sync Details Modal -->
<div id="syncDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden" style="z-index: 1000;">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Sync Operation Details</h3>
                <button onclick="closeSyncDetailsModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="syncDetailsContent" class="space-y-4">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Error Details Modal -->
<div id="errorDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden" style="z-index: 1000;">
    <div class="relative top-10 mx-auto p-5 border w-11/12 max-w-3xl shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Error Details</h3>
                <button onclick="closeErrorDetailsModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="errorDetailsContent" class="space-y-4">
                <!-- Content will be populated by JavaScript -->
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Filter functionality
        document.getElementById('statusFilter').addEventListener('change', function() {
            filterTable();
        });

        document.getElementById('typeFilter').addEventListener('change', function() {
            filterTable();
        });

        function filterTable() {
            const statusFilter = document.getElementById('statusFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            const rows = document.querySelectorAll('tbody tr[data-status]');

            rows.forEach(row => {
                const status = row.getAttribute('data-status');
                const type = row.getAttribute('data-type');

                const statusMatch = !statusFilter || status === statusFilter;
                const typeMatch = !typeFilter || type === typeFilter;

                if (statusMatch && typeMatch) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // Modal functions
        function showSyncDetails(syncId) {
            // In a real implementation, you would fetch detailed sync information
            // For now, we'll show a placeholder
            const content = `
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <h4 class="font-medium text-gray-900 dark:text-white mb-2">Sync ID: ${syncId}</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        Detailed sync information would be displayed here, including:
                    </p>
                    <ul class="mt-2 text-sm text-gray-600 dark:text-gray-400 list-disc list-inside">
                        <li>Complete sync timeline</li>
                        <li>Detailed record processing information</li>
                        <li>API call logs</li>
                        <li>Performance metrics</li>
                        <li>Configuration used</li>
                    </ul>
                </div>
            `;

            document.getElementById('syncDetailsContent').innerHTML = content;
            document.getElementById('syncDetailsModal').classList.remove('hidden');
        }

        function closeSyncDetailsModal() {
            document.getElementById('syncDetailsModal').classList.add('hidden');
        }

        function showErrorDetails(syncId, errorMessage, errorDetails) {
            const content = `
                <div class="space-y-4">
                    <div class="bg-red-50 dark:bg-red-900 rounded-lg p-4">
                        <h4 class="font-medium text-red-900 dark:text-red-100 mb-2">
                            <i class="fas fa-exclamation-triangle me-2"></i>Error Message
                        </h4>
                        <p class="text-sm text-red-800 dark:text-red-200">${errorMessage}</p>
                    </div>
                    ${errorDetails ? `
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="font-medium text-gray-900 dark:text-white mb-2">
                            <i class="fas fa-info-circle me-2"></i>Error Details
                        </h4>
                        <pre class="text-xs text-gray-600 dark:text-gray-400 whitespace-pre-wrap overflow-x-auto">${errorDetails}</pre>
                    </div>
                    ` : ''}
                    <div class="bg-blue-50 dark:bg-blue-900 rounded-lg p-4">
                        <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-2">
                            <i class="fas fa-lightbulb me-2"></i>Troubleshooting Tips
                        </h4>
                        <ul class="text-sm text-blue-800 dark:text-blue-200 list-disc list-inside space-y-1">
                            <li>Check SIS API connectivity and credentials</li>
                            <li>Verify network connectivity to SIS endpoints</li>
                            <li>Review application logs for additional context</li>
                            <li>Contact system administrator if issue persists</li>
                        </ul>
                    </div>
                </div>
            `;

            document.getElementById('errorDetailsContent').innerHTML = content;
            document.getElementById('errorDetailsModal').classList.remove('hidden');
        }

        function closeErrorDetailsModal() {
            document.getElementById('errorDetailsModal').classList.add('hidden');
        }

        // Close modals when clicking outside
        document.getElementById('syncDetailsModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeSyncDetailsModal();
            }
        });

        document.getElementById('errorDetailsModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeErrorDetailsModal();
            }
        });

        // Auto-refresh for in-progress syncs
        function checkForInProgressSyncs() {
            const inProgressRows = document.querySelectorAll('tr[data-status="InProgress"]');
            if (inProgressRows.length > 0) {
                // Refresh the page every 5 seconds if there are in-progress syncs
                setTimeout(() => {
                    window.location.reload();
                }, 5000);
            }
        }

        // Check for in-progress syncs on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkForInProgressSyncs();
        });
    </script>
}
