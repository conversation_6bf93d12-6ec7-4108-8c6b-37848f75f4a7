using Microsoft.EntityFrameworkCore;
using Forms.ktech.Data;
using Forms.ktech.Shared.ViewModels;

namespace Forms.ktech.Services
{
    /// <summary>
    /// Generic base implementation for form handlers
    /// Provides common functionality for all forms while allowing specialization
    /// </summary>
    /// <typeparam name="TViewModel">The ViewModel type for the form</typeparam>
    /// <typeparam name="TEntity">The Entity type for the form</typeparam>
    public abstract class FormHandler<TViewModel, TEntity> : IFormHandler<TViewModel, TEntity>
        where TViewModel : class, IFormViewModel
        where TEntity : BaseEntity, new()
    {
        protected readonly FormsKTechContext _context;
        protected readonly IFileUploadService _fileUploadService;
        protected readonly ILogger<FormHandler<TViewModel, TEntity>> _logger;

        protected FormHandler(
            FormsKTechContext context,
            IFileUploadService fileUploadService,
            ILogger<FormHandler<TViewModel, TEntity>> logger)
        {
            _context = context;
            _fileUploadService = fileUploadService;
            _logger = logger;
        }

        #region Abstract Methods (Must be implemented by derived classes)

        /// <summary>
        /// Maps a ViewModel to an Entity - must be implemented by derived classes
        /// </summary>
        /// <param name="viewModel">The ViewModel to map</param>
        /// <param name="entity">The existing entity to update, or null to create new</param>
        /// <returns>The mapped entity</returns>
        public abstract TEntity MapToEntity(TViewModel viewModel, TEntity? entity = null);

        /// <summary>
        /// Maps an Entity to a ViewModel - must be implemented by derived classes
        /// </summary>
        /// <param name="entity">The entity to map</param>
        /// <returns>The mapped ViewModel</returns>
        public abstract TViewModel MapToViewModel(TEntity entity);

        #endregion

        #region Virtual Methods (Can be overridden by derived classes)

        /// <summary>
        /// Checks if the form submission should be rejected based on business rules
        /// Default implementation checks if ViewModel implements IFormViewModel.IsEligible()
        /// Override this method for custom eligibility logic
        /// </summary>
        /// <param name="viewModel">The form ViewModel to validate</param>
        /// <returns>True if the submission should be rejected, false if it can proceed</returns>
        public virtual bool IsNotEligible(TViewModel viewModel)
        {
            if (viewModel is IFormViewModel formViewModel)
            {
                return !formViewModel.IsEligible();
            }
            return false; // Default to eligible if no eligibility logic is implemented
        }

        /// <summary>
        /// Gets the reason why a form submission is not eligible
        /// Default implementation uses IFormViewModel.GetIneligibilityReason()
        /// Override this method for custom ineligibility messages
        /// </summary>
        /// <param name="viewModel">The form ViewModel to check</param>
        /// <returns>Error message explaining why the form is not eligible, or null if eligible</returns>
        public virtual string? GetIneligibilityReason(TViewModel viewModel)
        {
            if (viewModel is IFormViewModel formViewModel)
            {
                return formViewModel.GetIneligibilityReason();
            }
            return null;
        }

        #endregion

        #region Implementation Methods

        /// <summary>
        /// Saves the form submission including files and data
        /// </summary>
        /// <param name="viewModel">The form ViewModel containing the data</param>
        /// <param name="formName">The name of the form (used for file organization)</param>
        /// <param name="userId">The ID of the user submitting the form</param>
        /// <returns>The ID of the created entity</returns>
        public virtual async Task<int> SaveAsync(TViewModel viewModel, string formName, string? userId = null)
        {
            try
            {
                _logger.LogInformation("Starting form submission for {FormName} by user {UserId}", formName, userId);

                // Upload files if the ViewModel supports file uploads
                var filePaths = new Dictionary<string, string>();
                if (viewModel is IFileUploadViewModel fileUploadViewModel)
                {
                    filePaths = await UploadFilesAsync(fileUploadViewModel, formName);
                }

                // Map ViewModel to Entity
                var entity = MapToEntity(viewModel);
                
                // Set audit fields
                entity.SubmissionGuid = viewModel.SubmissionGuid;
                entity.CreatedByUserId = userId;
                entity.CreatedDate = DateTime.UtcNow;

                // Apply file paths to entity
                ApplyFilePathsToEntity(entity, filePaths);

                // Save to database
                _context.Set<TEntity>().Add(entity);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Form submission completed successfully. Entity ID: {EntityId}", entity.Id);
                return entity.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving form submission for {FormName} by user {UserId}", formName, userId);
                throw;
            }
        }

        /// <summary>
        /// Retrieves a form submission by ID
        /// </summary>
        /// <param name="id">The ID of the submission</param>
        /// <returns>The entity if found, null otherwise</returns>
        public virtual async Task<TEntity?> GetByIdAsync(int id)
        {
            return await _context.Set<TEntity>().FirstOrDefaultAsync(e => e.Id == id);
        }

        /// <summary>
        /// Retrieves a form submission by submission GUID
        /// </summary>
        /// <param name="submissionGuid">The submission GUID</param>
        /// <returns>The entity if found, null otherwise</returns>
        public virtual async Task<TEntity?> GetBySubmissionGuidAsync(Guid submissionGuid)
        {
            return await _context.Set<TEntity>().FirstOrDefaultAsync(e => e.SubmissionGuid == submissionGuid);
        }

        /// <summary>
        /// Retrieves a form submission by ID with user authorization check
        /// </summary>
        /// <param name="id">The ID of the submission</param>
        /// <param name="userId">The ID of the user (for authorization)</param>
        /// <returns>The entity if found and authorized, null otherwise</returns>
        public virtual async Task<TEntity?> GetByIdAsync(int id, string? userId)
        {
            var query = _context.Set<TEntity>().AsQueryable();

            // Apply user filter if userId is provided
            if (!string.IsNullOrEmpty(userId))
            {
                query = query.Where(e => e.CreatedByUserId == userId);
            }

            return await query.FirstOrDefaultAsync(e => e.Id == id);
        }

        /// <summary>
        /// Updates an existing form submission
        /// </summary>
        /// <param name="id">The ID of the submission to update</param>
        /// <param name="viewModel">The updated ViewModel data</param>
        /// <param name="formName">The name of the form</param>
        /// <param name="userId">The ID of the user performing the update</param>
        /// <returns>True if successful, false otherwise</returns>
        public virtual async Task<bool> UpdateAsync(int id, TViewModel viewModel, string formName, string? userId = null)
        {
            try
            {
                var entity = await GetByIdAsync(id, userId);
                if (entity == null)
                {
                    _logger.LogWarning("Entity with ID {EntityId} not found or not authorized for user {UserId}", id, userId);
                    return false;
                }

                // Upload new files if provided
                var filePaths = new Dictionary<string, string>();
                if (viewModel is IFileUploadViewModel fileUploadViewModel)
                {
                    filePaths = await UploadFilesAsync(fileUploadViewModel, formName);
                }

                // Update entity with new data
                entity = MapToEntity(viewModel, entity);
                entity.UpdatedByUserId = userId;
                entity.MarkAsUpdated();

                // Apply new file paths to entity
                ApplyFilePathsToEntity(entity, filePaths);

                await _context.SaveChangesAsync();

                _logger.LogInformation("Form submission updated successfully. Entity ID: {EntityId}", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating form submission {EntityId} for user {UserId}", id, userId);
                return false;
            }
        }

        /// <summary>
        /// Soft deletes a form submission
        /// </summary>
        /// <param name="id">The ID of the submission to delete</param>
        /// <param name="userId">The ID of the user performing the deletion</param>
        /// <returns>True if successful, false otherwise</returns>
        public virtual async Task<bool> DeleteAsync(int id, string? userId = null)
        {
            try
            {
                var entity = await GetByIdAsync(id, userId);
                if (entity == null)
                {
                    _logger.LogWarning("Entity with ID {EntityId} not found or not authorized for user {UserId}", id, userId);
                    return false;
                }

                entity.MarkAsDeleted();
                entity.UpdatedByUserId = userId;
                await _context.SaveChangesAsync();

                _logger.LogInformation("Form submission soft deleted successfully. Entity ID: {EntityId}", id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting form submission {EntityId} for user {UserId}", id, userId);
                return false;
            }
        }

        /// <summary>
        /// Gets all submissions for a specific user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>List of entities for the user</returns>
        public virtual async Task<List<TEntity>> GetByUserIdAsync(string userId)
        {
            return await _context.Set<TEntity>()
                .Where(e => e.CreatedByUserId == userId)
                .OrderByDescending(e => e.CreatedDate)
                .ToListAsync();
        }

        /// <summary>
        /// Checks if a user has already submitted a form for a specific Civil ID
        /// Base implementation - should be overridden by derived classes that support Civil ID checking
        /// </summary>
        /// <param name="civilId">The Civil ID to check</param>
        /// <param name="userId">The ID of the user (for authorization)</param>
        /// <returns>The existing submission if found, null otherwise</returns>
        public virtual async Task<TEntity?> GetExistingSubmissionByCivilIdAsync(string civilId, string? userId)
        {
            // Base implementation returns null - derived classes should override this
            // for forms that support Civil ID-based single submission rules
            await Task.CompletedTask;
            return null;
        }

        /// <summary>
        /// Saves or updates a form submission based on whether an existing submission exists
        /// </summary>
        /// <param name="viewModel">The form ViewModel containing the data</param>
        /// <param name="formName">The name of the form (used for file organization)</param>
        /// <param name="userId">The ID of the user submitting the form</param>
        /// <returns>The ID of the created or updated entity</returns>
        public virtual async Task<int> SaveOrUpdateAsync(TViewModel viewModel, string formName, string? userId = null)
        {
            try
            {
                _logger.LogInformation("Starting SaveOrUpdate for {FormName} by user {UserId}", formName, userId);

                // Check for existing submission - this will be overridden by derived classes
                var existingSubmission = await GetExistingSubmissionByCivilIdAsync(
                    GetCivilIdFromViewModel(viewModel), userId);

                if (existingSubmission != null)
                {
                    // Update existing submission
                    _logger.LogInformation("EDIT MODE: Updating existing submission {EntityId} for user {UserId}",
                        existingSubmission.Id, userId);

                    var success = await UpdateAsync(existingSubmission.Id, viewModel, formName, userId);
                    if (success)
                    {
                        _logger.LogInformation("EDIT MODE: Successfully updated submission {EntityId} for user {UserId}. Returning entity ID: {EntityId}",
                            existingSubmission.Id, userId, existingSubmission.Id);
                        return existingSubmission.Id;
                    }
                    else
                    {
                        _logger.LogError("EDIT MODE: Failed to update existing submission {EntityId} for user {UserId}",
                            existingSubmission.Id, userId);
                        throw new InvalidOperationException("Failed to update existing submission");
                    }
                }
                else
                {
                    // Create new submission
                    _logger.LogInformation("CREATE MODE: Creating new submission for user {UserId}", userId);
                    var newEntityId = await SaveAsync(viewModel, formName, userId);
                    _logger.LogInformation("CREATE MODE: Successfully created new submission {EntityId} for user {UserId}",
                        newEntityId, userId);
                    return newEntityId;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SaveOrUpdate for {FormName} by user {UserId}", formName, userId);
                throw;
            }
        }

        #endregion

        #region Protected Helper Methods

        /// <summary>
        /// Extracts Civil ID from ViewModel - should be overridden by derived classes
        /// </summary>
        /// <param name="viewModel">The ViewModel to extract Civil ID from</param>
        /// <returns>Civil ID string, or empty string if not supported</returns>
        protected virtual string GetCivilIdFromViewModel(TViewModel viewModel)
        {
            // Base implementation returns empty string - derived classes should override
            return string.Empty;
        }

        /// <summary>
        /// Uploads files from the ViewModel using the file upload service
        /// </summary>
        /// <param name="fileUploadViewModel">The ViewModel containing file uploads</param>
        /// <param name="formName">The name of the form</param>
        /// <returns>Dictionary of file property names and their uploaded paths</returns>
        protected virtual async Task<Dictionary<string, string>> UploadFilesAsync(IFileUploadViewModel fileUploadViewModel, string formName)
        {
            var filePaths = new Dictionary<string, string>();
            var fileUploads = fileUploadViewModel.GetFileUploads();

            foreach (var kvp in fileUploads)
            {
                if (kvp.Value != null && kvp.Value.Length > 0)
                {
                    try
                    {
                        var fileName = GenerateFileName(kvp.Key, kvp.Value.FileName);
                        var relativePath = await _fileUploadService.UploadFileAsync(
                            kvp.Value, 
                            formName, 
                            fileUploadViewModel.SubmissionGuid, 
                            fileName);
                        
                        filePaths[kvp.Key] = relativePath;
                        _logger.LogInformation("File uploaded successfully: {FileName} -> {Path}", kvp.Value.FileName, relativePath);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error uploading file {FileName} for property {PropertyName}", kvp.Value.FileName, kvp.Key);
                        throw;
                    }
                }
            }

            return filePaths;
        }

        /// <summary>
        /// Generates a standardized file name based on the property name and original file name
        /// Override this method to customize file naming conventions
        /// </summary>
        /// <param name="propertyName">The property name from the ViewModel</param>
        /// <param name="originalFileName">The original file name</param>
        /// <returns>The generated file name</returns>
        protected virtual string GenerateFileName(string propertyName, string originalFileName)
        {
            var extension = Path.GetExtension(originalFileName);
            var baseName = propertyName.Replace("File", "").ToLowerInvariant();
            return $"{baseName}{extension}";
        }

        /// <summary>
        /// Applies uploaded file paths to the entity
        /// Override this method to customize how file paths are mapped to entity properties
        /// </summary>
        /// <param name="entity">The entity to update</param>
        /// <param name="filePaths">Dictionary of property names and file paths</param>
        protected virtual void ApplyFilePathsToEntity(TEntity entity, Dictionary<string, string> filePaths)
        {
            // Default implementation uses reflection to set file path properties
            // Override this method in derived classes for better performance and type safety
            var entityType = typeof(TEntity);
            
            foreach (var kvp in filePaths)
            {
                var pathPropertyName = kvp.Key.Replace("File", "Path");
                var property = entityType.GetProperty(pathPropertyName);
                
                if (property != null && property.CanWrite && property.PropertyType == typeof(string))
                {
                    property.SetValue(entity, kvp.Value);
                }
            }
        }

        #endregion
    }
}
