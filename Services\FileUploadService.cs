using Microsoft.AspNetCore.StaticFiles;

namespace Forms.ktech.Services
{
    /// <summary>
    /// Service implementation for handling file uploads across all forms
    /// </summary>
    public class FileUploadService : IFileUploadService
    {
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<FileUploadService> _logger;
        private readonly FileExtensionContentTypeProvider _contentTypeProvider;

        // Default allowed extensions for all forms
        private static readonly string[] DefaultAllowedExtensions = { ".pdf", ".jpg", ".jpeg", ".png" };
        private const int DefaultMaxSizeInMB = 5;

        public FileUploadService(IWebHostEnvironment environment, ILogger<FileUploadService> logger)
        {
            _environment = environment;
            _logger = logger;
            _contentTypeProvider = new FileExtensionContentTypeProvider();
        }

        public async Task<string> UploadFileAsync(IFormFile file, string formName, Guid submissionGuid, string? fileName = null)
        {
            if (file == null || file.Length == 0)
                throw new ArgumentException("File is null or empty", nameof(file));

            // Validate file
            var (isValid, errorMessage) = ValidateFile(file, DefaultAllowedExtensions, DefaultMaxSizeInMB);
            if (!isValid)
                throw new InvalidOperationException(errorMessage);

            // Create directory structure
            var uploadPath = Path.Combine(_environment.WebRootPath, "uploads", formName, submissionGuid.ToString());
            Directory.CreateDirectory(uploadPath);

            // Generate file name
            var finalFileName = fileName ?? GenerateSecureFileName(file.FileName);
            var filePath = Path.Combine(uploadPath, finalFileName);

            try
            {
                // Save file
                using var stream = new FileStream(filePath, FileMode.Create);
                await file.CopyToAsync(stream);

                _logger.LogInformation("File uploaded successfully: {FilePath}", filePath);

                // Return relative path
                return Path.Combine("uploads", formName, submissionGuid.ToString(), finalFileName).Replace('\\', '/');
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file: {FileName}", file.FileName);
                throw;
            }
        }

        public async Task<Dictionary<string, string>> UploadFilesAsync(Dictionary<string, IFormFile> files, string formName, Guid submissionGuid)
        {
            var results = new Dictionary<string, string>();

            foreach (var kvp in files)
            {
                if (kvp.Value != null && kvp.Value.Length > 0)
                {
                    try
                    {
                        var relativePath = await UploadFileAsync(kvp.Value, formName, submissionGuid, kvp.Key);
                        results[kvp.Key] = relativePath;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error uploading file {FileName} for key {Key}", kvp.Value.FileName, kvp.Key);
                        throw;
                    }
                }
            }

            return results;
        }

        public (bool IsValid, string? ErrorMessage) ValidateFile(IFormFile file, string[] allowedExtensions, int maxSizeInMB = 5)
        {
            if (file == null)
                return (false, "File is required");

            if (file.Length == 0)
                return (false, "File is empty");

            // Check file size
            var maxSizeInBytes = maxSizeInMB * 1024 * 1024;
            if (file.Length > maxSizeInBytes)
                return (false, $"File size exceeds {maxSizeInMB} MB limit");

            // Check file extension
            var extension = Path.GetExtension(file.FileName)?.ToLowerInvariant();
            if (string.IsNullOrEmpty(extension) || !allowedExtensions.Contains(extension))
                return (false, $"File type not allowed. Allowed types: {string.Join(", ", allowedExtensions)}");

            // Check for potentially dangerous file names
            if (ContainsDangerousCharacters(file.FileName))
                return (false, "File name contains invalid characters");

            // Basic content type validation
            if (!_contentTypeProvider.TryGetContentType(file.FileName, out var contentType))
                return (false, "Unable to determine file type");

            return (true, null);
        }

        public async Task<bool> DeleteSubmissionFilesAsync(string formName, Guid submissionGuid)
        {
            try
            {
                var uploadPath = Path.Combine(_environment.WebRootPath, "uploads", formName, submissionGuid.ToString());
                
                if (Directory.Exists(uploadPath))
                {
                    Directory.Delete(uploadPath, true);
                    _logger.LogInformation("Deleted submission files: {Path}", uploadPath);
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting submission files for {FormName}/{SubmissionGuid}", formName, submissionGuid);
                return false;
            }
        }

        public string GetPhysicalPath(string relativePath)
        {
            return Path.Combine(_environment.WebRootPath, relativePath.Replace('/', Path.DirectorySeparatorChar));
        }

        public bool FileExists(string relativePath)
        {
            var physicalPath = GetPhysicalPath(relativePath);
            return File.Exists(physicalPath);
        }

        public FileInfo? GetFileInfo(string relativePath)
        {
            var physicalPath = GetPhysicalPath(relativePath);
            return File.Exists(physicalPath) ? new FileInfo(physicalPath) : null;
        }

        public async Task<Stream?> GetFileStreamAsync(string relativePath)
        {
            var physicalPath = GetPhysicalPath(relativePath);
            
            if (!File.Exists(physicalPath))
                return null;

            try
            {
                return new FileStream(physicalPath, FileMode.Open, FileAccess.Read, FileShare.Read);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening file stream for {Path}", physicalPath);
                return null;
            }
        }

        public string GetMimeType(string fileName)
        {
            if (_contentTypeProvider.TryGetContentType(fileName, out var contentType))
                return contentType;

            return "application/octet-stream"; // Default MIME type
        }

        private static string GenerateSecureFileName(string originalFileName)
        {
            var extension = Path.GetExtension(originalFileName);
            var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
            
            // Sanitize the file name
            var sanitizedName = string.Join("_", nameWithoutExtension.Split(Path.GetInvalidFileNameChars()));
            
            // Limit length and add timestamp for uniqueness
            if (sanitizedName.Length > 50)
                sanitizedName = sanitizedName.Substring(0, 50);
                
            return $"{sanitizedName}_{DateTime.UtcNow:yyyyMMddHHmmss}{extension}";
        }

        private static bool ContainsDangerousCharacters(string fileName)
        {
            var dangerousChars = new[] { "..", "/", "\\", ":", "*", "?", "\"", "<", ">", "|" };
            return dangerousChars.Any(fileName.Contains);
        }
    }
}
