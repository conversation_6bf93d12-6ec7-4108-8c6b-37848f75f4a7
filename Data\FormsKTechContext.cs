using Microsoft.EntityFrameworkCore;
using Forms.ktech.Features.KuwaitiStudentInfo.Models;
using Forms.ktech.Models;

namespace Forms.ktech.Data
{
    /// <summary>
    /// Entity Framework DbContext for the Forms.ktech application
    /// Single DbContext for all forms following the shared architecture pattern
    /// </summary>
    public class FormsKTechContext : DbContext
    {
        public FormsKTechContext(DbContextOptions<FormsKTechContext> options) : base(options)
        {
        }

        #region DbSets

        /// <summary>
        /// DbSet for Kuwaiti Student Information form submissions
        /// </summary>
        public DbSet<StudentInfo> StudentInfos { get; set; }

        /// <summary>
        /// DbSet for synchronized student data from SIS (Student Information System)
        /// Contains local copies of student records for form pre-filling
        /// </summary>
        public DbSet<SisStudent> SisStudents { get; set; }

        /// <summary>
        /// DbSet for tracking SIS data synchronization operations
        /// Provides audit trail and monitoring for sync processes
        /// </summary>
        public DbSet<SyncHistory> SyncHistories { get; set; }

        /// <summary>
        /// DbSet for document approval tracking
        /// Manages approval status and history for individual documents
        /// </summary>
        public DbSet<DocumentApproval> DocumentApprovals { get; set; }

        /// <summary>
        /// DbSet for email notification tracking
        /// Manages email delivery status and retry logic
        /// </summary>
        public DbSet<EmailNotification> EmailNotifications { get; set; }

        #endregion

        #region Configuration

        /// <summary>
        /// Configure entity relationships and constraints
        /// </summary>
        /// <param name="modelBuilder">The model builder</param>
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure global query filters for soft delete
            ConfigureSoftDeleteFilters(modelBuilder);

            // Configure StudentInfo entity
            ConfigureStudentInfo(modelBuilder);

            // Configure SIS entities
            ConfigureSisStudent(modelBuilder);
            ConfigureSyncHistory(modelBuilder);

            // Configure document approval entities
            ConfigureDocumentApproval(modelBuilder);
            ConfigureEmailNotification(modelBuilder);
        }

        /// <summary>
        /// Configure soft delete query filters for all entities inheriting from BaseEntity
        /// </summary>
        /// <param name="modelBuilder">The model builder</param>
        private static void ConfigureSoftDeleteFilters(ModelBuilder modelBuilder)
        {
            // Apply soft delete filter to StudentInfo
            modelBuilder.Entity<StudentInfo>()
                .HasQueryFilter(e => !e.IsDeleted);

            // SisStudent doesn't inherit from BaseEntity, so no soft delete filter needed

            // Apply soft delete filter to SyncHistory
            modelBuilder.Entity<SyncHistory>()
                .HasQueryFilter(e => !e.IsDeleted);
        }

        /// <summary>
        /// Configure the StudentInfo entity
        /// </summary>
        /// <param name="modelBuilder">The model builder</param>
        private static void ConfigureStudentInfo(ModelBuilder modelBuilder)
        {
            var entity = modelBuilder.Entity<StudentInfo>();

            // Configure table name
            entity.ToTable("StudentInfos");

            // Configure indexes for better performance
            entity.HasIndex(e => e.SubmissionGuid)
                .IsUnique()
                .HasDatabaseName("IX_StudentInfos_SubmissionGuid");

            entity.HasIndex(e => e.SisStudentId)
                .HasDatabaseName("IX_StudentInfos_SisStudentId");

            entity.HasIndex(e => e.CreatedDate)
                .HasDatabaseName("IX_StudentInfos_CreatedDate");

            entity.HasIndex(e => e.CreatedByUserId)
                .HasDatabaseName("IX_StudentInfos_CreatedByUserId");

            // Configure foreign key relationship to SisStudent
            entity.HasOne(e => e.SisStudent)
                .WithMany()
                .HasForeignKey(e => e.SisStudentId)
                .OnDelete(DeleteBehavior.SetNull);

            // Configure string properties with specific lengths

            entity.Property(e => e.FatherName)
                .HasMaxLength(200)
                .IsRequired();

            entity.Property(e => e.MotherName)
                .HasMaxLength(200)
                .IsRequired();

            // Configure file path properties
            entity.Property(e => e.StudentCivilIdPath)
                .HasMaxLength(500);

            entity.Property(e => e.StudentNationalityCertificatePath)
                .HasMaxLength(500);

            entity.Property(e => e.FatherCivilIdPath)
                .HasMaxLength(500);

            entity.Property(e => e.FatherNationalityCertificatePath)
                .HasMaxLength(500);

            entity.Property(e => e.MotherCivilIdPath)
                .HasMaxLength(500);

            entity.Property(e => e.MotherNationalityCertificatePath)
                .HasMaxLength(500);

            // Configure audit fields from BaseEntity
            entity.Property(e => e.CreatedByUserId)
                .HasMaxLength(450);

            entity.Property(e => e.UpdatedByUserId)
                .HasMaxLength(450);
        }

        #endregion

        #region SaveChanges Override

        /// <summary>
        /// Override SaveChanges to automatically handle audit fields
        /// </summary>
        /// <returns>Number of affected records</returns>
        public override int SaveChanges()
        {
            HandleAuditFields();
            return base.SaveChanges();
        }

        /// <summary>
        /// Override SaveChangesAsync to automatically handle audit fields
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Number of affected records</returns>
        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            HandleAuditFields();
            return await base.SaveChangesAsync(cancellationToken);
        }

        /// <summary>
        /// Automatically set audit fields for entities inheriting from BaseEntity
        /// </summary>
        private void HandleAuditFields()
        {
            var entries = ChangeTracker.Entries<BaseEntity>();

            foreach (var entry in entries)
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        entry.Entity.CreatedDate = DateTime.UtcNow;
                        // CreatedByUserId should be set by the service layer
                        break;

                    case EntityState.Modified:
                        entry.Entity.MarkAsUpdated();
                        // UpdatedByUserId should be set by the service layer
                        break;
                }
            }
        }

        /// <summary>
        /// Configure the SisStudent entity
        /// </summary>
        /// <param name="modelBuilder">The model builder</param>
        private static void ConfigureSisStudent(ModelBuilder modelBuilder)
        {
            var entity = modelBuilder.Entity<SisStudent>();

            // Configure table name
            entity.ToTable("SisStudents");

            // Configure primary key
            entity.HasKey(e => e.Id);

            // Configure unique constraint on StudentID (business key)
            entity.HasIndex(e => e.StudentID)
                .IsUnique()
                .HasDatabaseName("IX_SisStudents_StudentID");

            // Configure index on Email for Azure AD matching (most common lookup)
            entity.HasIndex(e => e.Email)
                .HasDatabaseName("IX_SisStudents_Email");

            // Configure index on NationalID for Civil ID lookups
            entity.HasIndex(e => e.NationalID)
                .HasDatabaseName("IX_SisStudents_NationalID");

            // Configure index on LastSyncDate for data freshness queries
            entity.HasIndex(e => e.LastSyncDate)
                .HasDatabaseName("IX_SisStudents_LastSyncDate");

            // Configure string length constraints
            entity.Property(e => e.StudentID)
                .HasMaxLength(50)
                .IsRequired();

            entity.Property(e => e.FullNameAR)
                .HasMaxLength(200);

            entity.Property(e => e.FullNameEN)
                .HasMaxLength(200)
                .IsRequired();

            entity.Property(e => e.Gender)
                .HasMaxLength(20);

            entity.Property(e => e.Nationality)
                .HasMaxLength(50);

            entity.Property(e => e.NationalID)
                .HasMaxLength(20);

            entity.Property(e => e.Email)
                .HasMaxLength(100)
                .IsRequired();

            entity.Property(e => e.MobileNo)
                .HasMaxLength(20);

            entity.Property(e => e.EnrollmentStatus)
                .HasMaxLength(50);

            entity.Property(e => e.Major)
                .HasMaxLength(100);

            entity.Property(e => e.Level)
                .HasMaxLength(50);

            entity.Property(e => e.DataHash)
                .HasMaxLength(64);
        }

        /// <summary>
        /// Configure the SyncHistory entity
        /// </summary>
        /// <param name="modelBuilder">The model builder</param>
        private static void ConfigureSyncHistory(ModelBuilder modelBuilder)
        {
            var entity = modelBuilder.Entity<SyncHistory>();

            // Configure table name
            entity.ToTable("SyncHistories");

            // Configure primary key
            entity.HasKey(e => e.Id);

            // Configure unique constraint on SyncId
            entity.HasIndex(e => e.SyncId)
                .IsUnique()
                .HasDatabaseName("IX_SyncHistories_SyncId");

            // Configure index on StartTime for chronological queries
            entity.HasIndex(e => e.StartTime)
                .HasDatabaseName("IX_SyncHistories_StartTime");

            // Configure index on Status for filtering active/completed syncs
            entity.HasIndex(e => e.Status)
                .HasDatabaseName("IX_SyncHistories_Status");

            // Configure composite index for sync type and status queries
            entity.HasIndex(e => new { e.SyncType, e.Status })
                .HasDatabaseName("IX_SyncHistories_TypeStatus");

            // Configure index on TriggeredByUserId for admin queries
            entity.HasIndex(e => e.TriggeredByUserId)
                .HasDatabaseName("IX_SyncHistories_TriggeredBy");

            // Configure audit field indexes from BaseEntity
            entity.HasIndex(e => e.CreatedDate)
                .HasDatabaseName("IX_SyncHistories_CreatedDate");

            entity.HasIndex(e => e.SubmissionGuid)
                .IsUnique()
                .HasDatabaseName("IX_SyncHistories_SubmissionGuid");

            // Configure enum properties
            entity.Property(e => e.SyncType)
                .HasConversion<int>()
                .IsRequired();

            entity.Property(e => e.Status)
                .HasConversion<int>()
                .IsRequired();

            // Configure string length constraints
            entity.Property(e => e.ErrorMessage)
                .HasMaxLength(1000);

            entity.Property(e => e.ErrorDetails)
                .HasMaxLength(4000);

            entity.Property(e => e.AdditionalDetails)
                .HasMaxLength(2000);

            entity.Property(e => e.CurrentStep)
                .HasMaxLength(200);

            entity.Property(e => e.ApiEndpoint)
                .HasMaxLength(500);

            entity.Property(e => e.TriggeredByUserId)
                .HasMaxLength(450);

            // Configure audit fields from BaseEntity
            entity.Property(e => e.CreatedByUserId)
                .HasMaxLength(450);

            entity.Property(e => e.UpdatedByUserId)
                .HasMaxLength(450);
        }

        /// <summary>
        /// Configure the DocumentApproval entity
        /// </summary>
        /// <param name="modelBuilder">The model builder</param>
        private static void ConfigureDocumentApproval(ModelBuilder modelBuilder)
        {
            var entity = modelBuilder.Entity<DocumentApproval>();

            // Configure table name
            entity.ToTable("DocumentApprovals");

            // Configure primary key
            entity.HasKey(e => e.Id);

            // Configure foreign key relationship to StudentInfo
            entity.HasOne(e => e.Submission)
                .WithMany()
                .HasForeignKey(e => e.SubmissionId)
                .OnDelete(DeleteBehavior.Cascade);

            // Apply matching query filter for soft deletes to avoid EF warnings
            entity.HasQueryFilter(da => !da.Submission.IsDeleted);

            // Configure indexes for performance
            entity.HasIndex(e => new { e.SubmissionId, e.DocumentType })
                .HasDatabaseName("IX_DocumentApprovals_SubmissionId_DocumentType");

            entity.HasIndex(e => e.Status)
                .HasDatabaseName("IX_DocumentApprovals_Status");

            entity.HasIndex(e => e.ApprovalDate)
                .HasDatabaseName("IX_DocumentApprovals_ApprovalDate");

            entity.HasIndex(e => e.ApprovedByUserId)
                .HasDatabaseName("IX_DocumentApprovals_ApprovedByUserId");

            entity.HasIndex(e => e.CreatedDate)
                .HasDatabaseName("IX_DocumentApprovals_CreatedDate");

            // Configure enum property
            entity.Property(e => e.Status)
                .HasConversion<int>()
                .IsRequired();

            // Configure string length constraints
            entity.Property(e => e.DocumentType)
                .HasMaxLength(100)
                .IsRequired();

            entity.Property(e => e.ApprovedByUserId)
                .HasMaxLength(450);

            entity.Property(e => e.DisapprovalReason)
                .HasMaxLength(1000);

            entity.Property(e => e.Comments)
                .HasMaxLength(2000);
        }

        /// <summary>
        /// Configure the EmailNotification entity
        /// </summary>
        /// <param name="modelBuilder">The model builder</param>
        private static void ConfigureEmailNotification(ModelBuilder modelBuilder)
        {
            var entity = modelBuilder.Entity<EmailNotification>();

            // Configure table name
            entity.ToTable("EmailNotifications");

            // Configure primary key
            entity.HasKey(e => e.Id);

            // Configure foreign key relationship to StudentInfo
            entity.HasOne(e => e.Submission)
                .WithMany()
                .HasForeignKey(e => e.SubmissionId)
                .OnDelete(DeleteBehavior.Cascade);

            // Apply matching query filter for soft deletes to avoid EF warnings
            entity.HasQueryFilter(en => !en.Submission.IsDeleted);

            // Configure indexes for performance
            entity.HasIndex(e => e.SubmissionId)
                .HasDatabaseName("IX_EmailNotifications_SubmissionId");

            entity.HasIndex(e => e.Status)
                .HasDatabaseName("IX_EmailNotifications_Status");

            entity.HasIndex(e => e.EmailType)
                .HasDatabaseName("IX_EmailNotifications_EmailType");

            entity.HasIndex(e => e.CreatedDate)
                .HasDatabaseName("IX_EmailNotifications_CreatedDate");

            entity.HasIndex(e => e.SentDate)
                .HasDatabaseName("IX_EmailNotifications_SentDate");

            entity.HasIndex(e => new { e.Status, e.RetryCount })
                .HasDatabaseName("IX_EmailNotifications_Status_RetryCount");

            // Configure enum property
            entity.Property(e => e.Status)
                .HasConversion<int>()
                .IsRequired();

            // Configure string length constraints
            entity.Property(e => e.EmailType)
                .HasMaxLength(50)
                .IsRequired();

            entity.Property(e => e.RecipientEmail)
                .HasMaxLength(255)
                .IsRequired();

            entity.Property(e => e.Subject)
                .HasMaxLength(500)
                .IsRequired();

            entity.Property(e => e.Body)
                .IsRequired();

            entity.Property(e => e.FailureReason)
                .HasMaxLength(1000);
        }

        #endregion
    }
}
