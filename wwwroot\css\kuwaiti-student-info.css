/* 
 * Kuwaiti Student Info Form CSS - forms.ktech
 * Form-specific styles for the Kuwaiti Student Information form
 */

/* ===== NATIONALITY CHECKBOX STYLING ===== */
.nationality-checkbox:checked + label {
    color: #0d6efd;
    font-weight: 600;
}

/* ===== DOCUMENT SECTIONS ===== */
.document-section {
    border-left: 3px solid #e9ecef;
    padding-left: 1rem;
}

/* Mobile responsiveness for document sections */
@media (max-width: 768px) {
    .document-section {
        border-left: none;
        border-top: 3px solid #e9ecef;
        padding-left: 0;
        padding-top: 1rem;
        margin-top: 1rem;
    }
}

/* ===== DOCUMENT GRID LAYOUT ===== */
/* Ensure proper spacing in document grids */
.document-section .grid {
    gap: 1rem;
}

/* Responsive grid adjustments for better mobile experience */
@media (max-width: 768px) {
    .document-section .grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    /* Reduce bottom margin on mobile for better spacing */
    .document-section .grid > div {
        margin-bottom: 1rem;
    }
}

/* Ensure file upload components maintain proper spacing in grid */
.document-section .grid > div:last-child {
    margin-bottom: 0;
}

/* Dark mode support for document section borders */
.dark .document-section {
    border-left-color: #374151;
}

@media (max-width: 768px) {
    .dark .document-section {
        border-top-color: #374151;
    }
}

/* ===== BILINGUAL TEXT STYLING ===== */
/* Ensure proper text direction for mixed English/Arabic labels */
label {
    direction: ltr;
    text-align: left;
}

/* Arabic text within labels should display properly */
label:lang(ar) {
    direction: rtl;
    text-align: right;
}

/* For labels containing both English and Arabic, maintain LTR but allow Arabic to display correctly */
.bilingual-label {
    direction: ltr;
    text-align: left;
    unicode-bidi: embed;
}

/* Mobile number input styling */
input[type="tel"] {
    direction: ltr;
    text-align: left;
}

/* Ensure Arabic text in headers displays correctly */
h1, h2, h3, h4, h5, h6 {
    unicode-bidi: embed;
}

/* Accessibility improvements for bilingual content */
[lang="ar"] {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
}

/* Ensure proper spacing for bilingual labels */
.bilingual-separator {
    margin: 0 0.25rem;
    color: #6b7280;
}

/* ===== ENHANCED LIGHT/DARK MODE SUPPORT ===== */
/* Light mode specific styles */
:root {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --accent-color: #3b82f6;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
}

/* Dark mode specific styles */
.dark {
    --bg-primary: #1f2937;
    --bg-secondary: #111827;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --border-color: #374151;
    --accent-color: #60a5fa;
    --success-color: #34d399;
    --warning-color: #fbbf24;
    --error-color: #f87171;
}

/* Enhanced contrast for accessibility */
.high-contrast {
    --bg-primary: #000000;
    --bg-secondary: #1a1a1a;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --border-color: #666666;
    --accent-color: #4da6ff;
    --success-color: #00ff00;
    --warning-color: #ffff00;
    --error-color: #ff4444;
}

/* Theme transition animations */
* {
    transition: background-color 0.2s ease-in-out,
                border-color 0.2s ease-in-out,
                color 0.2s ease-in-out;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
        animation: none !important;
    }
}

/* Enhanced focus indicators for better accessibility */
button:focus,
input:focus,
select:focus,
textarea:focus,
a:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Print styles for both themes */
@media print {
    * {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }

    .theme-toggle,
    .fixed {
        display: none !important;
    }
}
