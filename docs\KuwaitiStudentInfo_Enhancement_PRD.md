# Product Requirements Document (PRD)
## Multi-Step Form Enhancement: Parent Name Removal, Universal Birth Certificate, and Deceased Parent Logic

---

## Executive Summary

This PRD outlines the implementation of three major enhancements to the existing Kuwaiti Student Information multi-step form:

1. **Remove Parent Name Fields**: Eliminate FatherName and <PERSON>Name input fields from the form
2. **Universal Birth Certificate Requirement**: Make student birth certificate required in all eligible cases (A, B, C)
3. **Deceased Parent Logic**: Add conditional logic for deceased Kuwaiti parents with death certificate requirements

## Current State Analysis

### Existing 4-Case Nationality Logic
The current system implements the following eligibility cases:

| Case | Father | Mother | Student | Status | Current Required Documents |
|------|--------|--------|---------|--------|---------------------------|
| A | ✅ | ✅ | ✅ | Eligible | All 6: Civil ID + Nationality Cert for all |
| B | ✅ | ❌ | ✅ | Eligible | Father & Student: Civil ID + Nationality Cert (4 docs) |
| C | ❌ | ✅ | ❌ | Eligible | Mother: Civil ID + Nationality Cert, Student: Civil ID only (3 docs) |
| D | ❌ | ❌ | ❌ | **Ineligible** | Blocked submission |

### Current Parent Name Fields (TO BE REMOVED)
**Fields to Remove:**
- `FatherName` (string, Required, StringLength(200)) - Line 86 in StudentFormViewModel.cs
- `MotherName` (string, Required, StringLength(200)) - Line 104 in StudentFormViewModel.cs
- Corresponding HTML inputs in CollectInfo.cshtml (Lines 218-222, 243-247)

### Current Document Structure
**Existing File Upload Properties:**
- `StudentCivilIdFile` - Always required
- `StudentNationalityCertificateFile` - Conditional based on case
- `FatherCivilIdFile` - Conditional based on case
- `FatherNationalityCertificateFile` - Conditional based on case
- `MotherCivilIdFile` - Conditional based on case
- `MotherNationalityCertificateFile` - Conditional based on case

## Requirements Specification

### 1. Remove Parent Name Fields

**FR-1.1: Remove ViewModel Properties**
- Remove `FatherName` property from `StudentFormViewModel.cs` (Line 86)
- Remove `MotherName` property from `StudentFormViewModel.cs` (Line 104)
- Remove all associated validation attributes and display attributes

**FR-1.2: Remove UI Elements**
- Remove father name input field from `CollectInfo.cshtml` (Lines 218-222)
- Remove mother name input field from `CollectInfo.cshtml` (Lines 243-247)
- Remove validation spans for these fields

**FR-1.3: Update Summary View**
- Remove parent name displays from review/summary sections
- Update any references to parent names in JavaScript

### 2. Universal Birth Certificate Requirement

**FR-2.1: Add Birth Certificate Property**
- Add `StudentBirthCertificateFile` property to `StudentFormViewModel.cs`
- Apply appropriate validation attributes: `[FileSize(5)]`, `[AllowedFileExtensions(".pdf", ".jpg", ".jpeg", ".png")]`

**FR-2.2: Update Document Requirements Logic**
- Modify `GetRequiredFileUploads()` method to include birth certificate in cases A, B, and C
- Update `GetFileUploads()` method to include the new property

**FR-2.3: Add UI Upload Section**
- Add birth certificate upload section in `CollectInfo.cshtml` Step 2
- Position it after student civil ID section
- Make it always visible (no conditional logic)
- Add appropriate labels in English and Arabic

**FR-2.4: Update Document Count**
- Case A: 7 documents (was 6) - All Civil IDs + All Nationality Certs + Student Birth Cert
- Case B: 5 documents (was 4) - Father & Student Civil IDs + Nationality Certs + Student Birth Cert  
- Case C: 4 documents (was 3) - Mother Civil ID + Nationality Cert, Student Civil ID + Birth Cert
- Case D: 0 documents (unchanged) - Blocked submission

### 3. Deceased Parent Logic

**FR-3.1: Add Deceased Status Properties**
- Add `FatherIsDeceased` boolean property to `StudentFormViewModel.cs`
- Add `MotherIsDeceased` boolean property to `StudentFormViewModel.cs`
- No validation attributes needed (optional checkboxes)

**FR-3.2: Add Death Certificate Properties**
- Add `FatherDeathCertificateFile` property to `StudentFormViewModel.cs`
- Add `MotherDeathCertificateFile` property to `StudentFormViewModel.cs`
- Apply validation attributes: `[FileSize(5)]`, `[AllowedFileExtensions(".pdf", ".jpg", ".jpeg", ".png")]`

**FR-3.3: Update Document Requirements Logic**
- Modify `GetRequiredFileUploads()` to implement deceased parent logic:
  - **Living Father Logic**: If `FatherIsKuwaiti == true` AND `FatherIsDeceased == false` → require `FatherCivilIdFile` and `FatherNationalityCertificateFile`
  - **Deceased Father Logic**: If `FatherIsKuwaiti == true` AND `FatherIsDeceased == true` → require ONLY `FatherDeathCertificateFile` (no civil ID or nationality cert)
  - **Living Mother Logic**: If `MotherIsKuwaiti == true` AND `MotherIsDeceased == false` → require `MotherCivilIdFile` and `MotherNationalityCertificateFile`
  - **Deceased Mother Logic**: If `MotherIsKuwaiti == true` AND `MotherIsDeceased == true` → require ONLY `MotherDeathCertificateFile` (no civil ID or nationality cert)

**FR-3.4: Add UI Elements**
- Add "Father is deceased" checkbox in father section (only visible when father is Kuwaiti)
- Add "Mother is deceased" checkbox in mother section (only visible when mother is Kuwaiti)
- Add conditional death certificate upload sections
- Implement JavaScript logic to show/hide death certificate uploads

**FR-3.5: Update Document Count (Replacement Logic)**
- **Case A with deceased father**: 6 documents (7 - 2 father docs + 1 death cert = 6)
- **Case A with deceased mother**: 6 documents (7 - 2 mother docs + 1 death cert = 6)
- **Case A with both deceased**: 5 documents (7 - 4 parent docs + 2 death certs = 5)
- **Case B with deceased father**: 4 documents (5 - 2 father docs + 1 death cert = 4)
- **Case C with deceased mother**: 3 documents (4 - 2 mother docs + 1 death cert = 3)

## Technical Implementation Plan

### Phase 1: ViewModel Updates
**Files to Modify:**
- `Features/KuwaitiStudentInfo/Models/StudentFormViewModel.cs`

**Changes:**
1. Remove `FatherName` and `MotherName` properties
2. Add `StudentBirthCertificateFile` property
3. Add `FatherIsDeceased` and `MotherIsDeceased` boolean properties
4. Add `FatherDeathCertificateFile` and `MotherDeathCertificateFile` properties
5. Update `GetFileUploads()` method to include new file properties
6. Update `GetRequiredFileUploads()` method with new deceased parent logic

### Detailed GetRequiredFileUploads() Implementation Logic

```csharp
public List<string> GetRequiredFileUploads()
{
    var requiredFiles = new List<string>();
    var eligibilityCase = GetEligibilityCase();

    switch (eligibilityCase)
    {
        case "A": // All Kuwaiti
            // Student documents (always required)
            requiredFiles.AddRange(new[]
            {
                nameof(StudentCivilIdFile),
                nameof(StudentNationalityCertificateFile),
                nameof(StudentBirthCertificateFile)
            });

            // Father documents (conditional on deceased status)
            if (FatherIsDeceased)
                requiredFiles.Add(nameof(FatherDeathCertificateFile));
            else
                requiredFiles.AddRange(new[] { nameof(FatherCivilIdFile), nameof(FatherNationalityCertificateFile) });

            // Mother documents (conditional on deceased status)
            if (MotherIsDeceased)
                requiredFiles.Add(nameof(MotherDeathCertificateFile));
            else
                requiredFiles.AddRange(new[] { nameof(MotherCivilIdFile), nameof(MotherNationalityCertificateFile) });
            break;

        case "B": // Father & Student Kuwaiti
            // Student documents (always required)
            requiredFiles.AddRange(new[]
            {
                nameof(StudentCivilIdFile),
                nameof(StudentNationalityCertificateFile),
                nameof(StudentBirthCertificateFile)
            });

            // Father documents (conditional on deceased status)
            if (FatherIsDeceased)
                requiredFiles.Add(nameof(FatherDeathCertificateFile));
            else
                requiredFiles.AddRange(new[] { nameof(FatherCivilIdFile), nameof(FatherNationalityCertificateFile) });
            break;

        case "C": // Mother Kuwaiti only
            // Student documents (Civil ID + Birth Cert only, no nationality cert needed)
            requiredFiles.AddRange(new[]
            {
                nameof(StudentCivilIdFile),
                nameof(StudentBirthCertificateFile)
            });

            // Mother documents (conditional on deceased status)
            if (MotherIsDeceased)
                requiredFiles.Add(nameof(MotherDeathCertificateFile));
            else
                requiredFiles.AddRange(new[] { nameof(MotherCivilIdFile), nameof(MotherNationalityCertificateFile) });
            break;

        case "D": // None Kuwaiti - No documents required (submission blocked)
            break;
    }

    return requiredFiles;
}
```

### Phase 2: UI Updates
**Files to Modify:**
- `Features/KuwaitiStudentInfo/Views/KuwaitiStudentInfo/CollectInfo.cshtml`

**Changes:**
1. Remove parent name input fields (Lines 218-222, 243-247)
2. Add birth certificate upload section in Step 2
3. Add deceased parent checkboxes in Step 1
4. Add conditional death certificate upload sections in Step 2

### Phase 3: JavaScript Updates
**Files to Modify:**
- `wwwroot/js/kuwaiti-student-info-wizard.js`

**Changes:**
1. Update `updateConditionalFields()` method to handle deceased parent logic
2. Add event handlers for deceased parent checkboxes
3. Update document count calculations
4. Remove any references to parent name fields

### Phase 4: Summary View Updates
**Files to Modify:**
- Summary view components and JavaScript summary population methods

**Changes:**
1. Remove parent name displays
2. Add birth certificate to document lists
3. Add death certificate displays when applicable

## Updated Document Requirements Matrix

| Case | Scenario | Required Documents | Count |
|------|----------|-------------------|-------|
| A | All Kuwaiti, no deceased | Student: Civil ID + Nationality Cert + Birth Cert<br/>Father: Civil ID + Nationality Cert<br/>Mother: Civil ID + Nationality Cert | 7 |
| A | All Kuwaiti, father deceased | Student: Civil ID + Nationality Cert + Birth Cert<br/>Father: Death Certificate (replaces Civil ID + Nationality Cert)<br/>Mother: Civil ID + Nationality Cert | 6 |
| A | All Kuwaiti, mother deceased | Student: Civil ID + Nationality Cert + Birth Cert<br/>Father: Civil ID + Nationality Cert<br/>Mother: Death Certificate (replaces Civil ID + Nationality Cert) | 6 |
| A | All Kuwaiti, both deceased | Student: Civil ID + Nationality Cert + Birth Cert<br/>Father: Death Certificate<br/>Mother: Death Certificate | 5 |
| B | Father+Student Kuwaiti, no deceased | Student: Civil ID + Nationality Cert + Birth Cert<br/>Father: Civil ID + Nationality Cert | 5 |
| B | Father+Student Kuwaiti, father deceased | Student: Civil ID + Nationality Cert + Birth Cert<br/>Father: Death Certificate (replaces Civil ID + Nationality Cert) | 4 |
| C | Mother Kuwaiti only, no deceased | Student: Civil ID + Birth Cert<br/>Mother: Civil ID + Nationality Cert | 4 |
| C | Mother Kuwaiti only, mother deceased | Student: Civil ID + Birth Cert<br/>Mother: Death Certificate (replaces Civil ID + Nationality Cert) | 3 |
| D | None Kuwaiti | Blocked - No documents | 0 |

## Validation Rules

### New Conditional Validation Logic
1. **Birth Certificate**: Always required for cases A, B, C
2. **Living Parent Documents**:
   - Father Civil ID + Nationality Cert: Required if `FatherIsKuwaiti == true` AND `FatherIsDeceased == false`
   - Mother Civil ID + Nationality Cert: Required if `MotherIsKuwaiti == true` AND `MotherIsDeceased == false`
3. **Death Certificates (Replacement Documents)**:
   - Father Death Certificate: Required if `FatherIsKuwaiti == true` AND `FatherIsDeceased == true`
   - Mother Death Certificate: Required if `MotherIsKuwaiti == true` AND `MotherIsDeceased == true`

### JavaScript Conditional Display Logic
1. **Deceased checkboxes**: Only show when corresponding parent is Kuwaiti
2. **Death certificate uploads**: Only show when corresponding parent is Kuwaiti AND deceased
3. **Birth certificate upload**: Always show for eligible cases (A, B, C)

## Testing Requirements

### Unit Tests to Add/Update
1. Test `GetRequiredFileUploads()` with all new scenarios (9 test cases)
2. Test `GetFileUploads()` includes new properties
3. Test validation logic for conditional death certificates
4. Test eligibility logic remains unchanged

### Integration Tests
1. Test form submission with birth certificates
2. Test form submission with death certificates
3. Test conditional UI display logic
4. Test document count validation

## Accessibility Considerations

1. **Labels**: All new form elements must have proper labels in English and Arabic
2. **ARIA attributes**: Death certificate sections need proper ARIA labeling
3. **Screen reader support**: Conditional sections must announce state changes
4. **Keyboard navigation**: All new checkboxes and file inputs must be keyboard accessible

## Migration Considerations

### Database Impact
- No database schema changes required (file uploads are handled separately)
- Existing data remains valid
- No migration scripts needed

### Backward Compatibility
- Existing form submissions remain valid
- No breaking changes to existing APIs
- Form handler can process both old and new submissions

## Success Criteria

1. ✅ Parent name fields completely removed from form
2. ✅ Birth certificate required and uploaded in all eligible cases
3. ✅ Deceased parent logic working with proper conditional display
4. ✅ Death certificates required and uploaded when applicable
5. ✅ All existing functionality preserved
6. ✅ Form validation working correctly
7. ✅ Accessibility standards maintained
8. ✅ Unit tests passing with new scenarios

## Implementation Priority

### High Priority (Must Have)
- Remove parent name fields
- Add universal birth certificate requirement
- Basic deceased parent logic

### Medium Priority (Should Have)
- Complete death certificate conditional logic
- Updated summary views
- Comprehensive testing

### Low Priority (Nice to Have)
- Enhanced UI animations for conditional sections
- Advanced accessibility features
- Performance optimizations

---

**Document Version**: 1.0
**Created**: December 2024
**Last Updated**: December 2024
**Status**: Ready for Implementation
