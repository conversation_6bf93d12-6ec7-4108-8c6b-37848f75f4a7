using Forms.ktech.Data;
using Forms.ktech.Shared.ViewModels;

namespace Forms.ktech.Services.SIS
{
    /// <summary>
    /// Interface for student lookup service that queries local SIS student data
    /// Provides methods for finding students and pre-filling form data
    /// </summary>
    public interface IStudentLookupService
    {
        #region Student Lookup Methods

        /// <summary>
        /// Gets a student by their email address (used for Azure AD matching)
        /// </summary>
        /// <param name="email">Student email address</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>SisStudent if found, null otherwise</returns>
        Task<SisStudent?> GetStudentByEmailAsync(string email, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets a student by their Student ID from SIS
        /// </summary>
        /// <param name="studentId">Student ID from SIS</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>SisStudent if found, null otherwise</returns>
        Task<SisStudent?> GetStudentByIdAsync(string studentId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets a student by their National ID (Civil ID)
        /// </summary>
        /// <param name="nationalId">National ID (Civil ID)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>SisStudent if found, null otherwise</returns>
        Task<SisStudent?> GetStudentByNationalIdAsync(string nationalId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Searches for students by name pattern (supports partial matching)
        /// </summary>
        /// <param name="namePattern">Name pattern to search for</param>
        /// <param name="maxResults">Maximum number of results to return</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of matching students</returns>
        Task<IEnumerable<SisStudent>> SearchStudentsByNameAsync(string namePattern, int maxResults = 10, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets multiple students by their email addresses (batch lookup)
        /// </summary>
        /// <param name="emails">List of email addresses</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Dictionary mapping emails to students (null if not found)</returns>
        Task<Dictionary<string, SisStudent?>> GetStudentsByEmailsAsync(IEnumerable<string> emails, CancellationToken cancellationToken = default);

        #endregion

        #region Data Freshness and Statistics

        /// <summary>
        /// Checks if the student data is fresh (within acceptable age)
        /// </summary>
        /// <param name="email">Student email to check</param>
        /// <param name="maxAge">Maximum acceptable age of data</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>True if data is fresh, false if stale or not found</returns>
        Task<bool> IsDataFreshAsync(string email, TimeSpan maxAge, CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the total number of students in the local database
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Total student count</returns>
        Task<int> GetTotalStudentCountAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the date of the last successful sync operation
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Last sync date, or null if no sync has occurred</returns>
        Task<DateTime?> GetLastSyncDateAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets data freshness statistics for monitoring
        /// </summary>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Data freshness statistics</returns>
        Task<DataFreshnessStats> GetDataFreshnessStatsAsync(CancellationToken cancellationToken = default);

        #endregion

        #region Form Pre-filling

        /// <summary>
        /// Pre-fills a form ViewModel with SIS student data using Azure AD email
        /// Uses student ID extraction from email (<EMAIL>) for reliable matching,
        /// with fallback to email-based lookup for backward compatibility
        /// </summary>
        /// <typeparam name="T">Type of ViewModel that implements ISisPreFillable</typeparam>
        /// <param name="viewModel">The ViewModel to pre-fill</param>
        /// <param name="userEmail">Azure AD email of the current user (preferably in format: <EMAIL>)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Pre-filled ViewModel with SIS data</returns>
        Task<T> PreFillFormAsync<T>(T viewModel, string userEmail, CancellationToken cancellationToken = default)
            where T : IFormViewModel, ISisPreFillable;

        /// <summary>
        /// Pre-fills a form ViewModel with specific student data
        /// </summary>
        /// <typeparam name="T">Type of ViewModel that implements ISisPreFillable</typeparam>
        /// <param name="viewModel">The ViewModel to pre-fill</param>
        /// <param name="student">The SIS student data to use for pre-filling</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Pre-filled ViewModel with SIS data</returns>
        Task<T> PreFillFormWithStudentAsync<T>(T viewModel, SisStudent student, CancellationToken cancellationToken = default) 
            where T : IFormViewModel, ISisPreFillable;

        /// <summary>
        /// Validates that pre-filled data is consistent with current SIS data
        /// </summary>
        /// <typeparam name="T">Type of ViewModel that implements ISisPreFillable</typeparam>
        /// <param name="viewModel">The ViewModel to validate</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Validation result with any inconsistencies found</returns>
        Task<PreFillValidationResult> ValidatePreFilledDataAsync<T>(T viewModel, CancellationToken cancellationToken = default) 
            where T : IFormViewModel, ISisPreFillable;

        #endregion
    }

    /// <summary>
    /// Interface for ViewModels that support SIS data pre-filling
    /// </summary>
    public interface ISisPreFillable
    {
        /// <summary>
        /// Maps SIS student data to the ViewModel properties
        /// </summary>
        /// <param name="student">SIS student data</param>
        void MapFromSisStudent(SisStudent student);

        /// <summary>
        /// Gets the list of field names that should be read-only when pre-filled from SIS
        /// </summary>
        /// <returns>List of read-only field names</returns>
        IEnumerable<string> GetReadOnlyFields();

        /// <summary>
        /// Gets the list of field names that were pre-filled from SIS data
        /// </summary>
        /// <returns>List of pre-filled field names</returns>
        IEnumerable<string> GetPreFilledFields();

        /// <summary>
        /// Indicates whether the form has been pre-filled with SIS data
        /// </summary>
        bool IsPreFilled { get; set; }

        /// <summary>
        /// Gets the SIS Student ID that was used for pre-filling (if any)
        /// </summary>
        string? PreFilledFromStudentId { get; set; }

        /// <summary>
        /// Gets the timestamp when the form was pre-filled
        /// </summary>
        DateTime? PreFilledAt { get; set; }
    }

    /// <summary>
    /// Data freshness statistics for monitoring
    /// </summary>
    public class DataFreshnessStats
    {
        public int TotalStudents { get; set; }
        public DateTime? LastSyncDate { get; set; }
        public TimeSpan? DataAge { get; set; }
        public int StudentsWithFreshData { get; set; }
        public int StudentsWithStaleData { get; set; }
        public double FreshDataPercentage { get; set; }
    }

    /// <summary>
    /// Result of pre-fill validation
    /// </summary>
    public class PreFillValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> Warnings { get; set; } = new();
        public List<string> Errors { get; set; } = new();
        public DateTime? LastSyncDate { get; set; }
        public bool DataIsStale { get; set; }
    }
}
