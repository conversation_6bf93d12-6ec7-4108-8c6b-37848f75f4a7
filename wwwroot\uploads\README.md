# File Upload Storage Directory

This directory contains all uploaded files for the forms.ktech solution, organized by form type and submission GUID.

## 📁 Directory Structure

```
/wwwroot/uploads/
├── KuwaitiStudentInfo/
│   └── {SubmissionGuid}/
│       ├── student_civil_id.{ext}
│       ├── student_nationality_certificate.{ext}
│       ├── father_civil_id.{ext}
│       ├── father_nationality_certificate.{ext}
│       ├── mother_civil_id.{ext}
│       └── mother_nationality_certificate.{ext}
├── {FutureFormName}/
│   └── {SubmissionGuid}/
│       └── {files...}
└── README.md (this file)
```

## 🔒 Security Features

- **GUID-based organization** prevents path traversal attacks
- **File type validation** by content, not just extension
- **Size limits** enforced (5MB default)
- **Secure file names** with sanitization
- **Access control** through controller authorization

## 📋 File Naming Conventions

### Kuwaiti Student Info Form
- `student_civil_id.{ext}` - Student's Civil ID document
- `student_nationality_certificate.{ext}` - Student's Nationality Certificate (if Kuwaiti)
- `father_civil_id.{ext}` - Father's Civil ID document (if Kuwaiti)
- `father_nationality_certificate.{ext}` - Father's Nationality Certificate (if Kuwaiti)
- `mother_civil_id.{ext}` - Mother's Civil ID document (if Kuwaiti)
- `mother_nationality_certificate.{ext}` - Mother's Nationality Certificate (if Kuwaiti)

## 🔧 File Management

Files are managed by the `FileUploadService` which provides:
- Automatic GUID folder creation
- File validation and security checks
- Cleanup for failed submissions
- Secure download streaming
- MIME type detection

## 🗂️ Backup and Maintenance

- Regular backups should be configured for this directory
- Old submissions can be archived based on retention policies
- Failed uploads are automatically cleaned up
- File integrity should be monitored

## ⚠️ Important Notes

- This directory should NOT be directly accessible via web requests
- All file access must go through authorized controller actions
- File paths are stored as relative paths in the database
- Never store absolute paths or expose internal directory structure
