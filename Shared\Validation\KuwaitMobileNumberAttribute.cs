using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace Forms.ktech.Shared.Validation
{
    /// <summary>
    /// Validation attribute for Kuwait mobile phone number format
    /// Accepts formats: +965XXXXXXXX, 965XXXXXXXX, or XXXXXXXX (8 digits)
    /// Valid Kuwait mobile prefixes: 5, 6, 9
    /// </summary>
    public class KuwaitMobileNumberAttribute : ValidationAttribute
    {
        private static readonly Regex KuwaitMobileRegex = new Regex(
            @"^(\+965|965)?[569]\d{7}$", 
            RegexOptions.Compiled | RegexOptions.IgnoreCase);

        public KuwaitMobileNumberAttribute()
        {
            ErrorMessage = "Please enter a valid Kuwait mobile number (e.g., +965XXXXXXXX or XXXXXXXX starting with 5, 6, or 9).";
        }

        /// <summary>
        /// Validates the Kuwait mobile number format
        /// </summary>
        /// <param name="value">The value to validate</param>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation result</returns>
        protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return ValidationResult.Success; // Let [Required] handle null/empty validation
            }

            var mobileNumber = value.ToString()!.Trim().Replace(" ", "").Replace("-", "");

            if (!KuwaitMobileRegex.IsMatch(mobileNumber))
            {
                return new ValidationResult(ErrorMessage, new[] { validationContext.MemberName });
            }

            return ValidationResult.Success;
        }

        /// <summary>
        /// Formats a Kuwait mobile number to a standard format
        /// </summary>
        /// <param name="mobileNumber">The mobile number to format</param>
        /// <returns>Formatted mobile number</returns>
        public static string FormatKuwaitMobileNumber(string mobileNumber)
        {
            if (string.IsNullOrWhiteSpace(mobileNumber))
                return string.Empty;

            var cleaned = mobileNumber.Trim().Replace(" ", "").Replace("-", "");
            
            // If it starts with +965 or 965, keep as is
            if (cleaned.StartsWith("+965"))
                return cleaned;
            
            if (cleaned.StartsWith("965"))
                return "+" + cleaned;
            
            // If it's 8 digits starting with 5, 6, or 9, add +965
            if (cleaned.Length == 8 && (cleaned.StartsWith("5") || cleaned.StartsWith("6") || cleaned.StartsWith("9")))
                return "+965" + cleaned;
            
            return cleaned;
        }
    }
}
