using Forms.ktech.Features.KuwaitiStudentInfo.Models;

namespace Forms.ktech.Services
{
    /// <summary>
    /// Interface for submission notification service
    /// </summary>
    public interface ISubmissionNotificationService
    {
        /// <summary>
        /// Sends approval notification to student
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <returns>Result indicating success or failure</returns>
        Task<NotificationResult> SendApprovalNotificationAsync(int submissionId);

        /// <summary>
        /// Sends rejection notification to student
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="reason">The reason for rejection</param>
        /// <returns>Result indicating success or failure</returns>
        Task<NotificationResult> SendRejectionNotificationAsync(int submissionId, string reason);

        /// <summary>
        /// Sends flagged notification to student (optional - may not be needed for students)
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="notes">Optional notes</param>
        /// <returns>Result indicating success or failure</returns>
        Task<NotificationResult> SendFlaggedNotificationAsync(int submissionId, string? notes = null);

        /// <summary>
        /// Generates approval email content
        /// </summary>
        /// <param name="submission">The submission</param>
        /// <returns>Email content</returns>
        Task<EmailContent> GenerateApprovalEmailAsync(StudentInfo submission);

        /// <summary>
        /// Generates rejection email content
        /// </summary>
        /// <param name="submission">The submission</param>
        /// <param name="reason">The reason for rejection</param>
        /// <returns>Email content</returns>
        Task<EmailContent> GenerateRejectionEmailAsync(StudentInfo submission, string reason);

        /// <summary>
        /// Queues submission notification for batch sending
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="notificationType">Type of notification</param>
        /// <param name="reason">Optional reason (for rejections)</param>
        /// <returns>Result indicating success or failure</returns>
        Task<NotificationResult> QueueSubmissionNotificationAsync(int submissionId, SubmissionNotificationType notificationType, string? reason = null);

        /// <summary>
        /// Gets pending submission notifications
        /// </summary>
        /// <returns>List of pending notifications</returns>
        Task<List<PendingSubmissionNotification>> GetPendingNotificationsAsync();

        /// <summary>
        /// Sends all pending submission notifications
        /// </summary>
        /// <returns>Results for each notification</returns>
        Task<List<NotificationResult>> SendPendingNotificationsAsync();
    }

    /// <summary>
    /// Types of submission notifications
    /// </summary>
    public enum SubmissionNotificationType
    {
        /// <summary>
        /// Submission approved notification
        /// </summary>
        Approved = 1,

        /// <summary>
        /// Submission rejected notification
        /// </summary>
        Rejected = 2,

        /// <summary>
        /// Submission flagged notification (internal use)
        /// </summary>
        Flagged = 3
    }

    /// <summary>
    /// Result of a notification operation
    /// </summary>
    public class NotificationResult
    {
        /// <summary>
        /// Whether the operation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Error message if the operation failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// The submission ID that was processed
        /// </summary>
        public int SubmissionId { get; set; }

        /// <summary>
        /// Type of notification
        /// </summary>
        public SubmissionNotificationType NotificationType { get; set; }

        /// <summary>
        /// Additional details about the operation
        /// </summary>
        public string? Details { get; set; }

        /// <summary>
        /// When the notification was sent
        /// </summary>
        public DateTime? SentDate { get; set; }

        /// <summary>
        /// Creates a successful result
        /// </summary>
        public static NotificationResult CreateSuccess(int submissionId, SubmissionNotificationType type, string? details = null)
        {
            return new NotificationResult
            {
                Success = true,
                SubmissionId = submissionId,
                NotificationType = type,
                Details = details,
                SentDate = DateTime.UtcNow
            };
        }

        /// <summary>
        /// Creates a failure result
        /// </summary>
        public static NotificationResult CreateFailure(int submissionId, SubmissionNotificationType type, string errorMessage)
        {
            return new NotificationResult
            {
                Success = false,
                SubmissionId = submissionId,
                NotificationType = type,
                ErrorMessage = errorMessage
            };
        }
    }

    /// <summary>
    /// Email content structure
    /// </summary>
    public class EmailContent
    {
        /// <summary>
        /// Email subject
        /// </summary>
        public string Subject { get; set; } = string.Empty;

        /// <summary>
        /// HTML email body
        /// </summary>
        public string HtmlBody { get; set; } = string.Empty;

        /// <summary>
        /// Plain text email body
        /// </summary>
        public string TextBody { get; set; } = string.Empty;

        /// <summary>
        /// Recipient email address
        /// </summary>
        public string ToEmail { get; set; } = string.Empty;

        /// <summary>
        /// Recipient name
        /// </summary>
        public string ToName { get; set; } = string.Empty;
    }

    /// <summary>
    /// Pending submission notification
    /// </summary>
    public class PendingSubmissionNotification
    {
        /// <summary>
        /// Unique identifier for the notification
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Submission ID
        /// </summary>
        public int SubmissionId { get; set; }

        /// <summary>
        /// Type of notification
        /// </summary>
        public SubmissionNotificationType NotificationType { get; set; }

        /// <summary>
        /// Reason for rejection (if applicable)
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// When the notification was queued
        /// </summary>
        public DateTime QueuedDate { get; set; }

        /// <summary>
        /// Who queued the notification
        /// </summary>
        public string? QueuedByUserId { get; set; }

        /// <summary>
        /// Student information
        /// </summary>
        public StudentInfo? Submission { get; set; }
    }
}
