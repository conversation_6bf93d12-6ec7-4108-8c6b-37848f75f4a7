﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Forms.ktech.Migrations
{
    /// <inheritdoc />
    public partial class AddDeceasedParentFields : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "SisStudents",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    StudentID = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    FullNameAR = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    FullNameEN = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Gender = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    Nationality = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    NationalID = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    BirthDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Email = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    MobileNo = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    EnrollmentStatus = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    Major = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Level = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    LastSyncDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DataHash = table.Column<string>(type: "nvarchar(64)", maxLength: 64, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SisStudents", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SyncHistories",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    SyncId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    SyncType = table.Column<int>(type: "int", nullable: false),
                    Status = table.Column<int>(type: "int", nullable: false),
                    StartTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    EndTime = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DurationMs = table.Column<long>(type: "bigint", nullable: true),
                    RecordsProcessed = table.Column<int>(type: "int", nullable: false),
                    RecordsAdded = table.Column<int>(type: "int", nullable: false),
                    RecordsUpdated = table.Column<int>(type: "int", nullable: false),
                    RecordsSkipped = table.Column<int>(type: "int", nullable: false),
                    RecordsErrored = table.Column<int>(type: "int", nullable: false),
                    ErrorMessage = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    ErrorDetails = table.Column<string>(type: "nvarchar(4000)", maxLength: 4000, nullable: true),
                    AdditionalDetails = table.Column<string>(type: "nvarchar(2000)", maxLength: 2000, nullable: true),
                    ProgressPercentage = table.Column<int>(type: "int", nullable: false),
                    CurrentStep = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    ApiEndpoint = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    BatchSize = table.Column<int>(type: "int", nullable: true),
                    TriggeredByUserId = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: true),
                    IsAutomated = table.Column<bool>(type: "bit", nullable: false),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    SubmissionGuid = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: true),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SyncHistories", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "StudentInfos",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    SisStudentId = table.Column<int>(type: "int", nullable: true),
                    StudentIsKuwaiti = table.Column<bool>(type: "bit", nullable: false),
                    FatherName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    FatherIsKuwaiti = table.Column<bool>(type: "bit", nullable: false),
                    FatherIsDeceased = table.Column<bool>(type: "bit", nullable: false),
                    MotherName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    MotherIsKuwaiti = table.Column<bool>(type: "bit", nullable: false),
                    MotherIsDeceased = table.Column<bool>(type: "bit", nullable: false),
                    StudentCivilIdPath = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    StudentNationalityCertificatePath = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    FatherCivilIdPath = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    FatherNationalityCertificatePath = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    MotherCivilIdPath = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    MotherNationalityCertificatePath = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    StudentBirthCertificatePath = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    FatherDeathCertificatePath = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    MotherDeathCertificatePath = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    SubmissionGuid = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    CreatedByUserId = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: true),
                    UpdatedByUserId = table.Column<string>(type: "nvarchar(450)", maxLength: 450, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StudentInfos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_StudentInfos_SisStudents_SisStudentId",
                        column: x => x.SisStudentId,
                        principalTable: "SisStudents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SisStudents_Email",
                table: "SisStudents",
                column: "Email");

            migrationBuilder.CreateIndex(
                name: "IX_SisStudents_LastSyncDate",
                table: "SisStudents",
                column: "LastSyncDate");

            migrationBuilder.CreateIndex(
                name: "IX_SisStudents_NationalID",
                table: "SisStudents",
                column: "NationalID");

            migrationBuilder.CreateIndex(
                name: "IX_SisStudents_StudentID",
                table: "SisStudents",
                column: "StudentID",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_StudentInfos_CreatedByUserId",
                table: "StudentInfos",
                column: "CreatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_StudentInfos_CreatedDate",
                table: "StudentInfos",
                column: "CreatedDate");

            migrationBuilder.CreateIndex(
                name: "IX_StudentInfos_SisStudentId",
                table: "StudentInfos",
                column: "SisStudentId");

            migrationBuilder.CreateIndex(
                name: "IX_StudentInfos_SubmissionGuid",
                table: "StudentInfos",
                column: "SubmissionGuid",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SyncHistories_CreatedDate",
                table: "SyncHistories",
                column: "CreatedDate");

            migrationBuilder.CreateIndex(
                name: "IX_SyncHistories_StartTime",
                table: "SyncHistories",
                column: "StartTime");

            migrationBuilder.CreateIndex(
                name: "IX_SyncHistories_Status",
                table: "SyncHistories",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_SyncHistories_SubmissionGuid",
                table: "SyncHistories",
                column: "SubmissionGuid",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SyncHistories_SyncId",
                table: "SyncHistories",
                column: "SyncId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SyncHistories_TriggeredBy",
                table: "SyncHistories",
                column: "TriggeredByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_SyncHistories_TypeStatus",
                table: "SyncHistories",
                columns: new[] { "SyncType", "Status" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "StudentInfos");

            migrationBuilder.DropTable(
                name: "SyncHistories");

            migrationBuilder.DropTable(
                name: "SisStudents");
        }
    }
}
