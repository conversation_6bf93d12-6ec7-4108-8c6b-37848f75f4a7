using Forms.ktech.Data;
using Forms.ktech.Features.KuwaitiStudentInfo.Models;
using Forms.ktech.Services;
using Microsoft.EntityFrameworkCore;

namespace Forms.ktech.Features.KuwaitiStudentInfo.Services
{
    /// <summary>
    /// Specialized form handler for the Kuwaiti Student Information form
    /// Implements specific business logic for eligibility validation and file handling
    /// </summary>
    public class KuwaitiStudentInfoHandler : FormHandler<StudentFormViewModel, StudentInfo>
    {
        public KuwaitiStudentInfoHandler(
            FormsKTechContext context,
            IFileUploadService fileUploadService,
            ILogger<KuwaitiStudentInfoHandler> logger)
            : base(context, fileUploadService, logger)
        {
        }

        #region Eligibility Logic Override

        /// <summary>
        /// Implements the specific eligibility logic for Kuwaiti Student Information form
        /// Case D (no Kuwaiti parent/student) = ineligible
        /// Cases A, B, C = eligible
        /// </summary>
        /// <param name="viewModel">The form ViewModel to validate</param>
        /// <returns>True if the submission should be rejected (Case D), false if it can proceed (Cases A, B, C)</returns>
        public override bool IsNotEligible(StudentFormViewModel viewModel)
        {
            // Case D: None are Kuwaiti (ineligible)
            return viewModel.GetEligibilityCase() == "D";
        }

        /// <summary>
        /// Gets the specific ineligibility reason for the Kuwaiti Student Information form
        /// </summary>
        /// <param name="viewModel">The form ViewModel to check</param>
        /// <returns>Error message explaining why the form is not eligible, or null if eligible</returns>
        public override string? GetIneligibilityReason(StudentFormViewModel viewModel)
        {
            if (IsNotEligible(viewModel))
            {
                return "To be eligible for this program, at least one person (student, father, or mother) must be a Kuwaiti citizen. " +
                       "Since none of the individuals listed are Kuwaiti citizens, this application cannot proceed.";
            }
            return null;
        }

        #endregion

        #region Entity Mapping

        /// <summary>
        /// Maps a StudentFormViewModel to a StudentInfo entity
        /// </summary>
        /// <param name="viewModel">The ViewModel to map</param>
        /// <param name="entity">The existing entity to update, or null to create new</param>
        /// <returns>The mapped entity</returns>
        public override StudentInfo MapToEntity(StudentFormViewModel viewModel, StudentInfo? entity = null)
        {
            var isUpdate = entity != null;
            var existingSisStudentId = entity?.SisStudentId; // Preserve existing SIS link

            entity ??= new StudentInfo();

            // Map basic properties
            entity.StudentMobileNumber = viewModel.StudentMobileNumber;
            entity.StudentIsKuwaiti = viewModel.StudentIsKuwaiti;

            // Parent information
            entity.FatherIsKuwaiti = viewModel.FatherIsKuwaiti;
            entity.FatherIsDeceased = viewModel.FatherIsDeceased;
            entity.MotherIsKuwaiti = viewModel.MotherIsKuwaiti;
            entity.MotherIsDeceased = viewModel.MotherIsDeceased;

            // Handle SIS student linking
            if (isUpdate)
            {
                // For updates, preserve the existing SIS relationship
                // The SIS link should not change during form edits
                entity.SisStudentId = existingSisStudentId;
                _logger.LogDebug("Preserved existing SIS student ID {SisStudentId} during update", existingSisStudentId);
            }
            else
            {
                // For new entities, link to SIS student if available
                if (viewModel.IsPreFilled && !string.IsNullOrWhiteSpace(viewModel.PreFilledFromStudentId))
                {
                    // Try to parse the SIS student database ID for the foreign key
                    if (int.TryParse(viewModel.PreFilledFromStudentId, out int sisStudentId))
                    {
                        entity.SisStudentId = sisStudentId;
                        _logger.LogDebug("Linked new entity to SIS student ID {SisStudentId}", sisStudentId);
                    }
                    else
                    {
                        _logger.LogWarning("Failed to parse SIS StudentID '{StudentId}' to integer", viewModel.PreFilledFromStudentId);
                        entity.SisStudentId = null;
                    }
                }
                else
                {
                    // No SIS link available - this is a pure manual entry
                    entity.SisStudentId = null;
                    _logger.LogDebug("No SIS student link available for Civil ID {CivilId}", viewModel.StudentCivilId);
                }
            }

            // Note: File paths will be set by the base class ApplyFilePathsToEntity method
            // after files are uploaded

            return entity;
        }

        /// <summary>
        /// Maps a StudentInfo entity to a StudentFormViewModel
        /// </summary>
        /// <param name="entity">The entity to map</param>
        /// <returns>The mapped ViewModel</returns>
        public override StudentFormViewModel MapToViewModel(StudentInfo entity)
        {
            var viewModel = new StudentFormViewModel
            {
                SubmissionGuid = entity.SubmissionGuid,

                StudentName = entity.GetStudentName(),
                StudentCivilId = entity.GetStudentCivilId(),
                StudentMobileNumber = entity.StudentMobileNumber,
                StudentIsKuwaiti = entity.StudentIsKuwaiti,

                FatherIsKuwaiti = entity.FatherIsKuwaiti,
                FatherIsDeceased = entity.FatherIsDeceased,
                MotherIsKuwaiti = entity.MotherIsKuwaiti,
                MotherIsDeceased = entity.MotherIsDeceased

                // Note: File upload properties (IFormFile) are not mapped from entity
                // as they represent new uploads, not existing files
            };

            // Set SIS pre-fill metadata if linked to SIS student
            if (entity.SisStudentId.HasValue && entity.SisStudent != null)
            {
                viewModel.IsPreFilled = true;
                viewModel.PreFilledFromStudentId = entity.SisStudentId.Value.ToString();
                viewModel.PreFilledAt = entity.CreatedDate; // Use creation date as approximation

                _logger.LogDebug("Mapped entity to ViewModel with SIS information: SisStudentId={SisStudentId}, NationalID={NationalID}",
                    entity.SisStudentId, entity.SisStudent.NationalID);
            }
            else
            {
                _logger.LogDebug("Mapped entity to ViewModel without SIS information: SisStudentId={SisStudentId}",
                    entity.SisStudentId);
            }

            return viewModel;
        }

        #endregion

        #region File Handling Customization

        /// <summary>
        /// Generates standardized file names for Kuwaiti Student Information form
        /// </summary>
        /// <param name="propertyName">The property name from the ViewModel</param>
        /// <param name="originalFileName">The original file name</param>
        /// <returns>The generated file name</returns>
        protected override string GenerateFileName(string propertyName, string originalFileName)
        {
            var extension = Path.GetExtension(originalFileName);

            // Map property names to standardized file names
            var fileName = propertyName switch
            {
                nameof(StudentFormViewModel.StudentCivilIdFile) => "student_civil_id",
                nameof(StudentFormViewModel.StudentNationalityCertificateFile) => "student_nationality_certificate",
                nameof(StudentFormViewModel.StudentBirthCertificateFile) => "student_birth_certificate",
                nameof(StudentFormViewModel.FatherCivilIdFile) => "father_civil_id",
                nameof(StudentFormViewModel.FatherNationalityCertificateFile) => "father_nationality_certificate",
                nameof(StudentFormViewModel.FatherDeathCertificateFile) => "father_death_certificate",
                nameof(StudentFormViewModel.MotherCivilIdFile) => "mother_civil_id",
                nameof(StudentFormViewModel.MotherNationalityCertificateFile) => "mother_nationality_certificate",
                nameof(StudentFormViewModel.MotherDeathCertificateFile) => "mother_death_certificate",
                _ => propertyName.Replace("File", "").ToLowerInvariant()
            };

            return $"{fileName}{extension}";
        }

        /// <summary>
        /// Applies uploaded file paths to the StudentInfo entity with type safety
        /// </summary>
        /// <param name="entity">The StudentInfo entity to update</param>
        /// <param name="filePaths">Dictionary of property names and file paths</param>
        protected override void ApplyFilePathsToEntity(StudentInfo entity, Dictionary<string, string> filePaths)
        {
            // Type-safe mapping of file paths to entity properties
            foreach (var kvp in filePaths)
            {
                switch (kvp.Key)
                {
                    case nameof(StudentFormViewModel.StudentCivilIdFile):
                        entity.StudentCivilIdPath = kvp.Value;
                        break;
                    case nameof(StudentFormViewModel.StudentNationalityCertificateFile):
                        entity.StudentNationalityCertificatePath = kvp.Value;
                        break;
                    case nameof(StudentFormViewModel.StudentBirthCertificateFile):
                        entity.StudentBirthCertificatePath = kvp.Value;
                        break;
                    case nameof(StudentFormViewModel.FatherCivilIdFile):
                        entity.FatherCivilIdPath = kvp.Value;
                        break;
                    case nameof(StudentFormViewModel.FatherNationalityCertificateFile):
                        entity.FatherNationalityCertificatePath = kvp.Value;
                        break;
                    case nameof(StudentFormViewModel.FatherDeathCertificateFile):
                        entity.FatherDeathCertificatePath = kvp.Value;
                        break;
                    case nameof(StudentFormViewModel.MotherCivilIdFile):
                        entity.MotherCivilIdPath = kvp.Value;
                        break;
                    case nameof(StudentFormViewModel.MotherNationalityCertificateFile):
                        entity.MotherNationalityCertificatePath = kvp.Value;
                        break;
                    case nameof(StudentFormViewModel.MotherDeathCertificateFile):
                        entity.MotherDeathCertificatePath = kvp.Value;
                        break;
                    default:
                        _logger.LogWarning("Unknown file property: {PropertyName}", kvp.Key);
                        break;
                }
            }
        }

        #endregion

        #region Business Logic Helpers

        /// <summary>
        /// Gets a detailed description of the eligibility case for logging and reporting
        /// </summary>
        /// <param name="viewModel">The form ViewModel</param>
        /// <returns>Detailed description of the eligibility case</returns>
        public string GetEligibilityCaseDescription(StudentFormViewModel viewModel)
        {
            return viewModel.GetEligibilityCase() switch
            {
                "A" => "Case A: Student is Kuwaiti citizen",
                "B" => "Case B: Father is Kuwaiti citizen (student is not)",
                "C" => "Case C: Mother is Kuwaiti citizen (student and father are not)",
                "D" => "Case D: No Kuwaiti citizens (ineligible)",
                _ => "Unknown case"
            };
        }

        /// <summary>
        /// Validates that all required files are provided based on the eligibility case
        /// </summary>
        /// <param name="viewModel">The form ViewModel to validate</param>
        /// <returns>List of missing required files, empty if all required files are present</returns>
        public List<string> ValidateRequiredFiles(StudentFormViewModel viewModel)
        {
            var missingFiles = new List<string>();
            var requiredFiles = viewModel.GetRequiredFileUploads();
            var providedFiles = viewModel.GetFileUploads();

            foreach (var requiredFile in requiredFiles)
            {
                if (!providedFiles.ContainsKey(requiredFile) ||
                    providedFiles[requiredFile] == null ||
                    providedFiles[requiredFile]!.Length == 0)
                {
                    missingFiles.Add(requiredFile);
                }
            }

            return missingFiles;
        }

        #endregion

        #region Single Submission Rule Implementation

        /// <summary>
        /// Extracts Civil ID from the StudentFormViewModel
        /// </summary>
        /// <param name="viewModel">The ViewModel to extract Civil ID from</param>
        /// <returns>Civil ID string from the ViewModel</returns>
        protected override string GetCivilIdFromViewModel(StudentFormViewModel viewModel)
        {
            return viewModel?.StudentCivilId ?? string.Empty;
        }

        /// <summary>
        /// Checks if a user has already submitted a form for a specific Civil ID
        /// Uses secure authorization by joining StudentInfo with SisStudent and filtering by user
        /// </summary>
        /// <param name="civilId">The Civil ID to check</param>
        /// <param name="userId">The ID of the user (for authorization)</param>
        /// <returns>The existing submission if found, null otherwise</returns>
        public override async Task<StudentInfo?> GetExistingSubmissionByCivilIdAsync(string civilId, string? userId)
        {
            if (string.IsNullOrWhiteSpace(civilId) || string.IsNullOrWhiteSpace(userId))
            {
                _logger.LogWarning("GetExistingSubmissionByCivilIdAsync called with null/empty parameters. CivilId: {CivilId}, UserId: {UserId}",
                    civilId, userId);
                return null;
            }

            try
            {
                _logger.LogInformation("Checking for existing submission for Civil ID: {CivilId}, User: {UserId}",
                    civilId, userId);

                // Query StudentInfo to find submissions by Civil ID through SIS relationship
                // Include strict user authorization to prevent cross-user data access
                var existingSubmission = await _context.StudentInfos
                    .Include(si => si.SisStudent)
                    .Where(si => si.CreatedByUserId == userId && !si.IsDeleted &&
                                si.SisStudent != null && si.SisStudent.NationalID == civilId)
                    .FirstOrDefaultAsync();

                if (existingSubmission != null)
                {
                    _logger.LogInformation("Found existing submission {EntityId} for Civil ID: {CivilId}, User: {UserId}",
                        existingSubmission.Id, civilId, userId);
                }
                else
                {
                    _logger.LogInformation("No existing submission found for Civil ID: {CivilId}, User: {UserId}",
                        civilId, userId);
                }

                return existingSubmission;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking for existing submission. Civil ID: {CivilId}, User: {UserId}",
                    civilId, userId);
                throw;
            }
        }

        #endregion

        #region Update Override for SIS Relationship Preservation

        /// <summary>
        /// Enhanced update method that preserves SIS student relationships
        /// </summary>
        /// <param name="id">The ID of the submission to update</param>
        /// <param name="viewModel">The updated ViewModel data</param>
        /// <param name="formName">The name of the form</param>
        /// <param name="userId">The ID of the user performing the update</param>
        /// <returns>True if successful, false otherwise</returns>
        public override async Task<bool> UpdateAsync(int id, StudentFormViewModel viewModel, string formName, string? userId = null)
        {
            try
            {
                _logger.LogInformation("Starting update for StudentInfo {EntityId} by user {UserId}", id, userId);

                // Get the existing entity with SIS relationship
                var existingEntity = await _context.StudentInfos
                    .Include(si => si.SisStudent)
                    .FirstOrDefaultAsync(si => si.Id == id && si.CreatedByUserId == userId && !si.IsDeleted);

                if (existingEntity == null)
                {
                    _logger.LogWarning("StudentInfo {EntityId} not found or not authorized for user {UserId}", id, userId);
                    return false;
                }

                // Log the existing SIS relationship for debugging
                _logger.LogInformation("Existing entity SisStudentId: {SisStudentId}, SisStudent.NationalID: {NationalID}",
                    existingEntity.SisStudentId, existingEntity.SisStudent?.NationalID);

                // Ensure the ViewModel has the SIS information for proper mapping
                if (existingEntity.SisStudentId.HasValue && existingEntity.SisStudent != null)
                {
                    viewModel.IsPreFilled = true;
                    viewModel.PreFilledFromStudentId = existingEntity.SisStudentId.Value.ToString();
                    viewModel.PreFilledAt = existingEntity.CreatedDate;
                    _logger.LogDebug("Restored SIS information to ViewModel for update");
                }

                // Call the base UpdateAsync method which will use our overridden MapToEntity
                var result = await base.UpdateAsync(id, viewModel, formName, userId);

                if (result)
                {
                    _logger.LogInformation("StudentInfo {EntityId} updated successfully, SIS relationship preserved", id);
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating StudentInfo {EntityId} for user {UserId}", id, userId);
                return false;
            }
        }

        #endregion

        #region Logging Enhancement

        /// <summary>
        /// Enhanced save method with detailed logging for Kuwaiti Student Information form
        /// </summary>
        /// <param name="viewModel">The form ViewModel containing the data</param>
        /// <param name="formName">The name of the form (should be "KuwaitiStudentInfo")</param>
        /// <param name="userId">The ID of the user submitting the form</param>
        /// <returns>The ID of the created entity</returns>
        public override async Task<int> SaveAsync(StudentFormViewModel viewModel, string formName, string? userId = null)
        {
            var eligibilityCase = GetEligibilityCaseDescription(viewModel);
            _logger.LogInformation("Processing Kuwaiti Student Information form submission. {EligibilityCase}, User: {UserId}",
                eligibilityCase, userId);

            // Validate required files before processing
            var missingFiles = ValidateRequiredFiles(viewModel);
            if (missingFiles.Count > 0)
            {
                var missingFilesList = string.Join(", ", missingFiles);
                _logger.LogWarning("Missing required files for submission: {MissingFiles}", missingFilesList);
                throw new InvalidOperationException($"Missing required files: {missingFilesList}");
            }

            // Link to SIS student by Civil ID if not already linked
            await EnsureSisStudentLinkAsync(viewModel);

            var entityId = await base.SaveAsync(viewModel, formName, userId);

            _logger.LogInformation("Kuwaiti Student Information form saved successfully. Entity ID: {EntityId}, {EligibilityCase}",
                entityId, eligibilityCase);

            return entityId;
        }

        /// <summary>
        /// Ensures that the ViewModel is linked to a SIS student record if one exists for the Civil ID
        /// This is crucial for the single submission rule to work correctly
        /// </summary>
        /// <param name="viewModel">The form ViewModel</param>
        private async Task EnsureSisStudentLinkAsync(StudentFormViewModel viewModel)
        {
            // Skip if already linked from SIS pre-fill
            if (viewModel.IsPreFilled && !string.IsNullOrWhiteSpace(viewModel.PreFilledFromStudentId))
            {
                return;
            }

            // Try to find SIS student by Civil ID
            if (!string.IsNullOrWhiteSpace(viewModel.StudentCivilId))
            {
                try
                {
                    var sisStudent = await _context.SisStudents
                        .AsNoTracking()
                        .FirstOrDefaultAsync(s => s.NationalID == viewModel.StudentCivilId);

                    if (sisStudent != null)
                    {
                        _logger.LogInformation("Found SIS student {StudentId} for Civil ID {CivilId}, linking to form submission",
                            sisStudent.StudentID, viewModel.StudentCivilId);

                        // Update ViewModel to reflect SIS link
                        viewModel.IsPreFilled = true;
                        viewModel.PreFilledFromStudentId = sisStudent.Id.ToString(); // Use database ID, not StudentID
                        viewModel.PreFilledAt = DateTime.UtcNow;
                    }
                    else
                    {
                        _logger.LogInformation("No SIS student found for Civil ID {CivilId}, proceeding with manual entry",
                            viewModel.StudentCivilId);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error looking up SIS student for Civil ID {CivilId}", viewModel.StudentCivilId);
                    // Continue without SIS link - don't fail the submission
                }
            }
        }

        #endregion
    }
}
