using Microsoft.EntityFrameworkCore;
using Forms.ktech.Data;
using Forms.ktech.Models;
using Forms.ktech.Features.KuwaitiStudentInfo.Models;

namespace Forms.ktech.Services
{
    /// <summary>
    /// Service for managing document approval operations
    /// </summary>
    public class DocumentApprovalService : IDocumentApprovalService
    {
        private readonly FormsKTechContext _context;
        private readonly ILogger<DocumentApprovalService> _logger;
        private readonly IEmailNotificationService _emailService;

        public DocumentApprovalService(
            FormsKTechContext context,
            ILogger<DocumentApprovalService> logger,
            IEmailNotificationService emailService)
        {
            _context = context;
            _logger = logger;
            _emailService = emailService;
        }

        /// <summary>
        /// Approves a document for a submission
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="documentType">The document type</param>
        /// <param name="approvedByUserId">The admin user ID who approved the document (can be null)</param>
        /// <param name="comments">Optional comments</param>
        /// <returns>The approval record</returns>
        public async Task<DocumentApproval> ApproveDocumentAsync(int submissionId, string documentType, string? approvedByUserId, string? comments = null)
        {
            try
            {
                _logger.LogInformation("Approving document {DocumentType} for submission {SubmissionId} by user {UserId}",
                    documentType, submissionId, approvedByUserId);

                // Check if there's an existing approval record
                var existingApproval = await _context.DocumentApprovals
                    .FirstOrDefaultAsync(da => da.SubmissionId == submissionId && da.DocumentType == documentType);

                DocumentApproval approval;

                if (existingApproval != null)
                {
                    // Update existing record
                    existingApproval.Status = DocumentApprovalStatus.Approved;
                    existingApproval.ApprovedByUserId = approvedByUserId; // This can be null now
                    existingApproval.ApprovalDate = DateTime.UtcNow;
                    existingApproval.Comments = comments;
                    existingApproval.DisapprovalReason = null; // Clear any previous disapproval reason
                    existingApproval.MarkAsUpdated();

                    approval = existingApproval;
                }
                else
                {
                    // Create new approval record
                    approval = new DocumentApproval
                    {
                        SubmissionId = submissionId,
                        DocumentType = documentType,
                        Status = DocumentApprovalStatus.Approved,
                        ApprovedByUserId = approvedByUserId, // This can be null now
                        ApprovalDate = DateTime.UtcNow,
                        Comments = comments
                    };

                    _context.DocumentApprovals.Add(approval);
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("Document {DocumentType} approved for submission {SubmissionId}",
                    documentType, submissionId);

                return approval;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving document {DocumentType} for submission {SubmissionId}",
                    documentType, submissionId);
                throw;
            }
        }

        /// <summary>
        /// Disapproves a document for a submission
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="documentType">The document type</param>
        /// <param name="disapprovedByUserId">The admin user ID who disapproved the document (can be null)</param>
        /// <param name="reason">The reason for disapproval</param>
        /// <param name="comments">Optional additional comments</param>
        /// <returns>The approval record</returns>
        public async Task<DocumentApproval> DisapproveDocumentAsync(int submissionId, string documentType, string? disapprovedByUserId, string reason, string? comments = null)
        {
            try
            {
                _logger.LogInformation("Disapproving document {DocumentType} for submission {SubmissionId} by user {UserId}",
                    documentType, submissionId, disapprovedByUserId);

                // Check if there's an existing approval record
                var existingApproval = await _context.DocumentApprovals
                    .FirstOrDefaultAsync(da => da.SubmissionId == submissionId && da.DocumentType == documentType);

                DocumentApproval approval;

                if (existingApproval != null)
                {
                    // Update existing record
                    existingApproval.Status = DocumentApprovalStatus.Disapproved;
                    existingApproval.ApprovedByUserId = disapprovedByUserId; // This can be null now
                    existingApproval.ApprovalDate = DateTime.UtcNow;
                    existingApproval.DisapprovalReason = reason;
                    existingApproval.Comments = comments;
                    existingApproval.MarkAsUpdated();

                    approval = existingApproval;
                }
                else
                {
                    // Create new approval record
                    approval = new DocumentApproval
                    {
                        SubmissionId = submissionId,
                        DocumentType = documentType,
                        Status = DocumentApprovalStatus.Disapproved,
                        ApprovedByUserId = disapprovedByUserId, // This can be null now
                        ApprovalDate = DateTime.UtcNow,
                        DisapprovalReason = reason,
                        Comments = comments
                    };

                    _context.DocumentApprovals.Add(approval);
                }

                await _context.SaveChangesAsync();

                _logger.LogInformation("Document {DocumentType} disapproved for submission {SubmissionId}",
                    documentType, submissionId);

                // Note: Email notification will be sent manually by admin through batch preview system
                _logger.LogInformation("Document disapproval queued for email notification review for submission {SubmissionId}", submissionId);

                return approval;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disapproving document {DocumentType} for submission {SubmissionId}",
                    documentType, submissionId);
                throw;
            }
        }

        /// <summary>
        /// Gets the approval status for a document
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="documentType">The document type</param>
        /// <returns>The approval record or null if not found</returns>
        public async Task<DocumentApproval?> GetDocumentApprovalAsync(int submissionId, string documentType)
        {
            return await _context.DocumentApprovals
                .Include(da => da.ApprovedByUser)
                .FirstOrDefaultAsync(da => da.SubmissionId == submissionId && da.DocumentType == documentType);
        }

        /// <summary>
        /// Gets all approval records for a submission
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <returns>List of approval records</returns>
        public async Task<List<DocumentApproval>> GetSubmissionApprovalsAsync(int submissionId)
        {
            return await _context.DocumentApprovals
                .Include(da => da.ApprovedByUser)
                .Where(da => da.SubmissionId == submissionId)
                .OrderBy(da => da.DocumentType)
                .ThenByDescending(da => da.CreatedDate)
                .ToListAsync();
        }

        /// <summary>
        /// Gets approval history for a specific document
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="documentType">The document type</param>
        /// <returns>List of approval history records</returns>
        public async Task<List<DocumentApproval>> GetDocumentApprovalHistoryAsync(int submissionId, string documentType)
        {
            return await _context.DocumentApprovals
                .Include(da => da.ApprovedByUser)
                .Where(da => da.SubmissionId == submissionId && da.DocumentType == documentType)
                .OrderByDescending(da => da.CreatedDate)
                .ToListAsync();
        }

        /// <summary>
        /// Bulk approve multiple documents for a submission
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="documentTypes">The document types to approve</param>
        /// <param name="approvedByUserId">The admin user ID who approved the documents (can be null)</param>
        /// <param name="comments">Optional comments</param>
        /// <returns>List of approval records</returns>
        public async Task<List<DocumentApproval>> BulkApproveDocumentsAsync(int submissionId, string[] documentTypes, string? approvedByUserId, string? comments = null)
        {
            var approvals = new List<DocumentApproval>();

            foreach (var documentType in documentTypes)
            {
                var approval = await ApproveDocumentAsync(submissionId, documentType, approvedByUserId, comments);
                approvals.Add(approval);
            }

            return approvals;
        }

        /// <summary>
        /// Gets approval statistics for the admin dashboard
        /// </summary>
        /// <returns>Approval statistics</returns>
        public async Task<DocumentApprovalStatistics> GetApprovalStatisticsAsync()
        {
            var totalApprovals = await _context.DocumentApprovals.CountAsync();
            var pendingApprovals = await _context.DocumentApprovals.CountAsync(da => da.Status == DocumentApprovalStatus.Pending);
            var approvedCount = await _context.DocumentApprovals.CountAsync(da => da.Status == DocumentApprovalStatus.Approved);
            var disapprovedCount = await _context.DocumentApprovals.CountAsync(da => da.Status == DocumentApprovalStatus.Disapproved);
            var underReviewCount = await _context.DocumentApprovals.CountAsync(da => da.Status == DocumentApprovalStatus.UnderReview);

            // Calculate average processing time
            var processedApprovals = await _context.DocumentApprovals
                .Where(da => da.ApprovalDate.HasValue)
                .Select(da => new { da.CreatedDate, da.ApprovalDate })
                .ToListAsync();

            var averageProcessingTimeHours = processedApprovals.Any()
                ? processedApprovals.Average(pa => (pa.ApprovalDate!.Value - pa.CreatedDate).TotalHours)
                : 0;

            return new DocumentApprovalStatistics
            {
                TotalApprovals = totalApprovals,
                PendingApprovals = pendingApprovals,
                ApprovedCount = approvedCount,
                DisapprovedCount = disapprovedCount,
                UnderReviewCount = underReviewCount,
                ApprovalRate = totalApprovals > 0 ? (double)approvedCount / totalApprovals * 100 : 0,
                AverageProcessingTimeHours = averageProcessingTimeHours
            };
        }
    }


}
