@model Forms.ktech.ViewModels.Admin.SubmissionDetailViewModel
@using Forms.ktech.Features.KuwaitiStudentInfo.Models

@{
    ViewData["Title"] = $"Submission Details - #{Model.Submission.Id}";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@section Breadcrumb {
    <li class="inline-flex items-center">
        <a asp-area="" asp-controller="Home" asp-action="Index" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
            <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
            </svg>
            Home
        </a>
    </li>
    <li>
        <div class="flex items-center">
            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
            </svg>
            <a asp-controller="Admin" asp-action="Dashboard" class="ms-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ms-2 dark:text-gray-400 dark:hover:text-white">Admin Dashboard</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
            </svg>
            <a asp-controller="Admin" asp-action="Submissions" class="ms-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ms-2 dark:text-gray-400 dark:hover:text-white">Submissions</a>
        </div>
    </li>
    <li>
        <div class="flex items-center">
            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
            </svg>
            <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400">View Submission #@Model.Submission.Id</span>
        </div>
    </li>
}

<!-- Page Header -->
<div class="bg-white rounded-lg shadow-lg p-6 mb-6 dark:bg-gray-800">
    <div class="flex items-center justify-between">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white">
                    <i class="fas fa-file-alt text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                    Submission Details - #@Model.Submission.Id
                </h1>
                <p class="text-gray-600 dark:text-gray-300">
                    Submitted by @Model.Submission.GetStudentName() on @Model.Submission.CreatedDate.ToString("MMMM dd, yyyy 'at' HH:mm")
                </p>
            </div>
        </div>
        <div class="flex items-center space-x-3">
            <!-- Submission Status Badge -->
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium @Model.Submission.GetStatusCssClass()">
                <i class="fas @(Model.Submission.Status switch {
                    SubmissionStatus.Pending => "fa-clock",
                    SubmissionStatus.Approved => "fa-check-circle",
                    SubmissionStatus.Rejected => "fa-times-circle",
                    SubmissionStatus.Flagged => "fa-flag",
                    _ => "fa-question-circle"
                }) me-2"></i>
                @Model.Submission.GetStatusDisplayText()
            </span>

            <!-- Eligibility Badge -->
            @{
                var eligibilityCase = Model.Submission.GetEligibilityCase();
                var badgeClass = eligibilityCase switch
                {
                    "A" => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
                    "B" => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
                    "C" => "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
                    "D" => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
                    _ => "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
                };
            }
            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium @badgeClass">
                <i class="fas fa-check-circle me-2"></i>
                Case @eligibilityCase - @(Model.Submission.IsEligible() ? "Eligible" : "Not Eligible")
            </span>
            
            <!-- Return Button -->
            <a href="@Model.NavigationContext.ReturnUrl" class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-4 focus:ring-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700">
                <i class="fas fa-arrow-left me-2"></i>
                @Model.NavigationContext.ReturnText
            </a>
        </div>
    </div>

    @if (Model.Submission.Status != SubmissionStatus.Pending)
    {
        <!-- Status Details -->
        <div class="mt-4 p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                @if (Model.Submission.Status == SubmissionStatus.Approved)
                {
                    <div>
                        <span class="font-medium text-gray-700 dark:text-gray-300">Approved Date:</span>
                        <span class="text-gray-900 dark:text-white">@Model.Submission.ApprovedDate?.ToString("MMMM dd, yyyy 'at' h:mm tt")</span>
                    </div>
                }
                else if (Model.Submission.Status == SubmissionStatus.Rejected)
                {
                    <div>
                        <span class="font-medium text-gray-700 dark:text-gray-300">Rejected Date:</span>
                        <span class="text-gray-900 dark:text-white">@Model.Submission.RejectedDate?.ToString("MMMM dd, yyyy 'at' h:mm tt")</span>
                    </div>
                    @if (!string.IsNullOrEmpty(Model.Submission.RejectionReason))
                    {
                        <div class="md:col-span-2">
                            <span class="font-medium text-gray-700 dark:text-gray-300">Rejection Reason:</span>
                            <span class="text-gray-900 dark:text-white">@Model.Submission.RejectionReason</span>
                        </div>
                    }
                }
                else if (Model.Submission.Status == SubmissionStatus.Flagged)
                {
                    <div>
                        <span class="font-medium text-gray-700 dark:text-gray-300">Flagged Date:</span>
                        <span class="text-gray-900 dark:text-white">@Model.Submission.FlaggedDate?.ToString("MMMM dd, yyyy 'at' h:mm tt")</span>
                    </div>
                    @if (!string.IsNullOrEmpty(Model.Submission.FlagNotes))
                    {
                        <div class="md:col-span-2">
                            <span class="font-medium text-gray-700 dark:text-gray-300">Flag Notes:</span>
                            <span class="text-gray-900 dark:text-white">@Model.Submission.FlagNotes</span>
                        </div>
                    }
                }

                @if (!string.IsNullOrEmpty(Model.Submission.StatusChangedByUserId))
                {
                    <div>
                        <span class="font-medium text-gray-700 dark:text-gray-300">Changed By:</span>
                        <span class="text-gray-900 dark:text-white">@Model.Submission.StatusChangedByUserId</span>
                    </div>
                }
            </div>
        </div>
    }
</div>

<!-- Main Content Grid -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    
    <!-- Student Information Card -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            <i class="fas fa-user me-2"></i>Student Information
        </h2>
        
        <div class="space-y-4">
            <!-- Student Name -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Student Name
                </label>
                <div class="relative">
                    <input type="text" value="@Model.Submission.GetStudentName()" readonly 
                           class="bg-gray-100 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    @if (Model.SisIntegrationStatus?.IsLinkedToSis == true && Model.SisIntegrationStatus.PreFilledFields.Contains("StudentName"))
                    {
                        <span class="absolute top-0 right-0 -mt-2 -mr-2">
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded dark:bg-blue-900 dark:text-blue-300">
                                From SIS
                            </span>
                        </span>
                    }
                </div>
            </div>

            <!-- Student Civil ID -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Civil ID
                </label>
                <div class="relative">
                    <input type="text" value="@Model.Submission.GetStudentCivilId()" readonly
                           class="bg-gray-100 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    @if (Model.SisIntegrationStatus?.IsLinkedToSis == true && Model.SisIntegrationStatus.PreFilledFields.Contains("StudentCivilId"))
                    {
                        <span class="absolute top-0 right-0 -mt-2 -mr-2">
                            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded dark:bg-blue-900 dark:text-blue-300">
                                From SIS 
                            </span>
                        </span>
                    }
                </div>
            </div>

            <!-- Student Nationality Status -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Nationality Status
                </label>
                <div class="flex items-center">
                    @if (Model.Submission.StudentIsKuwaiti)
                    {
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                            <i class="fas fa-flag me-2"></i>
                            Kuwaiti
                        </span>
                    }
                    else
                    {
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300">
                            <i class="fas fa-globe me-2"></i>
                            Non-Kuwaiti
                        </span>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- SIS Integration Card -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            <i class="fas fa-database me-2"></i>SIS Integration
        </h2>

        @if (Model.SisIntegrationStatus?.IsLinkedToSis == true)
        {
            <div class="space-y-4">
                <!-- Integration Status -->
                <div class="flex items-center">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                        <i class="fas fa-check-circle me-2"></i>
                        Linked to SIS
                    </span>
                </div>

                <!-- SIS Student Information -->
                <div class="bg-gray-50 rounded-lg p-4 dark:bg-gray-700">
                    <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-3">
                        SIS Student Record
                    </h3>
                    <div class="grid grid-cols-1 gap-3">
                        <div>
                            <span class="text-xs text-gray-500 dark:text-gray-400">Student ID:</span>
                            <p class="text-sm font-medium text-gray-900 dark:text-white">@Model.SisIntegrationStatus.SisStudentId</p>
                        </div>
                        @if (!string.IsNullOrEmpty(Model.SisIntegrationStatus.SisFullNameAR))
                        {
                            <div>
                                <span class="text-xs text-gray-500 dark:text-gray-400">Name (Arabic):</span>
                                <p class="text-sm font-medium text-gray-900 dark:text-white" dir="rtl">@Model.SisIntegrationStatus.SisFullNameAR</p>
                            </div>
                        }
                        @if (!string.IsNullOrEmpty(Model.SisIntegrationStatus.SisFullNameEN))
                        {
                            <div>
                                <span class="text-xs text-gray-500 dark:text-gray-400">Name (English):</span>
                                <p class="text-sm font-medium text-gray-900 dark:text-white">@Model.SisIntegrationStatus.SisFullNameEN</p>
                            </div>
                        }
                    </div>
                </div>

                <!-- Pre-filled Fields -->
                @if (Model.SisIntegrationStatus.PreFilledFields.Any())
                {
                    <div>
                        <span class="text-xs text-gray-500 dark:text-gray-400">Pre-filled Fields:</span>
                        <div class="flex flex-wrap gap-2 mt-1">
                            @foreach (var field in Model.SisIntegrationStatus.PreFilledFields)
                            {
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300">
                                    @field
                                </span>
                            }
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <div class="text-center py-4">
                <div class="flex items-center justify-center">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300">
                        <i class="fas fa-unlink me-2"></i>
                        Not Linked to SIS
                    </span>
                </div>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
                    This submission was entered manually
                </p>
            </div>
        }
    </div>

    <!-- Documents Card -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            <i class="fas fa-file-alt me-2"></i>Uploaded Documents
        </h2>

        @{
            var allDocuments = Model.DocumentInfo.SelectMany(d => d.Documents).ToList();
        }
        @if (allDocuments.Any())
        {
            <!-- Document Categories -->
            @foreach (var category in allDocuments.GroupBy(d => d.Category).OrderBy(g => g.Key))
            {
                <div class="mb-6">
                    <h3 class="font-medium text-gray-900 dark:text-white mb-3">
                        @category.Key Documents
                    </h3>
                    <div class="grid grid-cols-1 gap-3">
                        @foreach (var document in category)
                        {
                            <div class="border border-gray-200 rounded-lg p-3 dark:border-gray-600 @(document.IsRequired ? "bg-blue-50 dark:bg-blue-900/20" : "")">
                                <div class="flex items-center justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center">
                                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                                                @document.DisplayName
                                            </h4>
                                        </div>

                                        @if (!string.IsNullOrEmpty(document.FilePath) && document.FileExists)
                                        {
                                            <div class="mt-2 flex items-center text-sm text-gray-500 dark:text-gray-400">
                                                <i class="fas fa-file me-2"></i>
                                                <span>@document.FileName</span>
                                                <span class="mx-2">•</span>
                                                <span>@document.FormattedFileSize</span>
                                                @if (document.UploadDate.HasValue)
                                                {
                                                    <span class="mx-2">•</span>
                                                    <span>@document.UploadDate.Value.ToString("MMM dd, yyyy")</span>
                                                }
                                            </div>
                                        }
                                        else if (!string.IsNullOrEmpty(document.FilePath))
                                        {
                                            <div class="mt-2 flex items-center text-sm text-red-500 dark:text-red-400">
                                                <i class="fas fa-exclamation-triangle me-2"></i>
                                                <span>File not found</span>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="mt-2 flex items-center text-sm text-gray-400 dark:text-gray-500">
                                                <i class="fas fa-minus-circle me-2"></i>
                                                <span>Not uploaded</span>
                                            </div>
                                        }
                                    </div>

                                    <!-- Single Info Button for all documents -->
                                    <div class="flex items-center">
                                        <button type="button"
                                                onclick="showDocumentInfo(@Model.Submission.Id, '@document.DocumentType')"
                                                class="inline-flex items-center px-3 py-1 text-xs font-medium @(document.IsUploaded && document.FileExists ? "text-blue-600 bg-blue-100 hover:bg-blue-200 focus:ring-blue-300 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800 dark:focus:ring-blue-800" : document.IsUploaded ? "text-red-600 bg-red-100 hover:bg-red-200 focus:ring-red-300 dark:bg-red-900 dark:text-red-300 dark:hover:bg-red-800 dark:focus:ring-red-800" : "text-gray-600 bg-gray-100 hover:bg-gray-200 focus:ring-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 dark:focus:ring-gray-600") rounded-lg focus:ring-4 transition-all duration-200"
                                                title="@(document.IsUploaded && document.FileExists ? "View document information and actions" : document.IsUploaded ? "View error details" : "View document requirements")"
                                                aria-label="View information for @document.DisplayName">
                                            <i class="fas @(document.IsUploaded && document.FileExists ? "fa-info-circle" : document.IsUploaded ? "fa-exclamation-triangle" : "fa-question-circle") me-1" aria-hidden="true"></i>
                                            @(document.IsUploaded && document.FileExists ? "Info & Actions" : document.IsUploaded ? "Error Details" : "Requirements")
                                        </button>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            }
        }
        else
        {
            <div class="text-center py-8">
                <i class="fas fa-file-alt text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
                <p class="text-gray-500 dark:text-gray-400">
                    No documents uploaded
                </p>
            </div>
        }
    </div>

    <!-- Statistics Card -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            <i class="fas fa-chart-bar me-2"></i>Statistics
        </h2>

        <div class="space-y-4">
            <!-- Document Statistics -->
            <div class="grid grid-cols-3 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">@Model.Statistics.TotalDocuments</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">Total Documents</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600 dark:text-green-400">@Model.Statistics.RequiredDocuments</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">Required</div>
                </div>
            </div>

            <!-- File Size -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Total File Size
                </label>
                <p class="text-sm text-gray-900 dark:text-white">@Model.Statistics.FormattedTotalFileSize</p>
            </div>

            <!-- Admin Activity -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Admin Views
                    </label>
                    <p class="text-sm text-gray-900 dark:text-white">@Model.Statistics.AdminViewCount times</p>
                </div>
                @if (Model.Statistics.LastAdminView.HasValue)
                {
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Last Admin View / آخر مشاهدة إدارية
                        </label>
                        <p class="text-sm text-gray-900 dark:text-white">@Model.Statistics.LastAdminView.Value.ToString("MMM dd, yyyy HH:mm")</p>
                    </div>
                }
            </div>
        </div>
    </div>

    <!-- Email Notification Preview Card -->
    @if (ViewBag.HasPendingEmails == true)
    {
        <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800 lg:col-span-2">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                <i class="fas fa-envelope me-2 text-red-600"></i>Email Notification Preview
            </h2>

            @if (ViewBag.DisapprovedDocuments != null && ((List<Forms.ktech.ViewModels.Admin.DisapprovedDocumentInfo>)ViewBag.DisapprovedDocuments).Any())
            {
                <div class="space-y-4">
                    <!-- Notification Status -->
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 dark:bg-red-900/20 dark:border-red-800">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400 me-2"></i>
                            <span class="text-sm font-medium text-red-800 dark:text-red-300">
                                Pending Email Notification
                            </span>
                        </div>
                        <p class="text-sm text-red-700 dark:text-red-300 mt-1">
                            This submission has disapproved documents. An email notification is ready to be sent to the student.
                        </p>
                    </div>

                    <!-- Disapproved Documents List -->
                    <div class="bg-gray-50 rounded-lg p-4 dark:bg-gray-700">
                        <h3 class="font-medium text-gray-900 dark:text-white mb-3">
                            <i class="fas fa-times-circle me-2 text-red-500"></i>
                            Disapproved Documents
                        </h3>
                        <div class="space-y-2">
                            @foreach (var doc in (List<Forms.ktech.ViewModels.Admin.DisapprovedDocumentInfo>)ViewBag.DisapprovedDocuments)
                            {
                                <div class="flex items-center justify-between p-3 bg-white rounded border dark:bg-gray-800 dark:border-gray-600">
                                    <div class="flex-1">
                                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">@doc.DocumentName</h4>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">
                                            Reason: @doc.Reason
                                        </p>
                                    </div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        @doc.DisapprovalDate.ToString("MMM dd, yyyy")
                                    </div>
                                </div>
                            }
                        </div>
                    </div>

                    <!-- Email Content Editing Section -->
                    <div class="bg-blue-50 rounded-lg p-4 dark:bg-blue-900/20">
                        <h3 class="font-medium text-gray-900 dark:text-white mb-3">
                            <i class="fas fa-edit me-2 text-blue-500"></i>
                            Email Content Editing
                        </h3>

                        <!-- Email Subject -->
                        <div class="mb-4">
                            <label for="emailSubject" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Email Subject
                            </label>
                            <input type="text" id="emailSubject"
                                   placeholder="Email subject will load here..."
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                   maxlength="500">
                            <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                <span id="subjectCharCount">0</span>/500 characters
                            </div>
                        </div>

                        <!-- Email Body Content (Plain Text) -->
                        <div class="mb-4">
                            <label for="emailBodyText" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Email Message Content
                            </label>
                            <textarea id="emailBodyText"
                                      rows="8"
                                      placeholder="Email content will load here..."
                                      class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:text-white resize-vertical"></textarea>
                            <div class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                This text will be inserted into the professional KTECH email template when sent.
                            </div>
                        </div>

                        <!-- Email Actions -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <button type="button" onclick="resetEmailContent()"
                                        class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 focus:ring-4 focus:ring-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600 dark:focus:ring-gray-700">
                                    <i class="fas fa-undo me-2"></i>
                                    Reset to Default
                                </button>
                            </div>

                            <div class="flex items-center space-x-2 flex-wrap gap-2">
                                <button type="button" onclick="sendEmailNotification(@Model.Submission.Id)"
                                        class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:ring-4 focus:ring-red-300 dark:bg-red-500 dark:hover:bg-red-600 dark:focus:ring-red-800">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    Send Email
                                </button>


                            </div>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="text-center py-6">
                    <i class="fas fa-check-circle text-4xl text-green-500 mb-4"></i>
                    <p class="text-gray-600 dark:text-gray-300">
                        No pending email notifications for this submission. /
                        لا توجد إشعارات بريد إلكتروني معلقة لهذا الطلب.
                    </p>
                </div>
            }
        </div>
    }
</div>

<!-- Bottom Action Bar -->
<div class="mt-6 bg-white rounded-lg shadow-lg p-4 dark:bg-gray-800">
    <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
            <!-- Quick Actions -->
            <button type="button" onclick="window.print()" class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-4 focus:ring-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700">
                <i class="fas fa-print me-2"></i>
                Print
            </button>

            <!-- Export Dropdown -->
            <div class="relative inline-block text-left">
                <button type="button" onclick="toggleExportDropdown()" class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-700 rounded-lg hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                    <i class="fas fa-download me-2"></i>
                    Export
                    <i class="fas fa-chevron-down ml-2"></i>
                </button>

                <!-- Dropdown menu -->
                <div id="exportDropdown" class="hidden absolute left-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 dark:bg-gray-700 z-50">
                    <div class="py-1" role="menu" aria-orientation="vertical">
                        <a href="@Url.Action("ExportSubmissionPdf", "Admin", new { submissionId = Model.Submission.Id })"
                           class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-600"
                           role="menuitem">
                            <i class="fas fa-file-pdf me-2 text-red-500"></i>
                            Export as PDF
                        </a>
                        <a href="@Url.Action("ExportSubmissionExcel", "Admin", new { submissionId = Model.Submission.Id })"
                           class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-600"
                           role="menuitem">
                            <i class="fas fa-file-excel me-2 text-green-500"></i>
                            Export as Excel
                        </a>
                    </div>
                </div>
            </div>

            <!-- Admin Action Buttons -->
            <div class="flex items-center space-x-2">
                <button type="button" onclick="approveSubmission(@Model.Submission.Id)"
                        class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 focus:ring-4 focus:ring-green-300 dark:bg-green-500 dark:hover:bg-green-600 dark:focus:ring-green-800"
                        title="Approve submission">
                    <i class="fas fa-check me-1"></i>
                    Approve
                </button>

                <button type="button" onclick="rejectSubmission(@Model.Submission.Id)"
                        class="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:ring-4 focus:ring-red-300 dark:bg-red-500 dark:hover:bg-red-600 dark:focus:ring-red-800"
                        title="Reject submission">
                    <i class="fas fa-times me-1"></i>
                    Reject
                </button>

                <button type="button" onclick="flagForReview(@Model.Submission.Id)"
                        class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-yellow-100 border border-yellow-300 rounded-lg hover:bg-yellow-200 focus:ring-4 focus:ring-yellow-300 dark:bg-yellow-900 dark:text-yellow-300 dark:border-yellow-600 dark:hover:bg-yellow-800 dark:focus:ring-yellow-700"
                        title="Flag for review">
                    <i class="fas fa-flag me-1"></i>
                    Flag
                </button>
            </div>

            <!-- Related Submissions -->
            <button type="button" onclick="findRelatedSubmissions('@Model.Submission.GetStudentCivilId()')"
                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-purple-700 bg-purple-100 border border-purple-300 rounded-lg hover:bg-purple-200 focus:ring-4 focus:ring-purple-300 dark:bg-purple-900 dark:text-purple-300 dark:border-purple-600 dark:hover:bg-purple-800 dark:focus:ring-purple-700"
                    title="Find related submissions by Civil ID">
                <i class="fas fa-search me-2"></i>
                Related
            </button>
        </div>

        <div class="flex items-center space-x-2">
            <!-- Return to List -->
            <a href="@Model.NavigationContext.ReturnUrl" class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-4 focus:ring-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700">
                <i class="fas fa-arrow-left me-2"></i>
                @Model.NavigationContext.ReturnText
            </a>
        </div>
    </div>
</div>

<!-- Document Info Modal -->
<div id="documentInfoModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50" role="dialog" aria-labelledby="documentInfoModalTitle" aria-hidden="true">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white dark:bg-gray-800">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-600">
                <h3 id="documentInfoModalTitle" class="text-lg font-semibold text-gray-900 dark:text-white">
                    <i class="fas fa-info-circle me-2"></i>Document Information
                </h3>
                <button type="button" onclick="closeDocumentInfoModal()"
                        class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center dark:hover:bg-gray-600 dark:hover:text-white"
                        aria-label="Close modal">
                    <i class="fas fa-times text-lg" aria-hidden="true"></i>
                </button>
            </div>

            <!-- Modal Body -->
            <div id="documentInfoContent" class="py-4">
                <div class="flex items-center justify-center py-8">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                    <span class="ml-3 text-gray-600 dark:text-gray-300">Loading document information...</span>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="flex items-center justify-end pt-4 border-t border-gray-200 dark:border-gray-600 space-x-2">
                <button type="button" onclick="closeDocumentInfoModal()"
                        class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-4 focus:ring-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-white dark:focus:ring-gray-700">
                    <i class="fas fa-times me-2"></i>
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @Html.AntiForgeryToken()
    <script>
        // ===== DOCUMENT MANAGEMENT FUNCTIONS =====

        // Views a document in a new window
        function viewDocument(submissionId, documentType) {
            try {
                const url = `@Url.Action("ViewDocument", "Admin")/${submissionId}/${encodeURIComponent(documentType)}`;

                // Open in new window with specific features
                const windowFeatures = 'width=1024,height=768,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no';
                const newWindow = window.open(url, `document_${submissionId}_${documentType}`, windowFeatures);

                if (!newWindow) {
                    // Fallback if popup blocked
                    showNotification('Please allow popups to view documents', 'warning');
                    // Try direct navigation as fallback
                    window.location.href = url;
                } else {
                    newWindow.focus();
                }

                // Log the action for analytics
                logDocumentAction('view', submissionId, documentType);

            } catch (error) {
                console.error('Error viewing document:', error);
                showNotification('Error viewing document', 'error');
            }
        }

        // Downloads a document
        function downloadDocument(submissionId, documentType) {
            try {
                const url = `@Url.Action("DownloadDocument", "Admin")/${submissionId}/${encodeURIComponent(documentType)}`;

                // Create a temporary link element for download
                const link = document.createElement('a');
                link.href = url;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Show success notification
                showNotification('Document download started', 'success');

                // Log the action for analytics
                logDocumentAction('download', submissionId, documentType);

            } catch (error) {
                console.error('Error downloading document:', error);
                showNotification('Error downloading document', 'error');
            }
        }

        // Shows detailed document information in a modal with approval functionality
        async function showDocumentInfo(submissionId, documentType) {
            try {
                // Show modal with loading state
                const modal = document.getElementById('documentInfoModal');
                const content = document.getElementById('documentInfoContent');

                modal.classList.remove('hidden');
                modal.setAttribute('aria-hidden', 'false');

                // Set loading content
                content.innerHTML = `
                    <div class="flex items-center justify-center py-8">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <span class="ml-3 text-gray-600 dark:text-gray-300">Loading document information...</span>
                    </div>
                `;

                // Fetch document information and approval status
                const [docResponse, approvalResponse] = await Promise.all([
                    fetch(`@Url.Action("DocumentInfo", "Admin")/${submissionId}/${encodeURIComponent(documentType)}`),
                    fetch(`@Url.Action("DocumentApprovalStatus", "Admin")/${submissionId}/${encodeURIComponent(documentType)}`)
                ]);



                if (!docResponse.ok || !approvalResponse.ok) {
                    throw new Error(`HTTP error! Document: ${docResponse.status}, Approval: ${approvalResponse.status}`);
                }

                const [documentInfo, approvalInfo] = await Promise.all([
                    docResponse.json(),
                    approvalResponse.json()
                ]);



                // Update modal content with document information and approval status
                content.innerHTML = createDocumentInfoHTML(documentInfo, approvalInfo, submissionId, documentType);

                // Log the action for analytics
                logDocumentAction('info', submissionId, documentType);

            } catch (error) {
                console.error('Error fetching document info:', error);

                // Show error in modal
                const content = document.getElementById('documentInfoContent');
                content.innerHTML = `
                    <div class="text-center py-8">
                        <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                        <p class="text-red-600 dark:text-red-400">
                            Error loading document information
                        </p>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
                            ${error.message}
                        </p>
                    </div>
                `;
            }
        }

        // Closes the document info modal
        function closeDocumentInfoModal() {
            const modal = document.getElementById('documentInfoModal');
            modal.classList.add('hidden');
            modal.setAttribute('aria-hidden', 'true');
        }

        // Creates HTML content for document information display with approval functionality
        function createDocumentInfoHTML(documentInfo, approvalInfo, submissionId, documentType) {


            // Safely access validation status
            const validationStatus = documentInfo.ValidationStatus || {};
            const status = validationStatus.Status || 'Unknown';
            const statusClass = status === 'Valid' ? 'text-green-600' :
                               status === 'Warning' ? 'text-yellow-600' : 'text-red-600';

            const statusIcon = status === 'Valid' ? 'fa-check-circle' :
                              status === 'Warning' ? 'fa-exclamation-triangle' : 'fa-times-circle';

            // Get approval status information
            const approvalStatus = approvalInfo.status || 'Pending';
            const approvalStatusText = approvalInfo.statusText || 'Pending Review';
            const approvalBadgeClass = approvalInfo.badgeClass || 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
            const approvalIconClass = approvalInfo.iconClass || 'fas fa-clock';

            // Safely extract values with fallbacks
            const displayName = documentInfo.DisplayName || documentInfo.displayName || 'N/A';
            const contentType = documentInfo.ContentType || documentInfo.contentType || 'Unknown';
            const formattedFileSize = documentInfo.FormattedFileSize || documentInfo.formattedFileSize || 'N/A';
            const isRequired = documentInfo.IsRequired !== undefined ? documentInfo.IsRequired :
                              (documentInfo.isRequired !== undefined ? documentInfo.isRequired : false);
            const fileExists = documentInfo.FileExists !== undefined ? documentInfo.FileExists :
                              (documentInfo.fileExists !== undefined ? documentInfo.fileExists : false);
            const uploadDate = documentInfo.UploadDate || documentInfo.uploadDate;

            let html = `
                <div class="space-y-6">
                    <!-- Modal Header with Approval Status -->
                    <div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-600">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                            <i class="fas fa-file-check me-2"></i>
                            Document Review: ${displayName}
                        </h3>
                        <span class="px-3 py-1 text-xs font-medium rounded-full ${approvalBadgeClass}">
                            <i class="${approvalIconClass} me-1"></i>${approvalStatusText}
                        </span>
                    </div>

                    <!-- Document Basic Info -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-900 dark:text-white mb-3">
                            <i class="fas fa-file-alt me-2"></i>Document Details
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="font-medium text-gray-700 dark:text-gray-300">Name:</span>
                                <p class="text-gray-900 dark:text-white">${displayName}</p>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700 dark:text-gray-300">Type:</span>
                                <p class="text-gray-900 dark:text-white">${contentType}</p>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700 dark:text-gray-300">Size:</span>
                                <p class="text-gray-900 dark:text-white">${formattedFileSize}</p>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700 dark:text-gray-300">Required:</span>
                                <p class="text-gray-900 dark:text-white">${isRequired ? 'Yes' : 'No'}</p>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700 dark:text-gray-300">File Exists:</span>
                                <p class="text-gray-900 dark:text-white">${fileExists ? 'Yes' : 'No'}</p>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700 dark:text-gray-300">Upload Date:</span>
                                <p class="text-gray-900 dark:text-white">${uploadDate ? new Date(uploadDate).toLocaleDateString() : 'N/A'}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Validation Status -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-900 dark:text-white mb-3">
                            <i class="fas fa-shield-alt me-2"></i>Validation Status
                        </h4>
                        <div class="flex items-center mb-3">
                            <i class="fas ${statusIcon} ${statusClass} me-2"></i>
                            <span class="${statusClass} font-semibold">${status}</span>
                        </div>
            `;

            // Add errors if any
            const errors = validationStatus.Errors || validationStatus.errors || [];
            if (errors.length > 0) {
                html += `
                    <div class="mb-3">
                        <h5 class="font-medium text-red-600 dark:text-red-400 mb-2">Errors:</h5>
                        <ul class="list-disc list-inside text-sm text-red-600 dark:text-red-400 space-y-1">
                `;
                errors.forEach(error => {
                    html += `<li>${error}</li>`;
                });
                html += `</ul></div>`;
            }

            // Add warnings if any
            const warnings = validationStatus.Warnings || validationStatus.warnings || [];
            if (warnings.length > 0) {
                html += `
                    <div class="mb-3">
                        <h5 class="font-medium text-yellow-600 dark:text-yellow-400 mb-2">Warnings:</h5>
                        <ul class="list-disc list-inside text-sm text-yellow-600 dark:text-yellow-400 space-y-1">
                `;
                warnings.forEach(warning => {
                    html += `<li>${warning}</li>`;
                });
                html += `</ul></div>`;
            }

            html += `</div>`;

            // Add Document Approval Actions section
            html += `
                <!-- Document Approval Actions -->
                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                    <h4 class="font-semibold text-gray-900 dark:text-white mb-3">
                        <i class="fas fa-gavel me-2"></i>Approval Actions
                    </h4>

                    <div class="space-y-4">
                        <!-- Approval Buttons -->
                        <div class="flex flex-wrap gap-3">
                            <button type="button" onclick="approveDocument(${submissionId}, '${documentType}')"
                                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-white
                                           bg-green-600 rounded-lg hover:bg-green-700 focus:ring-4 focus:ring-green-300
                                           disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                                    ${approvalStatus === 'Approved' ? 'disabled' : ''}>
                                <i class="fas fa-check me-2"></i>Approve Document
                            </button>

                            <button type="button" onclick="showDisapprovalForm()"
                                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-white
                                           bg-red-600 rounded-lg hover:bg-red-700 focus:ring-4 focus:ring-red-300
                                           transition-all duration-200">
                                <i class="fas fa-times me-2"></i>Disapprove Document
                            </button>
                        </div>

                        <!-- Disapproval Form (Hidden by default) -->
                        <div id="disapprovalForm" class="hidden space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Reason for Disapproval <span class="text-red-500">*</span>
                                </label>
                                <select id="disapprovalReason"
                                        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg
                                               focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5
                                               dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                    <option value="">Select reason</option>
                                    <option value="poor_quality">Poor Image Quality</option>
                                    <option value="incomplete_document">Incomplete Document</option>
                                    <option value="wrong_document">Wrong Document Type</option>
                                    <option value="expired_document">Expired Document</option>
                                    <option value="illegible_text">Illegible Text</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>



                            <div class="flex gap-3">
                                <button type="button" onclick="submitDisapproval(${submissionId}, '${documentType}')"
                                        class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700
                                               focus:ring-4 focus:ring-red-300 transition-all duration-200">
                                    Submit Disapproval
                                </button>
                                <button type="button" onclick="hideDisapprovalForm()"
                                        class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300
                                               dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500 transition-all duration-200">
                                    Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Add Document Actions section if file exists
            const viewUrl = documentInfo.ViewUrl || documentInfo.viewUrl;
            const downloadUrl = documentInfo.DownloadUrl || documentInfo.downloadUrl;

            if (fileExists && viewUrl && downloadUrl) {
                html += `
                    <!-- Document Actions -->
                    <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-900 dark:text-white mb-3">
                            <i class="fas fa-tools me-2"></i>Document Actions
                        </h4>
                        <div class="flex flex-wrap gap-3">
                            <button type="button"
                                    onclick="viewDocumentFromModal('${viewUrl}')"
                                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:ring-4 focus:ring-blue-300 dark:bg-blue-500 dark:hover:bg-blue-600 dark:focus:ring-blue-800 transition-all duration-200">
                                <i class="fas fa-eye me-2"></i>
                                View Document
                            </button>
                            <button type="button"
                                    onclick="downloadDocumentFromModal('${downloadUrl}')"
                                    class="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 rounded-lg hover:bg-green-700 focus:ring-4 focus:ring-green-300 dark:bg-green-500 dark:hover:bg-green-600 dark:focus:ring-green-800 transition-all duration-200">
                                <i class="fas fa-download me-2"></i>
                                Download
                            </button>
                        </div>
                    </div>
                `;
            } else if (!fileExists && isRequired) {
                html += `
                    <!-- Missing Required Document Notice -->
                    <div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
                        <h4 class="font-semibold text-red-600 dark:text-red-400 mb-2">
                            <i class="fas fa-exclamation-triangle me-2"></i>Required Document Missing
                        </h4>
                        <p class="text-sm text-red-600 dark:text-red-400">
                            This document is required for the submission but has not been uploaded yet.
                        </p>
                    </div>
                `;
            } else if (!fileExists) {
                html += `
                    <!-- Optional Document Notice -->
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-600 dark:text-gray-400 mb-2">
                            <i class="fas fa-info-circle me-2"></i>Optional Document
                        </h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            This document is optional and has not been uploaded.
                        </p>
                    </div>
                `;
            }

            html += `</div>`;
            return html;
        }

        // Views a document from the modal
        function viewDocumentFromModal(viewUrl) {
            try {
                // Open in new window with specific features
                const windowFeatures = 'width=1024,height=768,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no';
                const newWindow = window.open(viewUrl, `document_view_${Date.now()}`, windowFeatures);

                if (!newWindow) {
                    // Fallback if popup blocked
                    showNotification('Please allow popups to view documents', 'warning');
                    // Try direct navigation as fallback
                    window.location.href = viewUrl;
                } else {
                    newWindow.focus();
                    showNotification('Document opened in new window', 'success');
                }
            } catch (error) {
                console.error('Error viewing document:', error);
                showNotification('Error viewing document', 'error');
            }
        }

        // Downloads a document from the modal
        function downloadDocumentFromModal(downloadUrl) {
            try {
                // Create a temporary link element for download
                const link = document.createElement('a');
                link.href = downloadUrl;
                link.style.display = 'none';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // Show success notification
                showNotification('Document download started', 'success');
            } catch (error) {
                console.error('Error downloading document:', error);
                showNotification('Error downloading document', 'error');
            }
        }

        // Logs document actions for analytics
        function logDocumentAction(action, submissionId, documentType) {
            // Document action logging removed for production
        }

        // Shows a notification to the user
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full`;

            // Set notification styling based on type
            switch (type) {
                case 'success':
                    notification.classList.add('bg-green-100', 'text-green-800', 'border', 'border-green-200');
                    break;
                case 'warning':
                    notification.classList.add('bg-yellow-100', 'text-yellow-800', 'border', 'border-yellow-200');
                    break;
                case 'error':
                    notification.classList.add('bg-red-100', 'text-red-800', 'border', 'border-red-200');
                    break;
                default:
                    notification.classList.add('bg-blue-100', 'text-blue-800', 'border', 'border-blue-200');
            }

            notification.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-1">
                        <p class="text-sm font-medium">${message}</p>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-3 text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // Auto remove after 5 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }, 5000);
        }

        // ===== EXPORT FUNCTIONS =====

        // Toggles the export dropdown menu
        function toggleExportDropdown() {
            const dropdown = document.getElementById('exportDropdown');
            dropdown.classList.toggle('hidden');
        }

        // Close export dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('exportDropdown');
            const button = event.target.closest('[onclick="toggleExportDropdown()"]');

            if (!button && !dropdown.contains(event.target)) {
                dropdown.classList.add('hidden');
            }
        });

        // ===== ADMIN ACTION FUNCTIONS =====

        // Approves a submission
        async function approveSubmission(submissionId) {
            if (!confirm('Are you sure you want to approve this submission?')) {
                return;
            }

            try {
                showNotification('Processing approval...', 'info');

                const response = await fetch(`@Url.Action("ApproveSubmission", "Admin")/${submissionId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                    },
                    body: JSON.stringify({}) // Empty body to satisfy anti-forgery validation
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        showNotification(result.message || 'Submission approved successfully', 'success');
                        // Refresh the page to show updated status
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        throw new Error(result.message || 'Failed to approve submission');
                    }
                } else {
                    // Try to parse error response
                    let errorMessage = `HTTP error! status: ${response.status}`;
                    try {
                        const errorResult = await response.json();
                        errorMessage = errorResult.message || errorMessage;
                    } catch (parseError) {
                        // If JSON parsing fails, use the status text
                        errorMessage = response.statusText || errorMessage;
                    }
                    throw new Error(errorMessage);
                }
            } catch (error) {
                console.error('Error approving submission:', error);
                showNotification(`Error approving submission: ${error.message}`, 'error');
            }
        }

        // Rejects a submission
        async function rejectSubmission(submissionId) {
            const reason = prompt('Please provide a reason for rejection:');
            if (!reason || reason.trim() === '') {
                return;
            }

            try {
                showNotification('Processing rejection...', 'info');

                const response = await fetch(`@Url.Action("RejectSubmission", "Admin")/${submissionId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                    },
                    body: JSON.stringify({ reason: reason.trim() })
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        showNotification(result.message || 'Submission rejected successfully', 'success');
                        // Refresh the page to show updated status
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        throw new Error(result.message || 'Failed to reject submission');
                    }
                } else {
                    // Try to parse error response
                    let errorMessage = `HTTP error! status: ${response.status}`;
                    try {
                        const errorResult = await response.json();
                        errorMessage = errorResult.message || errorMessage;
                    } catch (parseError) {
                        // If JSON parsing fails, use the status text
                        errorMessage = response.statusText || errorMessage;
                    }
                    throw new Error(errorMessage);
                }
            } catch (error) {
                console.error('Error rejecting submission:', error);
                showNotification(`Error rejecting submission: ${error.message}`, 'error');
            }
        }

        // Flags a submission for review
        async function flagForReview(submissionId) {
            const notes = prompt('Please provide notes for review (optional):');

            try {
                showNotification('Flagging for review...', 'info');

                const response = await fetch(`@Url.Action("FlagForReview", "Admin")/${submissionId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                    },
                    body: JSON.stringify({ notes: notes?.trim() || '' })
                });

                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        showNotification(result.message || 'Submission flagged for review', 'success');
                        // Refresh the page to show updated status
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        throw new Error(result.message || 'Failed to flag submission');
                    }
                } else {
                    // Try to parse error response
                    let errorMessage = `HTTP error! status: ${response.status}`;
                    try {
                        const errorResult = await response.json();
                        errorMessage = errorResult.message || errorMessage;
                    } catch (parseError) {
                        // If JSON parsing fails, use the status text
                        errorMessage = response.statusText || errorMessage;
                    }
                    throw new Error(errorMessage);
                }
            } catch (error) {
                console.error('Error flagging submission:', error);
                showNotification(`Error flagging submission: ${error.message}`, 'error');
            }
        }

        // ===== DOCUMENT APPROVAL FUNCTIONS =====

        // Approves a document
        async function approveDocument(submissionId, documentType) {
            if (!confirm('Are you sure you want to approve this document?')) {
                return;
            }

            try {
                showNotification('Processing approval...', 'info');

                const response = await fetch(`@Url.Action("ApproveDocument", "Admin")`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                    },
                    body: JSON.stringify({
                        submissionId: submissionId,
                        documentType: documentType,
                        comments: null
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    showNotification('Document approved successfully', 'success');

                    // Refresh the modal to show updated status
                    setTimeout(() => {
                        showDocumentInfo(submissionId, documentType);
                    }, 1000);
                } else {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
            } catch (error) {
                console.error('Error approving document:', error);
                showNotification('Error approving document', 'error');
            }
        }

        // Shows the disapproval form
        function showDisapprovalForm() {
            const form = document.getElementById('disapprovalForm');
            form.classList.remove('hidden');

            // Focus on the reason dropdown
            document.getElementById('disapprovalReason').focus();
        }

        // Hides the disapproval form
        function hideDisapprovalForm() {
            const form = document.getElementById('disapprovalForm');
            form.classList.add('hidden');

            // Clear form fields
            document.getElementById('disapprovalReason').value = '';
        }

        // Submits document disapproval
        async function submitDisapproval(submissionId, documentType) {
            const reason = document.getElementById('disapprovalReason').value;

            if (!reason) {
                showNotification('Please select a reason for disapproval', 'warning');
                return;
            }

            if (!confirm('Are you sure you want to disapprove this document? A notification will be queued for the student.')) {
                return;
            }

            try {
                showNotification('Processing disapproval...', 'info');

                const response = await fetch(`@Url.Action("DisapproveDocument", "Admin")`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('input[name="__RequestVerificationToken"]')?.value || ''
                    },
                    body: JSON.stringify({
                        submissionId: submissionId,
                        documentType: documentType,
                        reason: reason
                    })
                });

                if (response.ok) {
                    const result = await response.json();
                    showNotification('Document disapproved and notification queued for sending', 'success');

                    // Hide the form and refresh the modal
                    hideDisapprovalForm();
                    setTimeout(() => {
                        showDocumentInfo(submissionId, documentType);
                    }, 1000);
                } else {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
            } catch (error) {
                console.error('Error disapproving document:', error);
                showNotification('Error disapproving document', 'error');
            }
        }

        // Gets document approval history
        async function getDocumentApprovalHistory(submissionId, documentType) {
            try {
                const response = await fetch(`@Url.Action("DocumentApprovalHistory", "Admin")/${submissionId}/${encodeURIComponent(documentType)}`);

                if (response.ok) {
                    const result = await response.json();
                    return result.history || [];
                } else {
                    console.error('Error fetching approval history:', response.status);
                    return [];
                }
            } catch (error) {
                console.error('Error fetching approval history:', error);
                return [];
            }
        }

        // ===== RELATED SUBMISSIONS FUNCTIONS =====

        // Finds related submissions by Civil ID
        async function findRelatedSubmissions(civilId) {
            try {
                showNotification('Searching for related submissions...', 'info');

                const response = await fetch(`@Url.Action("FindRelatedSubmissions", "Admin")?civilId=${encodeURIComponent(civilId)}`);

                if (response.ok) {
                    const data = await response.json();
                    showRelatedSubmissionsModal(data);
                } else {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
            } catch (error) {
                console.error('Error finding related submissions:', error);
                showNotification('Error finding related submissions', 'error');
            }
        }

        // Shows the related submissions modal
        function showRelatedSubmissionsModal(data) {
            // Create modal HTML
            const modalHtml = `
                <div id="relatedSubmissionsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" role="dialog">
                    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-2/3 shadow-lg rounded-md bg-white dark:bg-gray-800">
                        <div class="mt-3">
                            <div class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-gray-600">
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                                    <i class="fas fa-search me-2"></i>Related Submissions for Civil ID: ${data.civilId}
                                </h3>
                                <button type="button" onclick="closeRelatedSubmissionsModal()" class="text-gray-400 hover:text-gray-600">
                                    <i class="fas fa-times text-lg"></i>
                                </button>
                            </div>
                            <div class="py-4">
                                ${generateRelatedSubmissionsContent(data)}
                            </div>
                            <div class="flex items-center justify-end pt-4 border-t border-gray-200 dark:border-gray-600">
                                <button type="button" onclick="closeRelatedSubmissionsModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                                    Close
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Add modal to page
            document.body.insertAdjacentHTML('beforeend', modalHtml);
        }

        // Generates content for related submissions modal
        function generateRelatedSubmissionsContent(data) {
            if (!data.submissions || data.submissions.length === 0) {
                return `
                    <div class="text-center py-8">
                        <i class="fas fa-search text-4xl text-gray-300 mb-4"></i>
                        <p class="text-gray-500">No related submissions found</p>
                    </div>
                `;
            }

            let html = `
                <div class="mb-4">
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        Found ${data.submissions.length} related submission(s)
                    </p>
                </div>
                <div class="space-y-3">
            `;

            data.submissions.forEach(submission => {
                html += `
                    <div class="border border-gray-200 rounded-lg p-4 dark:border-gray-600">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="font-medium text-gray-900 dark:text-white">
                                    Submission #${submission.id}
                                </h4>
                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                    ${submission.studentName} • ${submission.submissionDate}
                                </p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <a href="@Url.Action("ViewSubmission", "Admin")?id=${submission.id}"
                                   class="inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 bg-blue-100 rounded-lg hover:bg-blue-200">
                                    <i class="fas fa-eye me-1"></i>
                                    View
                                </a>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += `</div>`;
            return html;
        }

        // Closes the related submissions modal
        function closeRelatedSubmissionsModal() {
            const modal = document.getElementById('relatedSubmissionsModal');
            if (modal) {
                modal.remove();
            }
        }

        // ===== EVENT LISTENERS =====

        // Close modal when clicking outside
        document.getElementById('documentInfoModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeDocumentInfoModal();
            }
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const modal = document.getElementById('documentInfoModal');
                if (!modal.classList.contains('hidden')) {
                    closeDocumentInfoModal();
                }
            }
        });

        // ===== EMAIL CONTENT EDITING FUNCTIONS =====



        // Send email notification with custom content
        async function sendEmailNotification(submissionId) {
            if (!confirm('Are you sure you want to send this email notification?')) {
                return;
            }

            try {
                // Get custom email content from form fields
                const subjectField = document.getElementById('emailSubject');
                const bodyField = document.getElementById('emailBodyText');

                // Validate that form fields exist
                if (!subjectField || !bodyField) {
                    showNotification('Email form fields not found', 'error');
                    return;
                }

                // Get values and trim whitespace
                const customSubject = subjectField.value?.trim() || '';
                const customBody = bodyField.value?.trim() || '';



                // Validate that both subject and body have content
                if (!customSubject || !customBody) {
                    showNotification('Please ensure both email subject and body have content', 'warning');
                    return;
                }

                // Show loading notification
                showNotification('Sending email notification...', 'info');

                // Get anti-forgery token
                const token = document.querySelector('input[name="__RequestVerificationToken"]').value;

                // Prepare form data
                const formData = new FormData();
                formData.append('submissionId', submissionId);
                formData.append('__RequestVerificationToken', token);
                formData.append('customSubject', customSubject);
                formData.append('customBody', customBody);

                // Send email request
                const response = await fetch('@Url.Action("SendBatchEmail", "Admin")', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();



                if (result.success) {
                    showNotification('Email notification sent successfully!', 'success');



                    // Refresh the page after a short delay to show updated status
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    console.error('Email send failed:', result);
                    showNotification(`Error sending email: ${result.message}`, 'error');
                }
            } catch (error) {
                console.error('Error sending email notification:', error);
                showNotification('Error sending email notification', 'error');
            }
        }

        // Generate default email content from disapproved documents data
        function generateDefaultEmailContent() {
            const studentName = '@Model.Submission.GetStudentName()';
            const firstName = studentName.split(' ')[0];
            const formattedFirstName = firstName.charAt(0).toUpperCase() + firstName.slice(1).toLowerCase();

            // Get disapproved documents data from the page
            const disapprovedDocs = [
                @if (ViewBag.DisapprovedDocuments != null)
                {
                    @foreach (var doc in (List<Forms.ktech.ViewModels.Admin.DisapprovedDocumentInfo>)ViewBag.DisapprovedDocuments)
                    {
                        <text>{
                            name: '@Html.Raw(doc.DocumentName.Replace("'", "\\'"))',
                            reason: '@Html.Raw(doc.Reason.Replace("'", "\\'"))',
                            date: '@doc.DisapprovalDate.ToString("MMMM dd, yyyy")'
                        },</text>
                    }
                }
            ];

            // Generate default subject
            const defaultSubject = 'تحديث مستندات التسجيل / Registration Documents Update';

            // Generate disapproved documents list for Arabic section
            let arabicDocsList = '';
            disapprovedDocs.forEach((doc, index) => {
                arabicDocsList += `${index + 1}. ${doc.name}`;
                arabicDocsList += '\n';
            });

            // Generate disapproved documents list for English section
            let englishDocsList = '';
            disapprovedDocs.forEach((doc, index) => {
                englishDocsList += `${index + 1}. ${doc.name}`;
                englishDocsList += '\n';
            });

            // Generate email body using the specified template
            const emailBody = `عزيزي/عزيزتي ${formattedFirstName}،
                    تم رفض المستندات التالية بسبب عدم الوضوح أو عدم استيفاء الشروط:

                    ${arabicDocsList}
                    يرجى إعادة إرسالها بصيغة واضحة (PDF أو JPEG أو PNG) لاستكمال إجراءات التسجيل.
                    إذا احتجت أي مساعدة، راسلنا على: 
                    <EMAIL>

                    ---

                    Dear ${formattedFirstName},
                    The following documents were disapproved due to unclear scan or missing requirements:

                    ${englishDocsList}
                    Please resend them in a clear format (PDF, JPEG, or PNG) so we can complete your registration.
                    If you need any help, contact us at:
                    <EMAIL>`;
            return {
                subject: defaultSubject,
                body: emailBody
            };
        }

        // Load email content (now generates from page data)
        function loadEmailContent() {
            try {
                // Check if email form fields exist
                const subjectField = document.getElementById('emailSubject');
                const bodyField = document.getElementById('emailBodyText');

                if (!subjectField || !bodyField) {
                    return;
                }

                const content = generateDefaultEmailContent();

                // Update form fields
                subjectField.value = content.subject;
                bodyField.value = content.body;

                // Update character count
                updateSubjectCharCount();


            } catch (error) {
                console.error('Error generating email content:', error);
                showNotification('Error generating email content', 'error');
            }
        }

        // Reset email content to default (regenerate from page data)
        function resetEmailContent() {
            if (!confirm('Are you sure you want to reset to default content?')) {
                return;
            }

            try {
                loadEmailContent(); // Regenerate from page data
                showNotification('Email content reset to default', 'success');
            } catch (error) {
                console.error('Error resetting email content:', error);
                showNotification('Error resetting email content', 'error');
            }
        }

        // Update subject character count
        function updateSubjectCharCount() {
            const subject = document.getElementById('emailSubject');
            const charCount = document.getElementById('subjectCharCount');
            if (subject && charCount) {
                charCount.textContent = subject.value.length;
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {


            // Load email content if there are disapproved documents
            @{
                var hasDisapprovedDocs = ViewBag.DisapprovedDocuments != null && ((List<Forms.ktech.ViewModels.Admin.DisapprovedDocumentInfo>)ViewBag.DisapprovedDocuments).Any();
            }
            const hasDisapprovedDocs = @hasDisapprovedDocs.ToString().ToLower();
            if (hasDisapprovedDocs) {
                // Use setTimeout to ensure DOM is fully loaded
                setTimeout(() => {
                    loadEmailContent();
                }, 100);
            }

            // Add character count listener for subject field
            const subjectField = document.getElementById('emailSubject');
            if (subjectField) {
                subjectField.addEventListener('input', updateSubjectCharCount);
            }
        });
    </script>
}
