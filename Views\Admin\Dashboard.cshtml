@{
    ViewData["Title"] = "Admin Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@section Breadcrumb {
    <li class="inline-flex items-center">
        <a asp-area="" asp-controller="Home" asp-action="Index" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
            <svg class="w-3 h-3 me-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                <path d="m19.707 9.293-2-2-7-7a1 1 0 0 0-1.414 0l-7 7-2 2a1 1 0 0 0 1.414 1.414L2 10.414V18a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1h3a2 2 0 0 0 2-2v-7.586l.293.293a1 1 0 0 0 1.414-1.414Z"/>
            </svg>
            Home
        </a>
    </li>
    <li>
        <div class="flex items-center">
            <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
            </svg>
            <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400">Admin Dashboard</span>
        </div>
    </li>
}

<!-- Welcome Section -->
<div class="bg-white rounded-lg shadow-lg p-6 mb-6 dark:bg-gray-800">
    <div class="flex items-center">
        <div class="flex-shrink-0">
            <div class="flex items-center justify-center h-12 w-12 rounded-md bg-blue-500 text-white">
                <i class="fas fa-tachometer-alt text-xl"></i>
            </div>
        </div>
        <div class="ml-4">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Welcome, @User.Identity?.Name!</h1>
            <p class="text-gray-600 dark:text-gray-300">Manage forms and view system statistics</p>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <!-- Total Submissions Card -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-12 w-12 rounded-md bg-green-500 text-white">
                    <i class="fas fa-file-alt text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Submissions</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">@ViewBag.TotalSubmissions</p>
            </div>
        </div>
        <div class="mt-4">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                <i class="fas fa-arrow-up mr-1"></i>
                All time
            </span>
        </div>
    </div>

    <!-- Pending Reviews Card -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-12 w-12 rounded-md bg-yellow-500 text-white">
                    <i class="fas fa-clock text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Recent Submissions</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">@ViewBag.PendingReviews</p>
            </div>
        </div>
        <div class="mt-4">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
                <i class="fas fa-calendar mr-1"></i>
                Last 30 days
            </span>
        </div>
    </div>

    <!-- Failed Validations Card -->
    <div class="bg-white rounded-lg shadow-lg p-6 dark:bg-gray-800">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="flex items-center justify-center h-12 w-12 rounded-md bg-red-500 text-white">
                    <i class="fas fa-exclamation-triangle text-xl"></i>
                </div>
            </div>
            <div class="ml-4">
                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">System Issues</p>
                <p class="text-2xl font-bold text-gray-900 dark:text-white">@ViewBag.FailedValidations</p>
            </div>
        </div>
        <div class="mt-4">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300">
                <i class="fas fa-shield-alt mr-1"></i>
                Monitoring
            </span>
        </div>
    </div>
</div>

<!-- Email Preview Section -->
<div class="bg-white rounded-lg shadow-lg dark:bg-gray-800 mb-6">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
                <i class="fas fa-envelope mr-2 text-blue-500"></i>
                Email Preview / معاينة البريد الإلكتروني
            </h2>
            <a asp-controller="Admin" asp-action="EmailPreview" class="text-blue-600 hover:text-blue-800 text-sm font-medium dark:text-blue-400 dark:hover:text-blue-300">
                View all pending emails / عرض جميع الرسائل المعلقة
                <i class="fas fa-arrow-right ml-1"></i>
            </a>
        </div>
    </div>

    <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Pending Notifications -->
            <div class="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="flex items-center justify-center h-10 w-10 rounded-md bg-blue-500 text-white">
                            <i class="fas fa-clock text-lg"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-blue-800 dark:text-blue-200">Pending Email Notifications</p>
                        <p class="text-sm text-blue-600 dark:text-blue-300">إشعارات البريد الإلكتروني المعلقة</p>
                        <p class="text-xl font-bold text-blue-900 dark:text-blue-100" id="pendingEmailCount">Loading...</p>
                    </div>
                </div>
            </div>

            <!-- Disapproved Documents -->
            <div class="bg-gradient-to-r from-orange-50 to-orange-100 dark:from-orange-900 dark:to-orange-800 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="flex items-center justify-center h-10 w-10 rounded-md bg-orange-500 text-white">
                            <i class="fas fa-file-times text-lg"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-orange-800 dark:text-orange-200">Awaiting Email Review</p>
                        <p class="text-sm text-orange-600 dark:text-orange-300">في انتظار مراجعة البريد الإلكتروني</p>
                        <p class="text-xl font-bold text-orange-900 dark:text-orange-100" id="disapprovedDocsCount">Loading...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="mt-6 flex flex-wrap gap-3">
            <a asp-controller="Admin" asp-action="EmailPreview" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
                <i class="fas fa-eye mr-2"></i>
                Preview & Send Emails / معاينة وإرسال الرسائل
            </a>
            <button type="button" onclick="refreshEmailStats()" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white text-sm font-medium rounded-lg transition-colors">
                <i class="fas fa-sync-alt mr-2"></i>
                Refresh / تحديث
            </button>
        </div>

        <!-- Help Text -->
        <div class="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <i class="fas fa-info-circle text-blue-500 mt-0.5"></i>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-gray-700 dark:text-gray-300">
                        <strong>Email Preview System:</strong> Review and customize document disapproval notifications before sending them to students.
                        This prevents duplicate emails and gives you full control over communication.
                    </p>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1" dir="rtl">
                        <strong>نظام معاينة البريد الإلكتروني:</strong> راجع وخصص إشعارات رفض المستندات قبل إرسالها للطلاب.
                        يمنع هذا الرسائل المكررة ويمنحك السيطرة الكاملة على التواصل.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Submissions Table -->
<div class="bg-white rounded-lg shadow-lg dark:bg-gray-800">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Recent Submissions</h2>
            <a asp-controller="Admin" asp-action="Submissions" class="text-blue-600 hover:text-blue-800 text-sm font-medium dark:text-blue-400 dark:hover:text-blue-300">
                View all submissions
                <i class="fas fa-arrow-right ml-1"></i>
            </a>
        </div>
    </div>
    
    <div class="overflow-x-auto">
        <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
            <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr>
                    <th scope="col" class="px-6 py-3">ID</th>
                    <th scope="col" class="px-6 py-3">Student Name</th>
                    <th scope="col" class="px-6 py-3">Submission Date</th>
                    <th scope="col" class="px-6 py-3">Status</th>
                    <th scope="col" class="px-6 py-3">Actions</th>
                </tr>
            </thead>
            <tbody>
                @if (ViewBag.RecentSubmissions != null)
                {
                    @foreach (var submission in ViewBag.RecentSubmissions)
                    {
                        <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                            <td class="px-6 py-4 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                                #@submission.Id
                            </td>
                            <td class="px-6 py-4">
                                @submission.StudentName
                            </td>
                            <td class="px-6 py-4">
                                @submission.CreatedDate.ToString("MMM dd, yyyy")
                            </td>
                            <td class="px-6 py-4">
                                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300">
                                    Submitted
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <a asp-controller="Admin" asp-action="ViewSubmission" asp-route-id="@submission.Id" class="font-medium text-blue-600 dark:text-blue-500 hover:underline">
                                    View
                                </a>
                            </td>
                        </tr>
                    }
                }
                else
                {
                    <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700">
                        <td colspan="5" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                            No submissions found
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
    
    <!-- Pagination -->
    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between">
            <span class="text-sm text-gray-700 dark:text-gray-400">
                Showing recent submissions
            </span>
            <nav aria-label="Table navigation">
                <ul class="inline-flex -space-x-px text-sm h-8">
                    <li>
                        <a href="#" class="flex items-center justify-center px-3 h-8 ml-0 leading-tight text-gray-500 bg-white border border-gray-300 rounded-l-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">Previous</a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">1</a>
                    </li>
                    <li>
                        <a href="#" class="flex items-center justify-center px-3 h-8 leading-tight text-gray-500 bg-white border border-gray-300 rounded-r-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">Next</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Load email statistics on page load
        $(document).ready(function() {
            loadEmailStats();
        });

        // Load email statistics
        async function loadEmailStats() {
            try {
                await loadEmailStatsFromAPI();
            } catch (error) {
                console.error('Error loading email stats:', error);
                document.getElementById('pendingEmailCount').textContent = 'Error';
                document.getElementById('disapprovedDocsCount').textContent = 'Error';
            }
        }

        // Load email statistics from API
        async function loadEmailStatsFromAPI() {
            try {
                const response = await fetch('/Admin/GetEmailStats');
                if (response.ok) {
                    const data = await response.json();
                    document.getElementById('pendingEmailCount').textContent = data.pendingCount || 0;
                    document.getElementById('disapprovedDocsCount').textContent = data.disapprovedDocsCount || 0;
                } else {
                    throw new Error('API call failed');
                }
            } catch (error) {
                console.error('Error loading email stats from API:', error);
                document.getElementById('pendingEmailCount').textContent = '0';
                document.getElementById('disapprovedDocsCount').textContent = '0';
            }
        }

        // Refresh email statistics
        function refreshEmailStats() {
            document.getElementById('pendingEmailCount').textContent = 'Loading...';
            document.getElementById('disapprovedDocsCount').textContent = 'Loading...';
            loadEmailStats();
        }
    </script>
}
