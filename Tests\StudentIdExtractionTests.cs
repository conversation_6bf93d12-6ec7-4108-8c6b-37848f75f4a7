using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Forms.ktech.Data;
using Forms.ktech.Services.SIS;
using Forms.ktech.Features.KuwaitiStudentInfo.Models;

namespace Forms.ktech.Tests
{
    /// <summary>
    /// Test class to verify the student ID extraction functionality
    /// in the SIS pre-fill authentication fix
    /// </summary>
    public class StudentIdExtractionTests
    {
        private readonly ILogger<StudentLookupService> _logger;
        private readonly IMemoryCache _cache;
        private readonly ApplicationDbContext _context;
        private readonly StudentLookupService _service;

        public StudentIdExtractionTests()
        {
            // Setup test dependencies
            var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            _logger = loggerFactory.CreateLogger<StudentLookupService>();
            _cache = new MemoryCache(new MemoryCacheOptions());
            
            // Setup in-memory database for testing
            var options = new DbContextOptionsBuilder<ApplicationDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;
            _context = new ApplicationDbContext(options);
            
            _service = new StudentLookupService(_context, _cache, _logger);
        }

        /// <summary>
        /// Test student ID extraction from valid KTECH email formats
        /// </summary>
        public async Task TestValidStudentIdExtraction()
        {
            // Arrange - Create test student in database
            var testStudent = new SisStudent
            {
                StudentID = "180100104",
                FullNameEN = "Test Student",
                Email = "<EMAIL>", // Intentionally different from Azure AD
                NationalID = "123456789012",
                Nationality = "KUWAIT"
            };
            
            _context.SisStudents.Add(testStudent);
            await _context.SaveChangesAsync();

            var viewModel = new StudentFormViewModel();

            // Test cases for valid student ID extraction
            var testCases = new[]
            {
                "<EMAIL>",
                "<EMAIL>", 
                "<EMAIL>",
                "<EMAIL>" // Test case insensitive
            };

            foreach (var email in testCases)
            {
                Console.WriteLine($"\n--- Testing email: {email} ---");
                
                // Act
                var result = await _service.PreFillFormAsync(viewModel, email);
                
                // Verify
                var expectedStudentId = email.Substring(0, email.IndexOf('@'));
                Console.WriteLine($"Expected Student ID: {expectedStudentId}");
                
                if (expectedStudentId == "180100104")
                {
                    // Should find the test student
                    Console.WriteLine($"Pre-filled: {result.IsPreFilled}");
                    Console.WriteLine($"Student ID: {result.PreFilledFromStudentId}");
                    Console.WriteLine($"Student Name: {result.StudentName}");
                    
                    if (result.IsPreFilled)
                    {
                        Console.WriteLine("✅ SUCCESS: Student found using extracted ID");
                    }
                    else
                    {
                        Console.WriteLine("❌ FAILED: Student should have been found");
                    }
                }
                else
                {
                    // Should not find student (doesn't exist in test DB)
                    Console.WriteLine($"Pre-filled: {result.IsPreFilled}");
                    
                    if (!result.IsPreFilled)
                    {
                        Console.WriteLine("✅ SUCCESS: No student found (expected for non-existent ID)");
                    }
                    else
                    {
                        Console.WriteLine("❌ UNEXPECTED: Student found when none should exist");
                    }
                }
            }
        }

        /// <summary>
        /// Test invalid email formats that should not extract student IDs
        /// </summary>
        public async Task TestInvalidStudentIdExtraction()
        {
            var viewModel = new StudentFormViewModel();

            // Test cases for invalid formats
            var invalidTestCases = new[]
            {
                "<EMAIL>",           // Non-KTECH domain
                "<EMAIL>",          // Non-numeric student ID
                "<EMAIL>",           // Too short student ID
                "<EMAIL>",         // Non-numeric student ID
                "",                             // Empty email
                "invalid-email",                // Invalid email format
                "<EMAIL>"        // Wrong domain
            };

            foreach (var email in invalidTestCases)
            {
                Console.WriteLine($"\n--- Testing invalid email: '{email}' ---");
                
                // Act
                var result = await _service.PreFillFormAsync(viewModel, email);
                
                // Verify - should not be pre-filled (no extraction or fallback match)
                Console.WriteLine($"Pre-filled: {result.IsPreFilled}");
                
                if (!result.IsPreFilled)
                {
                    Console.WriteLine("✅ SUCCESS: No pre-fill for invalid format (expected)");
                }
                else
                {
                    Console.WriteLine("❌ UNEXPECTED: Pre-fill occurred for invalid format");
                }
            }
        }

        /// <summary>
        /// Test fallback to email-based lookup when student ID extraction fails
        /// </summary>
        public async Task TestEmailFallbackMechanism()
        {
            // Arrange - Create test student with matching email
            var testStudent = new SisStudent
            {
                StudentID = "999999999", // Different from email prefix
                FullNameEN = "Fallback Test Student",
                Email = "<EMAIL>", // This will match the email lookup
                NationalID = "999999999999",
                Nationality = "KUWAIT"
            };
            
            _context.SisStudents.Add(testStudent);
            await _context.SaveChangesAsync();

            var viewModel = new StudentFormViewModel();

            Console.WriteLine("\n--- Testing Email Fallback Mechanism ---");
            Console.WriteLine("Email: <EMAIL>");
            Console.WriteLine("Expected: Student ID extraction fails, email lookup succeeds");

            // Act
            var result = await _service.PreFillFormAsync(viewModel, "<EMAIL>");

            // Verify
            Console.WriteLine($"Pre-filled: {result.IsPreFilled}");
            Console.WriteLine($"Student ID: {result.PreFilledFromStudentId}");
            Console.WriteLine($"Student Name: {result.StudentName}");

            if (result.IsPreFilled && result.PreFilledFromStudentId == "999999999")
            {
                Console.WriteLine("✅ SUCCESS: Email fallback mechanism worked");
            }
            else
            {
                Console.WriteLine("❌ FAILED: Email fallback mechanism failed");
            }
        }

        /// <summary>
        /// Run all tests
        /// </summary>
        public async Task RunAllTests()
        {
            Console.WriteLine("=== SIS Pre-Fill Authentication Fix Tests ===\n");

            try
            {
                await TestValidStudentIdExtraction();
                await TestInvalidStudentIdExtraction();
                await TestEmailFallbackMechanism();

                Console.WriteLine("\n=== Test Summary ===");
                Console.WriteLine("All tests completed. Check individual results above.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ TEST ERROR: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
            }
            finally
            {
                _context.Dispose();
                _cache.Dispose();
            }
        }
    }

    /// <summary>
    /// Simple test runner program
    /// </summary>
    public class TestRunner
    {
        public static async Task Main(string[] args)
        {
            var tests = new StudentIdExtractionTests();
            await tests.RunAllTests();
            
            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }
    }
}
