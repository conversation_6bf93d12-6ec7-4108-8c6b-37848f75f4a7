using Forms.ktech.Data;
using Forms.ktech.Models;
using Forms.ktech.ViewModels.Admin;
using Microsoft.EntityFrameworkCore;
using System.Text;

namespace Forms.ktech.Services
{
    /// <summary>
    /// Service for integrating and processing email templates with dynamic content
    /// </summary>
    public class EmailTemplateService : IEmailTemplateService
    {
        private readonly IWebHostEnvironment _environment;
        private readonly IConfiguration _configuration;
        private readonly FormsKTechContext _context;
        private readonly ILogger<EmailTemplateService> _logger;

        public EmailTemplateService(
            IWebHostEnvironment environment,
            IConfiguration configuration,
            FormsKTechContext context,
            ILogger<EmailTemplateService> logger)
        {
            _environment = environment;
            _configuration = configuration;
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Generates email content using the existing KTECH template with dynamic content injection
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="disapprovedDocuments">List of disapproved documents</param>
        /// <returns>Complete HTML email content</returns>
        public async Task<string> GenerateEmailContentAsync(int submissionId, List<DisapprovedDocumentInfo> disapprovedDocuments)
        {
            try
            {
                // Load the existing email template
                var template = await LoadEmailTemplateAsync();
                
                // Get submission details
                var submission = await _context.StudentInfos
                    .Include(s => s.SisStudent)
                    .FirstOrDefaultAsync(s => s.Id == submissionId);

                if (submission == null)
                {
                    throw new ArgumentException($"Submission {submissionId} not found");
                }

                // Generate dynamic content for disapproved documents
                var dynamicContent = GenerateDisapprovalContent(submission, disapprovedDocuments);
                
                // Replace template placeholders with dynamic content
                var emailContent = template
                    .Replace("[PageName]", "Document Review Required / مراجعة المستندات مطلوبة")
                    .Replace("[PageContent]", dynamicContent);

                return emailContent;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating email content for submission {SubmissionId}", submissionId);
                throw;
            }
        }

        /// <summary>
        /// Loads the email template from the file system
        /// </summary>
        /// <returns>HTML template content</returns>
        public async Task<string> LoadEmailTemplateAsync()
        {
            try
            {
                var templatePath = Path.Combine(_environment.WebRootPath, "Template", "Email-Template.html");
                
                if (!File.Exists(templatePath))
                {
                    throw new FileNotFoundException($"Email template not found at: {templatePath}");
                }

                var templateContent = await File.ReadAllTextAsync(templatePath);
                
                _logger.LogDebug("Email template loaded successfully from {TemplatePath}", templatePath);
                
                return templateContent;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading email template");
                throw;
            }
        }

        /// <summary>
        /// Generates the dynamic content section for document disapprovals
        /// </summary>
        /// <param name="submission">The submission</param>
        /// <param name="disapprovedDocuments">List of disapproved documents</param>
        /// <returns>HTML content for the email body</returns>
        private string GenerateDisapprovalContent(Features.KuwaitiStudentInfo.Models.StudentInfo submission, List<DisapprovedDocumentInfo> disapprovedDocuments)
        {
            var studentName = submission.GetStudentName();
            var submissionId = submission.Id;
            var deadline = DateTime.Now.AddDays(7).ToString("MMMM dd, yyyy");
            var updateLink = GenerateUpdateLink(submission);

            var documentIssuesList = new StringBuilder();
            foreach (var doc in disapprovedDocuments)
            {
                documentIssuesList.AppendLine($@"
                    <div style=""margin-bottom: 15px; padding: 12px; border-left: 4px solid #ef4444; background-color: #fef2f2; border-radius: 4px;"">
                        <h4 style=""color: #ef4444; margin: 0 0 8px 0; font-size: 16px; font-weight: bold;"">{doc.DocumentName}</h4>
                        <p style=""margin: 4px 0; color: #374151; font-size: 14px;""><strong>Reason / السبب:</strong> {doc.Reason}</p>
                        {(!string.IsNullOrEmpty(doc.Comments) ? $"<p style=\"margin: 4px 0; color: #374151; font-size: 14px;\"><strong>Comments / تعليقات:</strong> {doc.Comments}</p>" : "")}
                        <p style=""margin: 4px 0; color: #6b7280; font-size: 12px;""><strong>Date / التاريخ:</strong> {doc.DisapprovalDate:MMMM dd, yyyy}</p>
                    </div>");
            }

            return $@"
                Dear {studentName} / عزيزي {studentName}, <br /><br />
                
                We have reviewed your submission #{submissionId} and found that some documents require attention before we can proceed with your application.<br />
                <span style=""direction: rtl; display: block; margin: 10px 0;"">لقد راجعنا طلبك رقم #{submissionId} ووجدنا أن بعض المستندات تحتاج إلى مراجعة قبل أن نتمكن من المتابعة مع طلبك.</span><br />
                
                <strong style=""color: #ef4444; font-size: 16px;"">Documents Requiring Attention / المستندات التي تحتاج مراجعة:</strong><br /><br />
                
                {documentIssuesList}
                
                <div style=""background-color: #fef3c7; padding: 15px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #f59e0b;"">
                    <p style=""margin: 0; font-weight: bold; color: #92400e; font-size: 14px;"">Please update these documents by {deadline}.</p>
                    <p style=""margin: 8px 0 0 0; font-weight: bold; color: #92400e; direction: rtl; text-align: right; font-size: 14px;"">يرجى تحديث هذه المستندات بحلول {deadline}.</p>
                </div>
                
                <div style=""text-align: center; margin: 25px 0;"">
                    <a href=""{updateLink}"" style=""display: inline-block; background-color: #1e40af; color: white; padding: 12px 25px; text-decoration: none; border-radius: 6px; font-weight: bold; font-size: 14px;"">Update My Submission / تحديث طلبي</a>
                </div>
                
                If you have any questions, please contact <NAME_EMAIL><br />
                <span style=""direction: rtl; display: block; margin: 10px 0;"">إذا كان لديك أي أسئلة، يرجى الاتصال بنا على <EMAIL></span><br />
                
                Best regards,<br />
                KTECH Registration System Team<br />
                <span style=""direction: rtl; display: block; margin: 5px 0;"">مع أطيب التحيات،<br />فريق نظام التسجيل - الكلية التقنية</span>";
        }

        /// <summary>
        /// Generates email content with custom plain text content
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="customSubject">Custom email subject</param>
        /// <param name="customBody">Custom plain text body content</param>
        /// <returns>Complete HTML email content</returns>
        public async Task<string> GenerateCustomEmailContentAsync(int submissionId, string customSubject, string customBody)
        {
            try
            {
                // Load the base template
                var template = await LoadEmailTemplateAsync();

                // Get submission details
                var submission = await _context.StudentInfos
                    .FirstOrDefaultAsync(s => s.Id == submissionId);

                if (submission == null)
                {
                    throw new ArgumentException($"Submission {submissionId} not found");
                }

                // Convert plain text body to HTML with proper formatting
                var htmlBody = ConvertPlainTextToHtml(customBody, submission);

                // Replace template placeholders with custom content
                var emailContent = template
                    .Replace("[PageName]", customSubject)
                    .Replace("[PageContent]", htmlBody);

                return emailContent;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating custom email content for submission {SubmissionId}", submissionId);
                throw;
            }
        }

        /// <summary>
        /// Converts plain text content to HTML with proper formatting
        /// </summary>
        /// <param name="plainText">Plain text content</param>
        /// <param name="submission">The submission for context</param>
        /// <returns>HTML formatted content</returns>
        private string ConvertPlainTextToHtml(string plainText, Features.KuwaitiStudentInfo.Models.StudentInfo submission)
        {
            if (string.IsNullOrWhiteSpace(plainText))
            {
                return "";
            }

            var studentName = submission.GetStudentName();
            var submissionId = submission.Id;
            var updateLink = GenerateUpdateLink(submission);

            // Convert line breaks to HTML
            var htmlContent = plainText
                .Replace("\r\n", "\n")
                .Replace("\n", "<br />")
                .Replace("\t", "&nbsp;&nbsp;&nbsp;&nbsp;");

            // Wrap in proper email structure
            return $@"
                Dear {studentName} / عزيزي {studentName}, <br /><br />

                {htmlContent}<br /><br />

                <div style=""text-align: center; margin: 25px 0;"">
                    <a href=""{updateLink}"" style=""display: inline-block; background-color: #1e40af; color: white; padding: 12px 25px; text-decoration: none; border-radius: 6px; font-weight: bold; font-size: 14px;"">Update My Submission / تحديث طلبي</a>
                </div>";
        }

        /// <summary>
        /// Generates update link for student to modify their submission
        /// </summary>
        /// <param name="submission">The submission</param>
        /// <returns>Update link URL</returns>
        private string GenerateUpdateLink(Features.KuwaitiStudentInfo.Models.StudentInfo submission)
        {
            var baseUrl = _configuration["Application:BaseUrl"] ?? "https://localhost:7045";
            return $"{baseUrl}/KuwaitiStudentInfo/CollectInfo";
        }
    }
}
