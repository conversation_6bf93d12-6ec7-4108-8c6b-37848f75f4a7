using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Forms.ktech.Data;

namespace Forms.ktech.Features.KuwaitiStudentInfo.Models
{
    /// <summary>
    /// Enumeration for submission approval status
    /// </summary>
    public enum SubmissionStatus
    {
        /// <summary>
        /// Submission is pending review
        /// </summary>
        Pending = 0,

        /// <summary>
        /// Submission has been approved
        /// </summary>
        Approved = 1,

        /// <summary>
        /// Submission has been rejected
        /// </summary>
        Rejected = 2,

        /// <summary>
        /// Submission has been flagged for additional review
        /// </summary>
        Flagged = 3
    }

    /// <summary>
    /// Entity class for Kuwaiti Student Information form submissions
    /// Inherits from BaseEntity to get common properties like Id, CreatedDate, SubmissionGuid, etc.
    /// </summary>
    public class StudentInfo : BaseEntity
    {
        #region Student Reference

        /// <summary>
        /// Foreign key reference to the SIS student record
        /// Links this form submission to the synchronized student data
        /// </summary>
        public int? SisStudentId { get; set; }

        /// <summary>
        /// Navigation property to the SIS student record
        /// Contains the authoritative student data from SIS
        /// </summary>
        [ForeignKey(nameof(SisStudentId))]
        public virtual SisStudent? SisStudent { get; set; }

        /// <summary>
        /// Student's mobile phone number
        /// </summary>
        [Required]
        [StringLength(20)]
        public string StudentMobileNumber { get; set; } = string.Empty;

        /// <summary>
        /// Indicates if the student is a Kuwaiti citizen
        /// This is form-specific data that may differ from SIS nationality
        /// </summary>
        public bool StudentIsKuwaiti { get; set; }

        #endregion

        #region Father Information

        /// <summary>
        /// Full name of the student's father
        /// </summary>
        [Required]
        [StringLength(200)]
        public string FatherName { get; set; } = string.Empty;

        /// <summary>
        /// Indicates if the father is a Kuwaiti citizen
        /// </summary>
        public bool FatherIsKuwaiti { get; set; }

        /// <summary>
        /// Indicates if the father is deceased
        /// </summary>
        public bool FatherIsDeceased { get; set; }

        #endregion

        #region Mother Information

        /// <summary>
        /// Full name of the student's mother
        /// </summary>
        [Required]
        [StringLength(200)]
        public string MotherName { get; set; } = string.Empty;

        /// <summary>
        /// Indicates if the mother is a Kuwaiti citizen
        /// </summary>
        public bool MotherIsKuwaiti { get; set; }

        /// <summary>
        /// Indicates if the mother is deceased
        /// </summary>
        public bool MotherIsDeceased { get; set; }

        #endregion

        #region File Paths for Uploaded Documents

        /// <summary>
        /// File path for student's civil ID document
        /// </summary>
        [StringLength(500)]
        public string? StudentCivilIdPath { get; set; }

        /// <summary>
        /// File path for student's nationality certificate (if not Kuwaiti)
        /// </summary>
        [StringLength(500)]
        public string? StudentNationalityCertificatePath { get; set; }

        /// <summary>
        /// File path for father's civil ID document
        /// </summary>
        [StringLength(500)]
        public string? FatherCivilIdPath { get; set; }

        /// <summary>
        /// File path for father's nationality certificate (if not Kuwaiti)
        /// </summary>
        [StringLength(500)]
        public string? FatherNationalityCertificatePath { get; set; }

        /// <summary>
        /// File path for mother's civil ID document
        /// </summary>
        [StringLength(500)]
        public string? MotherCivilIdPath { get; set; }

        /// <summary>
        /// File path for mother's nationality certificate (if not Kuwaiti)
        /// </summary>
        [StringLength(500)]
        public string? MotherNationalityCertificatePath { get; set; }

        /// <summary>
        /// File path for student's birth certificate (required for all eligible cases)
        /// </summary>
        [StringLength(500)]
        public string? StudentBirthCertificatePath { get; set; }

        /// <summary>
        /// File path for father's death certificate (required if father is Kuwaiti and deceased)
        /// </summary>
        [StringLength(500)]
        public string? FatherDeathCertificatePath { get; set; }

        /// <summary>
        /// File path for mother's death certificate (required if mother is Kuwaiti and deceased)
        /// </summary>
        [StringLength(500)]
        public string? MotherDeathCertificatePath { get; set; }

        #endregion

        #region Submission Status Tracking

        /// <summary>
        /// Current approval status of the submission
        /// </summary>
        [Required]
        public SubmissionStatus Status { get; set; } = SubmissionStatus.Pending;

        /// <summary>
        /// Date and time when the submission status was last changed
        /// </summary>
        public DateTime? StatusChangedDate { get; set; }

        /// <summary>
        /// User ID of the admin who changed the submission status
        /// </summary>
        [StringLength(450)]
        public string? StatusChangedByUserId { get; set; }

        /// <summary>
        /// Reason for rejection (required when Status is Rejected)
        /// </summary>
        [StringLength(1000)]
        public string? RejectionReason { get; set; }

        /// <summary>
        /// Notes for flagged submissions (optional when Status is Flagged)
        /// </summary>
        [StringLength(2000)]
        public string? FlagNotes { get; set; }

        /// <summary>
        /// Date and time when the submission was approved
        /// </summary>
        public DateTime? ApprovedDate { get; set; }

        /// <summary>
        /// Date and time when the submission was rejected
        /// </summary>
        public DateTime? RejectedDate { get; set; }

        /// <summary>
        /// Date and time when the submission was flagged
        /// </summary>
        public DateTime? FlaggedDate { get; set; }

        #endregion

        #region Business Logic Methods

        /// <summary>
        /// Gets the student name from the linked SIS record
        /// </summary>
        /// <returns>Student name from SIS, or empty string if not linked</returns>
        public string GetStudentName()
        {
            return SisStudent?.FullNameEN ?? string.Empty;
        }

        /// <summary>
        /// Gets the student civil ID from the linked SIS record
        /// </summary>
        /// <returns>Student civil ID from SIS, or empty string if not linked</returns>
        public string GetStudentCivilId()
        {
            return SisStudent?.NationalID ?? string.Empty;
        }

        /// <summary>
        /// Gets the student email from the linked SIS record
        /// </summary>
        /// <returns>Student email from SIS, or empty string if not linked</returns>
        public string GetStudentEmail()
        {
            return SisStudent?.Email ?? string.Empty;
        }

        /// <summary>
        /// Determines the eligibility case based on citizenship status
        /// Case A: Student is Kuwaiti
        /// Case B: Father is Kuwaiti (student is not)
        /// Case C: Mother is Kuwaiti (student and father are not)
        /// Case D: None are Kuwaiti (ineligible)
        /// </summary>
        /// <returns>The eligibility case (A, B, C, or D)</returns>
        public string GetEligibilityCase()
        {
            if (StudentIsKuwaiti)
                return "A";
            else if (FatherIsKuwaiti)
                return "B";
            else if (MotherIsKuwaiti)
                return "C";
            else
                return "D";
        }

        /// <summary>
        /// Checks if the student is eligible for the program
        /// </summary>
        /// <returns>True if eligible (cases A, B, or C), false if ineligible (case D)</returns>
        public bool IsEligible()
        {
            return GetEligibilityCase() != "D";
        }

        /// <summary>
        /// Approves the submission
        /// </summary>
        /// <param name="approvedByUserId">The admin user ID who approved the submission</param>
        public void Approve(string? approvedByUserId = null)
        {
            Status = SubmissionStatus.Approved;
            StatusChangedDate = DateTime.UtcNow;
            StatusChangedByUserId = approvedByUserId;
            ApprovedDate = DateTime.UtcNow;

            // Clear rejection/flag data
            RejectionReason = null;
            FlagNotes = null;
            RejectedDate = null;
            FlaggedDate = null;

            MarkAsUpdated(approvedByUserId);
        }

        /// <summary>
        /// Rejects the submission
        /// </summary>
        /// <param name="reason">The reason for rejection</param>
        /// <param name="rejectedByUserId">The admin user ID who rejected the submission</param>
        public void Reject(string reason, string? rejectedByUserId = null)
        {
            Status = SubmissionStatus.Rejected;
            StatusChangedDate = DateTime.UtcNow;
            StatusChangedByUserId = rejectedByUserId;
            RejectedDate = DateTime.UtcNow;
            RejectionReason = reason;

            // Clear approval/flag data
            ApprovedDate = null;
            FlagNotes = null;
            FlaggedDate = null;

            MarkAsUpdated(rejectedByUserId);
        }

        /// <summary>
        /// Flags the submission for review
        /// </summary>
        /// <param name="notes">Optional notes for the flag</param>
        /// <param name="flaggedByUserId">The admin user ID who flagged the submission</param>
        public void Flag(string? notes = null, string? flaggedByUserId = null)
        {
            Status = SubmissionStatus.Flagged;
            StatusChangedDate = DateTime.UtcNow;
            StatusChangedByUserId = flaggedByUserId;
            FlaggedDate = DateTime.UtcNow;
            FlagNotes = notes;

            // Clear approval/rejection data
            ApprovedDate = null;
            RejectionReason = null;
            RejectedDate = null;

            MarkAsUpdated(flaggedByUserId);
        }

        /// <summary>
        /// Gets the display text for the current status
        /// </summary>
        /// <returns>Human-readable status text</returns>
        public string GetStatusDisplayText()
        {
            return Status switch
            {
                SubmissionStatus.Pending => "Pending Review",
                SubmissionStatus.Approved => "Approved",
                SubmissionStatus.Rejected => "Rejected",
                SubmissionStatus.Flagged => "Flagged for Review",
                _ => "Unknown"
            };
        }

        /// <summary>
        /// Gets the CSS class for status display styling
        /// </summary>
        /// <returns>CSS class name for status styling</returns>
        public string GetStatusCssClass()
        {
            return Status switch
            {
                SubmissionStatus.Pending => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
                SubmissionStatus.Approved => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
                SubmissionStatus.Rejected => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
                SubmissionStatus.Flagged => "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
                _ => "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
            };
        }

        #endregion
    }
}
