using Microsoft.EntityFrameworkCore;
using System.Net.Mail;
using System.Net;
using System.Text;
using Forms.ktech.Data;
using Forms.ktech.Models;
using Forms.ktech.Features.KuwaitiStudentInfo.Models;

namespace Forms.ktech.Services
{
    /// <summary>
    /// Service for managing email notifications to students
    /// </summary>
    public class EmailNotificationService : IEmailNotificationService
    {
        private readonly FormsKTechContext _context;
        private readonly ILogger<EmailNotificationService> _logger;
        private readonly IConfiguration _configuration;
        private readonly IWebHostEnvironment _environment;

        public EmailNotificationService(
            FormsKTechContext context,
            ILogger<EmailNotificationService> logger,
            IConfiguration configuration,
            IWebHostEnvironment environment)
        {
            _context = context;
            _logger = logger;
            _configuration = configuration;
            _environment = environment;
        }

        /// <summary>
        /// Sends document disapproval notification to student
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="disapprovedDocuments">Array of disapproved document types</param>
        /// <returns>The email notification record</returns>
        public async Task<EmailNotification> SendDocumentDisapprovalNotificationAsync(int submissionId, string[] disapprovedDocuments)
        {
            try
            {
                _logger.LogInformation("Sending document disapproval notification for submission {SubmissionId}", submissionId);

                // Get submission with SIS data
                var submission = await _context.StudentInfos
                    .Include(s => s.SisStudent)
                    .FirstOrDefaultAsync(s => s.Id == submissionId);

                if (submission == null)
                {
                    throw new ArgumentException($"Submission {submissionId} not found");
                }

                // Get student email - prefer SIS email, fallback to user email
                var studentEmail = GetStudentEmail(submission);
                if (string.IsNullOrEmpty(studentEmail))
                {
                    throw new InvalidOperationException($"No email address found for submission {submissionId}");
                }

                // Get disapproval details
                var disapprovalDetails = await GetDisapprovalDetailsAsync(submissionId, disapprovedDocuments);

                // Generate email content
                var subject = GenerateDisapprovalEmailSubject(submissionId);
                var body = await GenerateDisapprovalEmailBodyAsync(submission, disapprovalDetails);

                // Create email notification record
                var emailNotification = new EmailNotification
                {
                    SubmissionId = submissionId,
                    EmailType = "DocumentDisapproval",
                    RecipientEmail = studentEmail,
                    Subject = subject,
                    Body = body,
                    Status = EmailStatus.Pending
                };

                _context.EmailNotifications.Add(emailNotification);
                await _context.SaveChangesAsync();

                // Send email
                await SendEmailAsync(emailNotification);

                _logger.LogInformation("Document disapproval notification sent for submission {SubmissionId}", submissionId);

                return emailNotification;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending document disapproval notification for submission {SubmissionId}", submissionId);
                throw;
            }
        }

        /// <summary>
        /// Sends email notification
        /// </summary>
        /// <param name="emailNotification">The email notification to send</param>
        /// <returns>True if sent successfully</returns>
        public async Task<bool> SendEmailAsync(EmailNotification emailNotification)
        {
            try
            {
                _logger.LogInformation("Starting email send process for submission {SubmissionId} to {RecipientEmail}",
                    emailNotification.SubmissionId, emailNotification.RecipientEmail);

                // Get SMTP configuration with detailed logging
                var smtpHost = _configuration["Email:SmtpHost"];
                var smtpPort = int.Parse(_configuration["Email:SmtpPort"] ?? "587");
                var smtpUsername = _configuration["Email:SmtpUsername"];
                var smtpPassword = _configuration["Email:SmtpPassword"];
                var fromEmail = _configuration["Email:FromEmail"];
                var fromName = _configuration["Email:FromName"] ?? "KTECH Forms System";

                _logger.LogInformation("Email configuration: Host={SmtpHost}, Port={SmtpPort}, Username={SmtpUsername}, FromEmail={FromEmail}",
                    smtpHost, smtpPort, smtpUsername, fromEmail);

                // Validate configuration
                if (string.IsNullOrEmpty(smtpHost) || string.IsNullOrEmpty(smtpUsername) || string.IsNullOrEmpty(fromEmail))
                {
                    var missingConfig = new List<string>();
                    if (string.IsNullOrEmpty(smtpHost)) missingConfig.Add("SmtpHost");
                    if (string.IsNullOrEmpty(smtpUsername)) missingConfig.Add("SmtpUsername");
                    if (string.IsNullOrEmpty(fromEmail)) missingConfig.Add("FromEmail");

                    _logger.LogError("Email configuration is incomplete. Missing: {MissingConfig}", string.Join(", ", missingConfig));

                    emailNotification.MarkAsFailed($"Email configuration is incomplete. Missing: {string.Join(", ", missingConfig)}");
                    await _context.SaveChangesAsync();
                    return false;
                }

                // Validate password separately
                if (string.IsNullOrEmpty(smtpPassword))
                {
                    _logger.LogError("SMTP password is missing from configuration");
                    emailNotification.MarkAsFailed("SMTP password is missing from configuration");
                    await _context.SaveChangesAsync();
                    return false;
                }

                // Validate email addresses first
                if (!IsValidEmail(emailNotification.RecipientEmail))
                {
                    _logger.LogError("Invalid recipient email address: {RecipientEmail}", emailNotification.RecipientEmail);
                    emailNotification.MarkAsFailed($"Invalid recipient email address: {emailNotification.RecipientEmail}");
                    await _context.SaveChangesAsync();
                    return false;
                }

                if (!IsValidEmail(fromEmail))
                {
                    _logger.LogError("Invalid sender email address: {FromEmail}", fromEmail);
                    emailNotification.MarkAsFailed($"Invalid sender email address: {fromEmail}");
                    await _context.SaveChangesAsync();
                    return false;
                }

                _logger.LogInformation("Creating SMTP client for {SmtpHost}:{SmtpPort} with SSL enabled", smtpHost, smtpPort);

                using var client = new SmtpClient(smtpHost, smtpPort);
                client.EnableSsl = true;
                client.Credentials = new NetworkCredential(smtpUsername, smtpPassword);
                client.Timeout = 60000; // 60 seconds timeout for better reliability
                client.DeliveryMethod = SmtpDeliveryMethod.Network;

                // Test SMTP connection first
                _logger.LogInformation("Testing SMTP connection to {SmtpHost}:{SmtpPort}...", smtpHost, smtpPort);

                try
                {
                    // Create a simple test to verify connection
                    using var testMessage = new MailMessage(fromEmail, emailNotification.RecipientEmail, "Connection Test", "Test");
                    // Don't send the test message, just validate the connection setup
                }
                catch (Exception connEx)
                {
                    _logger.LogError(connEx, "SMTP connection test failed: {Message}", connEx.Message);
                    emailNotification.MarkAsFailed($"SMTP connection test failed: {connEx.Message}");
                    await _context.SaveChangesAsync();
                    return false;
                }

                _logger.LogInformation("Creating email message from {FromEmail} to {RecipientEmail}", fromEmail, emailNotification.RecipientEmail);

                using var message = new MailMessage();
                message.From = new MailAddress(fromEmail, fromName);
                message.To.Add(emailNotification.RecipientEmail);
                message.Subject = emailNotification.Subject;
                message.Body = emailNotification.Body;
                message.IsBodyHtml = true;
                message.BodyEncoding = Encoding.UTF8;
                message.SubjectEncoding = Encoding.UTF8;

                // Add additional headers for better delivery
                message.Headers.Add("X-Mailer", "KTECH Forms System");
                message.Headers.Add("X-Priority", "3"); // Normal priority
                message.Priority = MailPriority.Normal;

                _logger.LogInformation("Email message created. From: {FromEmail}, To: {ToEmail}, Subject: {Subject}, BodyLength: {BodyLength}, IsBodyHtml: {IsBodyHtml}",
                    fromEmail, emailNotification.RecipientEmail, emailNotification.Subject, emailNotification.Body?.Length ?? 0, message.IsBodyHtml);

                // Log first 200 characters of body for debugging
                var bodyPreview = emailNotification.Body?.Length > 200
                    ? emailNotification.Body.Substring(0, 200) + "..."
                    : emailNotification.Body ?? "";
                _logger.LogInformation("Email body preview: {BodyPreview}", bodyPreview);

                _logger.LogInformation("Attempting to send email via SMTP to {SmtpHost}:{SmtpPort} using credentials {SmtpUsername}...",
                    smtpHost, smtpPort, smtpUsername);

                // Record send attempt time
                var sendStartTime = DateTime.UtcNow;

                try
                {
                    await client.SendMailAsync(message);
                    var sendDuration = DateTime.UtcNow - sendStartTime;
                    _logger.LogInformation("SMTP send completed successfully in {Duration}ms. Email accepted by server.", sendDuration.TotalMilliseconds);

                    // Additional verification - check if the message was actually accepted
                    _logger.LogInformation("Email message details - MessageId: {MessageId}, Recipients: {Recipients}",
                        message.Headers["Message-ID"] ?? "Not set", string.Join(", ", message.To.Select(t => t.Address)));
                }
                catch (SmtpFailedRecipientsException recipEx)
                {
                    _logger.LogError(recipEx, "SMTP failed recipients error: {Message}. Failed recipients: {FailedRecipients}",
                        recipEx.Message, string.Join(", ", recipEx.InnerExceptions.Select(e => e.FailedRecipient)));
                    throw;
                }
                catch (SmtpException smtpEx)
                {
                    _logger.LogError(smtpEx, "SMTP error occurred: StatusCode={StatusCode}, Message={Message}. This may indicate authentication, relay, or delivery issues.",
                        smtpEx.StatusCode, smtpEx.Message);
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "General error during SMTP send: {ExceptionType} - {Message}", ex.GetType().Name, ex.Message);
                    throw;
                }

                emailNotification.MarkAsSent();
                await _context.SaveChangesAsync();

                _logger.LogInformation("Email sent successfully to {Email} for submission {SubmissionId}. EmailId: {EmailId}",
                    emailNotification.RecipientEmail, emailNotification.SubmissionId, emailNotification.Id);

                return true;
            }
            catch (SmtpException smtpEx)
            {
                _logger.LogError(smtpEx, "SMTP error sending email to {Email} for submission {SubmissionId}. StatusCode: {StatusCode}, Message: {Message}",
                    emailNotification.RecipientEmail, emailNotification.SubmissionId, smtpEx.StatusCode, smtpEx.Message);

                var errorMessage = $"SMTP Error: {smtpEx.StatusCode} - {smtpEx.Message}";
                emailNotification.MarkAsFailed(errorMessage);
                await _context.SaveChangesAsync();

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "General error sending email to {Email} for submission {SubmissionId}. Type: {ExceptionType}, Message: {Message}",
                    emailNotification.RecipientEmail, emailNotification.SubmissionId, ex.GetType().Name, ex.Message);

                emailNotification.MarkAsFailed($"{ex.GetType().Name}: {ex.Message}");
                await _context.SaveChangesAsync();

                return false;
            }
        }



        /// <summary>
        /// Retries failed email notifications
        /// </summary>
        /// <param name="maxRetries">Maximum number of retry attempts</param>
        /// <returns>Number of emails processed</returns>
        public async Task<int> RetryFailedEmailsAsync(int maxRetries = 3)
        {
            var failedEmails = await _context.EmailNotifications
                .Where(en => en.Status == EmailStatus.Failed && en.RetryCount < maxRetries)
                .ToListAsync();

            int processedCount = 0;

            foreach (var email in failedEmails)
            {
                email.IncrementRetry();
                await _context.SaveChangesAsync();

                var success = await SendEmailAsync(email);
                if (success)
                {
                    processedCount++;
                }
            }

            return processedCount;
        }

        /// <summary>
        /// Gets email notification history for a submission
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <returns>List of email notifications</returns>
        public async Task<List<EmailNotification>> GetEmailHistoryAsync(int submissionId)
        {
            return await _context.EmailNotifications
                .Where(en => en.SubmissionId == submissionId)
                .OrderByDescending(en => en.CreatedDate)
                .ToListAsync();
        }

        /// <summary>
        /// Validates email address format
        /// </summary>
        /// <param name="email">Email address to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        private static bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                var addr = new MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Gets student email address from submission or SIS data
        /// </summary>
        /// <param name="submission">The submission</param>
        /// <returns>Student email address</returns>
        private static string? GetStudentEmail(StudentInfo submission)
        {
            // Prefer SIS email if available
            if (submission.SisStudent != null && !string.IsNullOrEmpty(submission.SisStudent.Email))
            {
                return submission.SisStudent.Email;
            }

            // TODO: Add logic to get email from user account if needed
            // This would require additional user context or storing email in submission

            return null;
        }

        /// <summary>
        /// Gets disapproval details for documents
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="documentTypes">The document types</param>
        /// <returns>List of disapproval details</returns>
        private async Task<List<DocumentDisapprovalDetail>> GetDisapprovalDetailsAsync(int submissionId, string[] documentTypes)
        {
            var details = new List<DocumentDisapprovalDetail>();

            foreach (var documentType in documentTypes)
            {
                var approval = await _context.DocumentApprovals
                    .FirstOrDefaultAsync(da => da.SubmissionId == submissionId && da.DocumentType == documentType);

                if (approval != null && approval.Status == DocumentApprovalStatus.Disapproved)
                {
                    details.Add(new DocumentDisapprovalDetail
                    {
                        DocumentType = documentType,
                        DocumentName = approval.GetDocumentDisplayName(),
                        Reason = approval.DisapprovalReason ?? "No reason provided",
                        Comments = approval.Comments
                    });
                }
            }

            return details;
        }

        /// <summary>
        /// Generates email subject for disapproval notification
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <returns>Email subject</returns>
        private static string GenerateDisapprovalEmailSubject(int submissionId)
        {
            return $"Document Update Required - Submission #{submissionId} / مطلوب تحديث المستندات - طلب رقم #{submissionId}";
        }

        /// <summary>
        /// Generates email body for disapproval notification
        /// </summary>
        /// <param name="submission">The submission</param>
        /// <param name="disapprovalDetails">The disapproval details</param>
        /// <returns>Email body HTML</returns>
        private async Task<string> GenerateDisapprovalEmailBodyAsync(StudentInfo submission, List<DocumentDisapprovalDetail> disapprovalDetails)
        {
            var studentName = submission.GetStudentName();
            var submissionId = submission.Id;
            var deadline = DateTime.Now.AddDays(7).ToString("MMMM dd, yyyy"); // 7 days from now
            var updateLink = GenerateUpdateLink(submission);

            var documentIssuesList = new StringBuilder();
            foreach (var detail in disapprovalDetails)
            {
                documentIssuesList.AppendLine($@"
                    <div class=""document-item"">
                        <h3 style=""color: #ef4444; margin: 0 0 10px 0;"">{detail.DocumentName}</h3>
                        <p style=""margin: 5px 0;""><strong>Reason:</strong> {detail.Reason}</p>
                        {(!string.IsNullOrEmpty(detail.Comments) ? $"<p style=\"margin: 5px 0;\"><strong>Additional Comments:</strong> {detail.Comments}</p>" : "")}
                    </div>");
            }

            // Load email template (simplified version for now)
            var template = await LoadEmailTemplateAsync("DocumentDisapproval");
            
            return template
                .Replace("{StudentName}", studentName)
                .Replace("{SubmissionId}", submissionId.ToString())
                .Replace("{DocumentIssuesList}", documentIssuesList.ToString())
                .Replace("{Deadline}", deadline)
                .Replace("{UpdateLink}", updateLink);
        }

        /// <summary>
        /// Generates update link for student to modify their submission
        /// </summary>
        /// <param name="submission">The submission</param>
        /// <returns>Update link URL</returns>
        private string GenerateUpdateLink(StudentInfo submission)
        {
            var baseUrl = _configuration["Application:BaseUrl"] ?? "https://localhost:7045";
            return $"{baseUrl}/KuwaitiStudentInfo/Edit/{submission.Id}";
        }

        /// <summary>
        /// Loads email template from file or returns default template
        /// </summary>
        /// <param name="templateName">The template name</param>
        /// <returns>Email template HTML</returns>
        private async Task<string> LoadEmailTemplateAsync(string templateName)
        {
            // For now, return a simple default template
            // In production, this would load from a file or database
            return @"
<!DOCTYPE html>
<html>
<head>
    <meta charset=""UTF-8"">
    <title>Document Update Required</title>
    <style>
        .container { max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif; }
        .header { background: #1e40af; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9fafb; }
        .document-item { background: white; padding: 15px; margin: 10px 0; border-left: 4px solid #ef4444; }
        .button { display: inline-block; padding: 12px 24px; background: #1e40af; color: white; text-decoration: none; border-radius: 6px; }
        .footer { padding: 20px; text-align: center; color: #6b7280; font-size: 12px; }
    </style>
</head>
<body>
    <div class=""container"">
        <div class=""header"">
            <h1>KTECH Forms System</h1>
            <p>Document Update Required / مطلوب تحديث المستندات</p>
        </div>
        <div class=""content"">
            <h2>Dear {StudentName},</h2>
            <p>Your submission <strong>#{SubmissionId}</strong> requires document updates. Our review team has identified the following documents that need attention:</p>
            {DocumentIssuesList}
            <p><strong>Please update these documents by {Deadline}.</strong></p>
            <p style=""text-align: center; margin: 20px 0;"">
                <a href=""{UpdateLink}"" class=""button"">Update My Submission</a>
            </p>
        </div>
        <div class=""footer"">
            <p>KTECH Admin Team</p>
            <p>This is an automated message. Please do not reply to this email.</p>
        </div>
    </div>
</body>
</html>";
        }
    }

    /// <summary>
    /// Details about a disapproved document
    /// </summary>
    public class DocumentDisapprovalDetail
    {
        public string DocumentType { get; set; } = string.Empty;
        public string DocumentName { get; set; } = string.Empty;
        public string Reason { get; set; } = string.Empty;
        public string? Comments { get; set; }
    }
}
