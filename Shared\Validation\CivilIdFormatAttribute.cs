using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;

namespace Forms.ktech.Shared.Validation
{
    /// <summary>
    /// Validation attribute for Kuwait Civil ID format (12 digits)
    /// </summary>
    public class CivilIdFormatAttribute : ValidationAttribute
    {
        private static readonly Regex CivilIdRegex = new Regex(@"^\d{12}$", RegexOptions.Compiled);

        public CivilIdFormatAttribute()
        {
            ErrorMessage = "Civil ID must be exactly 12 digits.";
        }

        /// <summary>
        /// Validates the Civil ID format
        /// </summary>
        /// <param name="value">The value to validate</param>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation result</returns>
        protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return ValidationResult.Success; // Let [Required] handle null/empty validation
            }

            var civilId = value.ToString()!.Trim();

            if (!CivilIdRegex.IsMatch(civilId))
            {
                return new ValidationResult(ErrorMessage, new[] { validationContext.MemberName });
            }

            // Additional validation: Check if it's a valid Kuwait Civil ID
            if (!IsValidKuwaitCivilId(civilId))
            {
                return new ValidationResult("Invalid Kuwait Civil ID format.", new[] { validationContext.MemberName });
            }

            return ValidationResult.Success;
        }

        /// <summary>
        /// Validates Kuwait Civil ID using checksum algorithm
        /// </summary>
        /// <param name="civilId">The 12-digit civil ID</param>
        /// <returns>True if valid, false otherwise</returns>
        private static bool IsValidKuwaitCivilId(string civilId)
        {
            if (civilId.Length != 12 || !civilId.All(char.IsDigit))
                return false;

            // Kuwait Civil ID validation algorithm
            // The first digit indicates the century: 2 for 1900s, 3 for 2000s
            var firstDigit = int.Parse(civilId[0].ToString());
            if (firstDigit != 2 && firstDigit != 3)
                return false;

            // Extract year, month, day
            var year = int.Parse(civilId.Substring(1, 2));
            var month = int.Parse(civilId.Substring(3, 2));
            var day = int.Parse(civilId.Substring(5, 2));

            // Validate date components
            if (month < 1 || month > 12 || day < 1 || day > 31)
                return false;

            // Calculate full year
            var fullYear = firstDigit == 2 ? 1900 + year : 2000 + year;

            // Validate the date
            try
            {
                var date = new DateTime(fullYear, month, day);
                
                // Check if the date is not in the future
                if (date > DateTime.Now)
                    return false;
                    
                // Check reasonable birth year range (not before 1900, not more than 150 years ago)
                if (fullYear < 1900 || fullYear < DateTime.Now.Year - 150)
                    return false;
            }
            catch (ArgumentOutOfRangeException)
            {
                return false; // Invalid date
            }

            return true;
        }
    }
}
