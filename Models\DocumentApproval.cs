using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.AspNetCore.Identity;

namespace Forms.ktech.Models
{
    /// <summary>
    /// Entity for tracking document-level approval status and history
    /// </summary>
    public class DocumentApproval
    {
        /// <summary>
        /// Primary key for the document approval record
        /// </summary>
        [Key]
        public int Id { get; set; }
        
        /// <summary>
        /// Foreign key reference to the submission
        /// </summary>
        [Required]
        public int SubmissionId { get; set; }
        
        /// <summary>
        /// Type of document being approved (e.g., StudentCivilIdPath, FatherCivilIdPath)
        /// </summary>
        [Required]
        [StringLength(100)]
        public string DocumentType { get; set; } = string.Empty;
        
        /// <summary>
        /// Current approval status of the document
        /// </summary>
        [Required]
        public DocumentApprovalStatus Status { get; set; } = DocumentApprovalStatus.Pending;
        
        /// <summary>
        /// User ID of the admin who approved/disapproved the document
        /// </summary>
        [StringLength(450)]
        public string? ApprovedByUserId { get; set; }
        
        /// <summary>
        /// Date and time when the approval/disapproval was made
        /// </summary>
        public DateTime? ApprovalDate { get; set; }
        
        /// <summary>
        /// Reason for disapproval (required when Status is Disapproved)
        /// </summary>
        [StringLength(1000)]
        public string? DisapprovalReason { get; set; }
        
        /// <summary>
        /// Additional comments from the admin
        /// </summary>
        [StringLength(2000)]
        public string? Comments { get; set; }
        
        /// <summary>
        /// Date and time when this approval record was created
        /// </summary>
        [Required]
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        
        /// <summary>
        /// Date and time when this approval record was last updated
        /// </summary>
        public DateTime? UpdatedDate { get; set; }
        
        // Navigation Properties
        
        /// <summary>
        /// Navigation property to the related submission
        /// </summary>
        [ForeignKey(nameof(SubmissionId))]
        public virtual Features.KuwaitiStudentInfo.Models.StudentInfo Submission { get; set; } = null!;
        
        /// <summary>
        /// Navigation property to the admin user who made the approval decision
        /// </summary>
        [ForeignKey(nameof(ApprovedByUserId))]
        public virtual IdentityUser? ApprovedByUser { get; set; }
        
        /// <summary>
        /// Marks the approval record as updated
        /// </summary>
        public void MarkAsUpdated()
        {
            UpdatedDate = DateTime.UtcNow;
        }
        
        /// <summary>
        /// Gets the display name for the document type in bilingual format
        /// </summary>
        public string GetDocumentDisplayName()
        {
            return DocumentType switch
            {
                "StudentCivilIdPath" => "Student Civil ID / الهوية المدنية للطالب",
                "StudentNationalityCertificatePath" => "Student Nationality Certificate / شهادة جنسية الطالب",
                "StudentBirthCertificatePath" => "Student Birth Certificate / شهادة ميلاد الطالب",
                "FatherCivilIdPath" => "Father Civil ID / الهوية المدنية للأب",
                "FatherNationalityCertificatePath" => "Father Nationality Certificate / شهادة جنسية الأب",
                "FatherDeathCertificatePath" => "Father Death Certificate / شهادة وفاة الأب",
                "MotherCivilIdPath" => "Mother Civil ID / الهوية المدنية للأم",
                "MotherNationalityCertificatePath" => "Mother Nationality Certificate / شهادة جنسية الأم",
                "MotherDeathCertificatePath" => "Mother Death Certificate / شهادة وفاة الأم",
                _ => "Document / مستند"
            };
        }
        
        /// <summary>
        /// Gets the status display text in bilingual format
        /// </summary>
        public string GetStatusDisplayText()
        {
            return Status switch
            {
                DocumentApprovalStatus.Pending => "Pending Review / قيد المراجعة",
                DocumentApprovalStatus.Approved => "Approved / موافق عليه",
                DocumentApprovalStatus.Disapproved => "Disapproved / مرفوض",
                DocumentApprovalStatus.UnderReview => "Under Review / تحت المراجعة",
                _ => "Unknown / غير معروف"
            };
        }
        
        /// <summary>
        /// Gets the CSS class for status badge styling
        /// </summary>
        public string GetStatusBadgeClass()
        {
            return Status switch
            {
                DocumentApprovalStatus.Pending => "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
                DocumentApprovalStatus.Approved => "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
                DocumentApprovalStatus.Disapproved => "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
                DocumentApprovalStatus.UnderReview => "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
                _ => "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"
            };
        }
        
        /// <summary>
        /// Gets the icon class for status display
        /// </summary>
        public string GetStatusIconClass()
        {
            return Status switch
            {
                DocumentApprovalStatus.Pending => "fas fa-clock",
                DocumentApprovalStatus.Approved => "fas fa-check-circle",
                DocumentApprovalStatus.Disapproved => "fas fa-times-circle",
                DocumentApprovalStatus.UnderReview => "fas fa-search",
                _ => "fas fa-question-circle"
            };
        }
    }
    
    /// <summary>
    /// Enumeration for document approval status
    /// </summary>
    public enum DocumentApprovalStatus
    {
        /// <summary>
        /// Document is pending initial review
        /// </summary>
        Pending = 0,
        
        /// <summary>
        /// Document has been approved by an admin
        /// </summary>
        Approved = 1,
        
        /// <summary>
        /// Document has been disapproved and requires updates
        /// </summary>
        Disapproved = 2,
        
        /// <summary>
        /// Document is currently under detailed review
        /// </summary>
        UnderReview = 3
    }

    /// <summary>
    /// Extension methods for DocumentApproval
    /// </summary>
    public static class DocumentApprovalExtensions
    {
        /// <summary>
        /// Gets the display name for a document type
        /// </summary>
        public static string GetDocumentDisplayName(this DocumentApproval approval)
        {
            return approval.DocumentType switch
            {
                "StudentBirthCertificatePath" => "Student Birth Certificate / شهادة ميلاد الطالب",
                "FatherBirthCertificatePath" => "Father Birth Certificate / شهادة ميلاد الأب",
                "MotherBirthCertificatePath" => "Mother Birth Certificate / شهادة ميلاد الأم",
                "FatherDeathCertificatePath" => "Father Death Certificate / شهادة وفاة الأب",
                "MotherDeathCertificatePath" => "Mother Death Certificate / شهادة وفاة الأم",
                "FatherCivilIdPath" => "Father Civil ID / الهوية المدنية للأب",
                "MotherCivilIdPath" => "Mother Civil ID / الهوية المدنية للأم",
                "StudentCivilIdPath" => "Student Civil ID / الهوية المدنية للطالب",
                "MarriageCertificatePath" => "Marriage Certificate / شهادة الزواج",
                "DivorceCertificatePath" => "Divorce Certificate / شهادة الطلاق",
                "GuardianshipDocumentPath" => "Guardianship Document / وثيقة الوصاية",
                "AdditionalDocumentPath" => "Additional Document / مستند إضافي",
                _ => approval.DocumentType
            };
        }
    }
}
