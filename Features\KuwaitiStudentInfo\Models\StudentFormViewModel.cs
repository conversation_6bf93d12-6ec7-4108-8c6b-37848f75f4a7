using System.ComponentModel.DataAnnotations;
using Forms.ktech.Shared.ViewModels;
using Forms.ktech.Shared.Validation;
using Forms.ktech.Services.SIS;
using Forms.ktech.Data;

namespace Forms.ktech.Features.KuwaitiStudentInfo.Models
{
    /// <summary>
    /// ViewModel for the Kuwaiti Student Information form
    /// Implements IFormViewModel, IFileUploadViewModel, and ISisPreFillable for shared functionality
    /// </summary>
    public class StudentFormViewModel : IFormViewModel, IFileUploadViewModel, ISisPreFillable
    {
        #region IFormViewModel Implementation

        /// <summary>
        /// Unique identifier for the form submission
        /// Used for organizing uploaded files and tracking submissions
        /// </summary>
        public Guid SubmissionGuid { get; set; } = Guid.NewGuid();

        #endregion

        #region SIS Pre-fill Properties

        /// <summary>
        /// Indicates whether the form has been pre-filled with SIS data
        /// </summary>
        public bool IsPreFilled { get; set; }

        /// <summary>
        /// Gets the SIS Student ID that was used for pre-filling (if any)
        /// </summary>
        public string? PreFilledFromStudentId { get; set; }

        /// <summary>
        /// Gets the timestamp when the form was pre-filled
        /// </summary>
        public DateTime? PreFilledAt { get; set; }

        #endregion

        #region Student Information

        /// <summary>
        /// Full name of the student
        /// </summary>
        [Required(ErrorMessage = "Student name is required.")]
        [StringLength(200, ErrorMessage = "Student name cannot exceed 200 characters.")]
        [Display(Name = "Student Name")]
        public string StudentName { get; set; } = string.Empty;

        /// <summary>
        /// Student's Kuwait Civil ID (12 digits)
        /// </summary>
        [Required(ErrorMessage = "Student Civil ID is required.")]
        [CivilIdFormat]
        [Display(Name = "Student Civil ID")]
        public string StudentCivilId { get; set; } = string.Empty;

        /// <summary>
        /// Student's mobile phone number
        /// </summary>
        [Required(ErrorMessage = "Mobile number is required.")]
        [KuwaitMobileNumber]
        [Display(Name = "Mobile Number")]
        public string StudentMobileNumber { get; set; } = string.Empty;

        /// <summary>
        /// Indicates if the student is a Kuwaiti citizen
        /// </summary>
        [Display(Name = "Is Student Kuwaiti?")]
        public bool StudentIsKuwaiti { get; set; }

        #endregion

        #region Father Information

        /// <summary>
        /// Indicates if the father is a Kuwaiti citizen
        /// </summary>
        [Display(Name = "Is Father Kuwaiti?")]
        public bool FatherIsKuwaiti { get; set; }

        /// <summary>
        /// Indicates if the father is deceased
        /// </summary>
        [Display(Name = "Is Father Deceased?")]
        public bool FatherIsDeceased { get; set; }

        #endregion

        #region Mother Information

        /// <summary>
        /// Indicates if the mother is a Kuwaiti citizen
        /// </summary>
        [Display(Name = "Is Mother Kuwaiti?")]
        public bool MotherIsKuwaiti { get; set; }

        /// <summary>
        /// Indicates if the mother is deceased
        /// </summary>
        [Display(Name = "Is Mother Deceased?")]
        public bool MotherIsDeceased { get; set; }

        #endregion

        #region File Upload Properties

        /// <summary>
        /// Student's civil ID document file (required based on eligibility case)
        /// </summary>
        [FileSize(5)]
        [AllowedFileExtensions(".pdf", ".jpg", ".jpeg", ".png")]
        [Display(Name = "Student Civil ID Document")]
        public IFormFile? StudentCivilIdFile { get; set; }

        /// <summary>
        /// Student's nationality certificate (required based on eligibility case)
        /// </summary>
        [FileSize(5)]
        [AllowedFileExtensions(".pdf", ".jpg", ".jpeg", ".png")]
        [Display(Name = "Student Nationality Certificate")]
        public IFormFile? StudentNationalityCertificateFile { get; set; }

        /// <summary>
        /// Student's birth certificate (required in all eligible cases)
        /// </summary>
        [FileSize(5)]
        [AllowedFileExtensions(".pdf", ".jpg", ".jpeg", ".png")]
        [Display(Name = "Student Birth Certificate")]
        public IFormFile? StudentBirthCertificateFile { get; set; }

        /// <summary>
        /// Father's civil ID document (required based on eligibility case)
        /// </summary>
        [FileSize(5)]
        [AllowedFileExtensions(".pdf", ".jpg", ".jpeg", ".png")]
        [Display(Name = "Father's Civil ID Document")]
        public IFormFile? FatherCivilIdFile { get; set; }

        /// <summary>
        /// Father's nationality certificate (required based on eligibility case)
        /// </summary>
        [FileSize(5)]
        [AllowedFileExtensions(".pdf", ".jpg", ".jpeg", ".png")]
        [Display(Name = "Father's Nationality Certificate")]
        public IFormFile? FatherNationalityCertificateFile { get; set; }

        /// <summary>
        /// Mother's civil ID document (required based on eligibility case)
        /// </summary>
        [FileSize(5)]
        [AllowedFileExtensions(".pdf", ".jpg", ".jpeg", ".png")]
        [Display(Name = "Mother's Civil ID Document")]
        public IFormFile? MotherCivilIdFile { get; set; }

        /// <summary>
        /// Mother's nationality certificate (required based on eligibility case)
        /// </summary>
        [FileSize(5)]
        [AllowedFileExtensions(".pdf", ".jpg", ".jpeg", ".png")]
        [Display(Name = "Mother's Nationality Certificate")]
        public IFormFile? MotherNationalityCertificateFile { get; set; }

        /// <summary>
        /// Father's death certificate (required if father is Kuwaiti and deceased)
        /// </summary>
        [FileSize(5)]
        [AllowedFileExtensions(".pdf", ".jpg", ".jpeg", ".png")]
        [Display(Name = "Father's Death Certificate")]
        public IFormFile? FatherDeathCertificateFile { get; set; }

        /// <summary>
        /// Mother's death certificate (required if mother is Kuwaiti and deceased)
        /// </summary>
        [FileSize(5)]
        [AllowedFileExtensions(".pdf", ".jpg", ".jpeg", ".png")]
        [Display(Name = "Mother's Death Certificate")]
        public IFormFile? MotherDeathCertificateFile { get; set; }

        #endregion

        #region IFormViewModel Implementation

        /// <summary>
        /// Validates the form's business rules for eligibility
        /// </summary>
        /// <returns>True if the form passes all business rules, false otherwise</returns>
        public bool IsEligible()
        {
            // At least one person (student, father, or mother) must be Kuwaiti
            return StudentIsKuwaiti || FatherIsKuwaiti || MotherIsKuwaiti;
        }

        /// <summary>
        /// Gets the reason why the form is ineligible
        /// </summary>
        /// <returns>Ineligibility reason or null if eligible</returns>
        public string? GetIneligibilityReason()
        {
            if (!IsEligible())
            {
                return "At least one person (student, father, or mother) must be a Kuwaiti citizen to be eligible for this program.";
            }
            return null;
        }

        #endregion

        #region Business Logic Methods

        /// <summary>
        /// Determines the eligibility case based on citizenship status
        /// Case A: All three are Kuwaiti (Father ✅, Mother ✅, Student ✅)
        /// Case B: Father & Student Kuwaiti (Father ✅, Student ✅, Mother ❌)
        /// Case C: Mother Kuwaiti only (Father ❌, Mother ✅, Student ❌)
        /// Case D: None are Kuwaiti (Father ❌, Mother ❌, Student ❌) - Ineligible
        /// </summary>
        /// <returns>The eligibility case (A, B, C, or D)</returns>
        public string GetEligibilityCase()
        {
            // Case A: All three are Kuwaiti
            if (StudentIsKuwaiti && FatherIsKuwaiti && MotherIsKuwaiti)
                return "A";
            // Case B: Father & Student Kuwaiti (Mother is not)
            else if (StudentIsKuwaiti && FatherIsKuwaiti && !MotherIsKuwaiti)
                return "B";
            // Case C: Mother Kuwaiti only (Father and Student are not)
            else if (!StudentIsKuwaiti && !FatherIsKuwaiti && MotherIsKuwaiti)
                return "C";
            // Case D: None are Kuwaiti or any other combination - Ineligible
            else
                return "D";
        }

        #endregion

        #region IFileUploadViewModel Implementation

        /// <summary>
        /// Gets all file upload properties from the ViewModel
        /// </summary>
        /// <returns>Dictionary of property names and their IFormFile values</returns>
        public Dictionary<string, IFormFile?> GetFileUploads()
        {
            return new Dictionary<string, IFormFile?>
            {
                { nameof(StudentCivilIdFile), StudentCivilIdFile },
                { nameof(StudentNationalityCertificateFile), StudentNationalityCertificateFile },
                { nameof(StudentBirthCertificateFile), StudentBirthCertificateFile },
                { nameof(FatherCivilIdFile), FatherCivilIdFile },
                { nameof(FatherNationalityCertificateFile), FatherNationalityCertificateFile },
                { nameof(FatherDeathCertificateFile), FatherDeathCertificateFile },
                { nameof(MotherCivilIdFile), MotherCivilIdFile },
                { nameof(MotherNationalityCertificateFile), MotherNationalityCertificateFile },
                { nameof(MotherDeathCertificateFile), MotherDeathCertificateFile }
            };
        }

        /// <summary>
        /// Gets the file upload properties that are required based on current form state
        /// Requirements vary by eligibility case and deceased parent status:
        /// Case A (All Kuwaiti): 7 documents base - Student Civil ID + Nationality Cert + Birth Cert + Parent documents (conditional on deceased status)
        /// Case B (Father & Student Kuwaiti): 5 documents base - Student Civil ID + Nationality Cert + Birth Cert + Father documents (conditional on deceased status)
        /// Case C (Mother Kuwaiti only): 4 documents base - Student Civil ID + Birth Cert + Mother documents (conditional on deceased status)
        /// Case D (None Kuwaiti): 0 documents - Submission blocked
        /// Deceased parents require only death certificate (replaces Civil ID + Nationality Certificate)
        /// </summary>
        /// <returns>List of property names that should have files uploaded</returns>
        public List<string> GetRequiredFileUploads()
        {
            var requiredFiles = new List<string>();
            var eligibilityCase = GetEligibilityCase();

            switch (eligibilityCase)
            {
                case "A": // All Kuwaiti
                    // Student documents (always required)
                    requiredFiles.AddRange(new[]
                    {
                        nameof(StudentCivilIdFile),
                        nameof(StudentNationalityCertificateFile),
                        nameof(StudentBirthCertificateFile)
                    });

                    // Father documents (conditional on deceased status)
                    if (FatherIsDeceased)
                        requiredFiles.Add(nameof(FatherDeathCertificateFile));
                    else
                        requiredFiles.AddRange(new[] { nameof(FatherCivilIdFile), nameof(FatherNationalityCertificateFile) });

                    // Mother documents (conditional on deceased status)
                    if (MotherIsDeceased)
                        requiredFiles.Add(nameof(MotherDeathCertificateFile));
                    else
                        requiredFiles.AddRange(new[] { nameof(MotherCivilIdFile), nameof(MotherNationalityCertificateFile) });
                    break;

                case "B": // Father & Student Kuwaiti
                    // Student documents (always required)
                    requiredFiles.AddRange(new[]
                    {
                        nameof(StudentCivilIdFile),
                        nameof(StudentNationalityCertificateFile),
                        nameof(StudentBirthCertificateFile)
                    });

                    // Father documents (conditional on deceased status)
                    if (FatherIsDeceased)
                        requiredFiles.Add(nameof(FatherDeathCertificateFile));
                    else
                        requiredFiles.AddRange(new[] { nameof(FatherCivilIdFile), nameof(FatherNationalityCertificateFile) });
                    break;

                case "C": // Mother Kuwaiti only
                    // Student documents (Civil ID + Birth Cert only, no nationality cert needed)
                    requiredFiles.AddRange(new[]
                    {
                        nameof(StudentCivilIdFile),
                        nameof(StudentBirthCertificateFile)
                    });

                    // Mother documents (conditional on deceased status)
                    if (MotherIsDeceased)
                        requiredFiles.Add(nameof(MotherDeathCertificateFile));
                    else
                        requiredFiles.AddRange(new[] { nameof(MotherCivilIdFile), nameof(MotherNationalityCertificateFile) });
                    break;

                case "D": // None Kuwaiti - No documents required (submission blocked)
                    break;

                default:
                    // Fallback - should not happen with correct case logic
                    break;
            }

            return requiredFiles;
        }

        /// <summary>
        /// Validates that all required files are uploaded
        /// </summary>
        /// <returns>True if all required files are present, false otherwise</returns>
        public bool HasAllRequiredFiles()
        {
            var requiredFiles = GetRequiredFileUploads();
            var fileUploads = GetFileUploads();

            return requiredFiles.All(fileName =>
                fileUploads.ContainsKey(fileName) &&
                fileUploads[fileName] != null &&
                fileUploads[fileName]!.Length > 0);
        }

        /// <summary>
        /// Gets a list of missing required files
        /// </summary>
        /// <returns>List of property names for missing required files</returns>
        public List<string> GetMissingRequiredFiles()
        {
            var requiredFiles = GetRequiredFileUploads();
            var fileUploads = GetFileUploads();

            return requiredFiles.Where(fileName =>
                !fileUploads.ContainsKey(fileName) ||
                fileUploads[fileName] == null ||
                fileUploads[fileName]!.Length == 0).ToList();
        }

        #endregion

        #region ISisPreFillable Implementation

        /// <summary>
        /// Maps SIS student data to the ViewModel properties
        /// </summary>
        /// <param name="student">SIS student data</param>
        public void MapFromSisStudent(SisStudent student)
        {
            if (student == null) return;

            // Map student name (read-only from SIS)
            StudentName = student.FullNameEN ?? string.Empty;

            // Map civil ID (read-only from SIS)
            StudentCivilId = student.NationalID ?? string.Empty;

            // Map nationality status based on SIS nationality
            // Note: This is a business logic decision - Kuwait nationality = Kuwaiti citizen
            StudentIsKuwaiti = string.Equals(student.Nationality, "KUWAIT", StringComparison.OrdinalIgnoreCase) ||
                              string.Equals(student.Nationality, "Kuwaiti", StringComparison.OrdinalIgnoreCase);

            // Set pre-fill metadata
            IsPreFilled = true;
            PreFilledFromStudentId = student.StudentID;
            PreFilledAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Gets the list of field names that should be read-only when pre-filled from SIS
        /// </summary>
        /// <returns>List of read-only field names</returns>
        public IEnumerable<string> GetReadOnlyFields()
        {
            if (!IsPreFilled)
                return Enumerable.Empty<string>();

            return new[]
            {
                nameof(StudentName),
                nameof(StudentCivilId)
            };
        }

        /// <summary>
        /// Gets the list of field names that were pre-filled from SIS data
        /// </summary>
        /// <returns>List of pre-filled field names</returns>
        public IEnumerable<string> GetPreFilledFields()
        {
            if (!IsPreFilled)
                return Enumerable.Empty<string>();

            return new[]
            {
                nameof(StudentName),
                nameof(StudentCivilId),
                nameof(StudentIsKuwaiti)
            };
        }

        /// <summary>
        /// Gets a user-friendly description of which fields were pre-filled
        /// </summary>
        /// <returns>Description of pre-filled fields</returns>
        public string GetPreFillDescription()
        {
            if (!IsPreFilled)
                return "Form was not pre-filled from SIS data";

            var preFilledFields = GetPreFilledFields().ToList();
            if (!preFilledFields.Any())
                return "No fields were pre-filled";

            var fieldDescriptions = preFilledFields.Select(field => field switch
            {
                nameof(StudentName) => "Student Name",
                nameof(StudentCivilId) => "Civil ID",
                nameof(StudentIsKuwaiti) => "Nationality Status",
                _ => field
            });

            return $"Pre-filled from SIS: {string.Join(", ", fieldDescriptions)}";
        }

        /// <summary>
        /// Checks if a specific field is read-only due to SIS pre-filling
        /// </summary>
        /// <param name="fieldName">Name of the field to check</param>
        /// <returns>True if the field is read-only, false otherwise</returns>
        public bool IsFieldReadOnly(string fieldName)
        {
            return GetReadOnlyFields().Contains(fieldName);
        }

        /// <summary>
        /// Checks if a specific field was pre-filled from SIS
        /// </summary>
        /// <param name="fieldName">Name of the field to check</param>
        /// <returns>True if the field was pre-filled, false otherwise</returns>
        public bool IsFieldPreFilled(string fieldName)
        {
            return GetPreFilledFields().Contains(fieldName);
        }

        #endregion
    }
}
