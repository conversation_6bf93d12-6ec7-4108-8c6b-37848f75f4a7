using Forms.ktech.Data;
using Forms.ktech.Features.KuwaitiStudentInfo.Models;
using Forms.ktech.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;

namespace Forms.ktech.Services
{
    /// <summary>
    /// Service for handling submission notification operations
    /// </summary>
    public class SubmissionNotificationService : ISubmissionNotificationService
    {
        private readonly FormsKTechContext _context;
        private readonly ILogger<SubmissionNotificationService> _logger;
        private readonly IEmailNotificationService _emailNotificationService;
        private readonly IConfiguration _configuration;

        public SubmissionNotificationService(
            FormsKTechContext context,
            ILogger<SubmissionNotificationService> logger,
            IEmailNotificationService emailNotificationService,
            IConfiguration configuration)
        {
            _context = context;
            _logger = logger;
            _emailNotificationService = emailNotificationService;
            _configuration = configuration;
        }

        /// <summary>
        /// Sends approval notification to student
        /// </summary>
        public async Task<NotificationResult> SendApprovalNotificationAsync(int submissionId)
        {
            try
            {
                var submission = await _context.StudentInfos.FindAsync(submissionId);
                if (submission == null)
                {
                    return NotificationResult.CreateFailure(submissionId, SubmissionNotificationType.Approved, "Submission not found");
                }

                var emailContent = await GenerateApprovalEmailAsync(submission);

                // Create EmailNotification object for the existing email service
                var emailNotification = new EmailNotification
                {
                    SubmissionId = submissionId,
                    RecipientEmail = emailContent.ToEmail,
                    Subject = emailContent.Subject,
                    Body = emailContent.HtmlBody,
                    Status = EmailStatus.Pending,
                    CreatedDate = DateTime.UtcNow,
                    EmailType = "SubmissionApproved"
                };

                // Send email using existing email notification service
                var success = await _emailNotificationService.SendEmailAsync(emailNotification);

                if (success)
                {
                    _logger.LogInformation("Approval notification sent successfully for submission {SubmissionId}", submissionId);
                    return NotificationResult.CreateSuccess(submissionId, SubmissionNotificationType.Approved, "Approval notification sent successfully");
                }
                else
                {
                    _logger.LogError("Failed to send approval notification for submission {SubmissionId}", submissionId);
                    return NotificationResult.CreateFailure(submissionId, SubmissionNotificationType.Approved, "Failed to send email");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending approval notification for submission {SubmissionId}", submissionId);
                return NotificationResult.CreateFailure(submissionId, SubmissionNotificationType.Approved, "An error occurred while sending the notification");
            }
        }

        /// <summary>
        /// Sends rejection notification to student
        /// </summary>
        public async Task<NotificationResult> SendRejectionNotificationAsync(int submissionId, string reason)
        {
            try
            {
                var submission = await _context.StudentInfos.FindAsync(submissionId);
                if (submission == null)
                {
                    return NotificationResult.CreateFailure(submissionId, SubmissionNotificationType.Rejected, "Submission not found");
                }

                var emailContent = await GenerateRejectionEmailAsync(submission, reason);

                // Create EmailNotification object for the existing email service
                var emailNotification = new EmailNotification
                {
                    SubmissionId = submissionId,
                    RecipientEmail = emailContent.ToEmail,
                    Subject = emailContent.Subject,
                    Body = emailContent.HtmlBody,
                    Status = EmailStatus.Pending,
                    CreatedDate = DateTime.UtcNow,
                    EmailType = "SubmissionRejected"
                };

                // Send email using existing email notification service
                var success = await _emailNotificationService.SendEmailAsync(emailNotification);

                if (success)
                {
                    _logger.LogInformation("Rejection notification sent successfully for submission {SubmissionId}", submissionId);
                    return NotificationResult.CreateSuccess(submissionId, SubmissionNotificationType.Rejected, "Rejection notification sent successfully");
                }
                else
                {
                    _logger.LogError("Failed to send rejection notification for submission {SubmissionId}", submissionId);
                    return NotificationResult.CreateFailure(submissionId, SubmissionNotificationType.Rejected, "Failed to send email");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending rejection notification for submission {SubmissionId}", submissionId);
                return NotificationResult.CreateFailure(submissionId, SubmissionNotificationType.Rejected, "An error occurred while sending the notification");
            }
        }

        /// <summary>
        /// Sends flagged notification (internal use - may not send to students)
        /// </summary>
        public async Task<NotificationResult> SendFlaggedNotificationAsync(int submissionId, string? notes = null)
        {
            try
            {
                // For now, flagged notifications are internal only
                // We could implement admin-to-admin notifications here if needed
                _logger.LogInformation("Submission {SubmissionId} flagged for review with notes: {Notes}", submissionId, notes ?? "No notes");
                return NotificationResult.CreateSuccess(submissionId, SubmissionNotificationType.Flagged, "Submission flagged for internal review");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing flagged notification for submission {SubmissionId}", submissionId);
                return NotificationResult.CreateFailure(submissionId, SubmissionNotificationType.Flagged, "An error occurred while processing the flag notification");
            }
        }

        /// <summary>
        /// Generates approval email content
        /// </summary>
        public async Task<EmailContent> GenerateApprovalEmailAsync(StudentInfo submission)
        {
            var studentName = submission.GetStudentName();
            var subject = "تم قبول طلب التسجيل / Registration Application Approved";
            
            var htmlBody = $@"
<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <title>Registration Approved</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; }}
        .content {{ padding: 30px; }}
        .success-icon {{ font-size: 48px; margin-bottom: 20px; }}
        .footer {{ background-color: #f8fafc; padding: 20px; text-align: center; font-size: 14px; color: #64748b; }}
        .button {{ display: inline-block; background-color: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }}
        .details {{ background-color: #f1f5f9; padding: 20px; border-radius: 6px; margin: 20px 0; }}
    </style>
</head>
<body>
    <div class=""container"">
        <div class=""header"">
            <div class=""success-icon"">✅</div>
            <h1>تم قبول طلب التسجيل</h1>
            <h2>Registration Application Approved</h2>
        </div>
        <div class=""content"">
            <p><strong>عزيزي/عزيزتي {studentName}</strong></p>
            <p><strong>Dear {studentName},</strong></p>
            
            <p>نحن سعداء لإبلاغكم بأنه تم قبول طلب التسجيل الخاص بكم في الكلية التقنية الكويتية.</p>
            <p>We are pleased to inform you that your registration application for Kuwait Technical College has been approved.</p>
            
            <div class=""details"">
                <h3>تفاصيل الطلب / Application Details:</h3>
                <p><strong>رقم الطلب / Application ID:</strong> #{submission.Id}</p>
                <p><strong>تاريخ التقديم / Submission Date:</strong> {submission.CreatedDate:MMMM dd, yyyy}</p>
                <p><strong>تاريخ الموافقة / Approval Date:</strong> {submission.ApprovedDate?.ToString("MMMM dd, yyyy") ?? DateTime.UtcNow.ToString("MMMM dd, yyyy")}</p>
            </div>
            
            <p>سيتم التواصل معكم قريباً بخصوص الخطوات التالية للتسجيل.</p>
            <p>You will be contacted soon regarding the next steps for enrollment.</p>
            
            <p>تهانينا ومرحباً بكم في الكلية التقنية الكويتية!</p>
            <p>Congratulations and welcome to Kuwait Technical College!</p>
        </div>
        <div class=""footer"">
            <p>الكلية التقنية الكويتية / Kuwait Technical College</p>
            <p>البريد الإلكتروني / Email: <EMAIL></p>
            <p>هذه رسالة تلقائية، يرجى عدم الرد عليها / This is an automated message, please do not reply</p>
        </div>
    </div>
</body>
</html>";

            var textBody = $@"
تم قبول طلب التسجيل / Registration Application Approved

عزيزي/عزيزتي {studentName}
Dear {studentName},

نحن سعداء لإبلاغكم بأنه تم قبول طلب التسجيل الخاص بكم في الكلية التقنية الكويتية.
We are pleased to inform you that your registration application for Kuwait Technical College has been approved.

تفاصيل الطلب / Application Details:
رقم الطلب / Application ID: #{submission.Id}
تاريخ التقديم / Submission Date: {submission.CreatedDate:MMMM dd, yyyy}
تاريخ الموافقة / Approval Date: {submission.ApprovedDate?.ToString("MMMM dd, yyyy") ?? DateTime.UtcNow.ToString("MMMM dd, yyyy")}

سيتم التواصل معكم قريباً بخصوص الخطوات التالية للتسجيل.
You will be contacted soon regarding the next steps for enrollment.

تهانينا ومرحباً بكم في الكلية التقنية الكويتية!
Congratulations and welcome to Kuwait Technical College!

الكلية التقنية الكويتية / Kuwait Technical College
البريد الإلكتروني / Email: <EMAIL>
";

            return new EmailContent
            {
                Subject = subject,
                HtmlBody = htmlBody,
                TextBody = textBody,
                ToEmail = submission.GetStudentEmail(),
                ToName = studentName
            };
        }

        /// <summary>
        /// Generates rejection email content
        /// </summary>
        public async Task<EmailContent> GenerateRejectionEmailAsync(StudentInfo submission, string reason)
        {
            var studentName = submission.GetStudentName();
            var subject = "تحديث حالة طلب التسجيل / Registration Application Status Update";

            var htmlBody = $@"
<!DOCTYPE html>
<html dir=""rtl"" lang=""ar"">
<head>
    <meta charset=""UTF-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <title>Registration Status Update</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); color: white; padding: 30px; text-align: center; }}
        .content {{ padding: 30px; }}
        .info-icon {{ font-size: 48px; margin-bottom: 20px; }}
        .footer {{ background-color: #f8fafc; padding: 20px; text-align: center; font-size: 14px; color: #64748b; }}
        .details {{ background-color: #fef2f2; padding: 20px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #dc2626; }}
        .reason {{ background-color: #f1f5f9; padding: 15px; border-radius: 6px; margin: 15px 0; }}
    </style>
</head>
<body>
    <div class=""container"">
        <div class=""header"">
            <div class=""info-icon"">📋</div>
            <h1>تحديث حالة طلب التسجيل</h1>
            <h2>Registration Application Status Update</h2>
        </div>
        <div class=""content"">
            <p><strong>عزيزي/عزيزتي {studentName}</strong></p>
            <p><strong>Dear {studentName},</strong></p>

            <p>نشكركم على اهتمامكم بالتسجيل في الكلية التقنية الكويتية.</p>
            <p>Thank you for your interest in Kuwait Technical College.</p>

            <div class=""details"">
                <h3>تفاصيل الطلب / Application Details:</h3>
                <p><strong>رقم الطلب / Application ID:</strong> #{submission.Id}</p>
                <p><strong>تاريخ التقديم / Submission Date:</strong> {submission.CreatedDate:MMMM dd, yyyy}</p>
                <p><strong>تاريخ المراجعة / Review Date:</strong> {submission.RejectedDate?.ToString("MMMM dd, yyyy") ?? DateTime.UtcNow.ToString("MMMM dd, yyyy")}</p>
            </div>

            <p>بعد مراجعة طلبكم، نأسف لإبلاغكم أنه لا يمكننا المتابعة مع طلب التسجيل الحالي للأسباب التالية:</p>
            <p>After reviewing your application, we regret to inform you that we cannot proceed with your current registration application for the following reason(s):</p>

            <div class=""reason"">
                <p><strong>السبب / Reason:</strong></p>
                <p>{reason}</p>
            </div>

            <p>نشجعكم على مراجعة المتطلبات والتقديم مرة أخرى في المستقبل إذا كان ذلك مناسباً.</p>
            <p>We encourage you to review the requirements and apply again in the future if appropriate.</p>

            <p>إذا كان لديكم أي استفسارات، يرجى التواصل معنا.</p>
            <p>If you have any questions, please feel free to contact us.</p>
        </div>
        <div class=""footer"">
            <p>الكلية التقنية الكويتية / Kuwait Technical College</p>
            <p>البريد الإلكتروني / Email: <EMAIL></p>
            <p>هذه رسالة تلقائية، يرجى عدم الرد عليها / This is an automated message, please do not reply</p>
        </div>
    </div>
</body>
</html>";

            var textBody = $@"
تحديث حالة طلب التسجيل / Registration Application Status Update

عزيزي/عزيزتي {studentName}
Dear {studentName},

نشكركم على اهتمامكم بالتسجيل في الكلية التقنية الكويتية.
Thank you for your interest in Kuwait Technical College.

تفاصيل الطلب / Application Details:
رقم الطلب / Application ID: #{submission.Id}
تاريخ التقديم / Submission Date: {submission.CreatedDate:MMMM dd, yyyy}
تاريخ المراجعة / Review Date: {submission.RejectedDate?.ToString("MMMM dd, yyyy") ?? DateTime.UtcNow.ToString("MMMM dd, yyyy")}

بعد مراجعة طلبكم، نأسف لإبلاغكم أنه لا يمكننا المتابعة مع طلب التسجيل الحالي للأسباب التالية:
After reviewing your application, we regret to inform you that we cannot proceed with your current registration application for the following reason(s):

السبب / Reason:
{reason}

نشجعكم على مراجعة المتطلبات والتقديم مرة أخرى في المستقبل إذا كان ذلك مناسباً.
We encourage you to review the requirements and apply again in the future if appropriate.

إذا كان لديكم أي استفسارات، يرجى التواصل معنا.
If you have any questions, please feel free to contact us.

الكلية التقنية الكويتية / Kuwait Technical College
البريد الإلكتروني / Email: <EMAIL>
";

            return new EmailContent
            {
                Subject = subject,
                HtmlBody = htmlBody,
                TextBody = textBody,
                ToEmail = submission.GetStudentEmail(),
                ToName = studentName
            };
        }

        /// <summary>
        /// Queues submission notification for batch sending (placeholder for future implementation)
        /// </summary>
        public async Task<NotificationResult> QueueSubmissionNotificationAsync(int submissionId, SubmissionNotificationType notificationType, string? reason = null)
        {
            // For now, send immediately. In the future, this could queue for batch processing
            return notificationType switch
            {
                SubmissionNotificationType.Approved => await SendApprovalNotificationAsync(submissionId),
                SubmissionNotificationType.Rejected => await SendRejectionNotificationAsync(submissionId, reason ?? "No reason provided"),
                SubmissionNotificationType.Flagged => await SendFlaggedNotificationAsync(submissionId, reason),
                _ => NotificationResult.CreateFailure(submissionId, notificationType, "Unknown notification type")
            };
        }

        /// <summary>
        /// Gets pending submission notifications (placeholder for future implementation)
        /// </summary>
        public async Task<List<PendingSubmissionNotification>> GetPendingNotificationsAsync()
        {
            // Placeholder - in the future, this would return queued notifications from database
            return new List<PendingSubmissionNotification>();
        }

        /// <summary>
        /// Sends all pending submission notifications (placeholder for future implementation)
        /// </summary>
        public async Task<List<NotificationResult>> SendPendingNotificationsAsync()
        {
            // Placeholder - in the future, this would process queued notifications
            return new List<NotificationResult>();
        }
    }
}
