using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;

namespace Forms.ktech.Hubs
{
    /// <summary>
    /// SignalR hub for real-time SIS synchronization progress updates
    /// Provides live updates to admin dashboard during sync operations
    /// </summary>
    [Authorize]
    public class SisSyncHub : Hub
    {
        private readonly ILogger<SisSyncHub> _logger;

        public SisSyncHub(ILogger<SisSyncHub> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// Called when a client connects to the hub
        /// </summary>
        public override async Task OnConnectedAsync()
        {
            _logger.LogInformation("Client connected to SIS Sync Hub: {ConnectionId}", Context.ConnectionId);
            
            // Add to admin group for sync updates
            await Groups.AddToGroupAsync(Context.ConnectionId, "AdminSyncUpdates");
            await base.OnConnectedAsync();
        }

        /// <summary>
        /// Called when a client disconnects from the hub
        /// </summary>
        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            _logger.LogInformation("Client disconnected from SIS Sync Hub: {ConnectionId}", Context.ConnectionId);
            
            // Remove from admin group
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, "AdminSyncUpdates");
            await base.OnDisconnectedAsync(exception);
        }

        /// <summary>
        /// Allows clients to join the sync updates group
        /// </summary>
        public async Task JoinSyncUpdatesGroup()
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, "AdminSyncUpdates");
            _logger.LogInformation("Client {ConnectionId} joined sync updates group", Context.ConnectionId);
        }

        /// <summary>
        /// Allows clients to leave the sync updates group
        /// </summary>
        public async Task LeaveSyncUpdatesGroup()
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, "AdminSyncUpdates");
            _logger.LogInformation("Client {ConnectionId} left sync updates group", Context.ConnectionId);
        }
    }

    /// <summary>
    /// Extension methods for SisSyncHub to send updates to clients
    /// </summary>
    public static class SisSyncHubExtensions
    {
        /// <summary>
        /// Sends sync progress update to all connected admin clients
        /// </summary>
        public static async Task SendSyncProgressUpdate(this IHubContext<SisSyncHub> hubContext, 
            Guid syncId, int progressPercentage, string currentStep, int recordsProcessed)
        {
            await hubContext.Clients.Group("AdminSyncUpdates").SendAsync("SyncProgressUpdate", new
            {
                syncId = syncId,
                progress = progressPercentage,
                currentStep = currentStep,
                recordsProcessed = recordsProcessed,
                timestamp = DateTime.UtcNow
            });
        }

        /// <summary>
        /// Sends sync completion notification to all connected admin clients
        /// </summary>
        public static async Task SendSyncCompleted(this IHubContext<SisSyncHub> hubContext, 
            Guid syncId, string status, int recordsProcessed, int recordsAdded, int recordsUpdated, string? errorMessage = null)
        {
            await hubContext.Clients.Group("AdminSyncUpdates").SendAsync("SyncCompleted", new
            {
                syncId = syncId,
                status = status,
                recordsProcessed = recordsProcessed,
                recordsAdded = recordsAdded,
                recordsUpdated = recordsUpdated,
                errorMessage = errorMessage,
                timestamp = DateTime.UtcNow
            });
        }

        /// <summary>
        /// Sends sync started notification to all connected admin clients
        /// </summary>
        public static async Task SendSyncStarted(this IHubContext<SisSyncHub> hubContext, 
            Guid syncId, string syncType, string triggeredBy)
        {
            await hubContext.Clients.Group("AdminSyncUpdates").SendAsync("SyncStarted", new
            {
                syncId = syncId,
                syncType = syncType,
                triggeredBy = triggeredBy,
                timestamp = DateTime.UtcNow
            });
        }

        /// <summary>
        /// Sends API health status update to all connected admin clients
        /// </summary>
        public static async Task SendApiHealthUpdate(this IHubContext<SisSyncHub> hubContext, 
            bool isHealthy, string status, double? responseTimeMs = null)
        {
            await hubContext.Clients.Group("AdminSyncUpdates").SendAsync("ApiHealthUpdate", new
            {
                isHealthy = isHealthy,
                status = status,
                responseTimeMs = responseTimeMs,
                timestamp = DateTime.UtcNow
            });
        }
    }
}
