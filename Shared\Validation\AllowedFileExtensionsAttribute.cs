using System.ComponentModel.DataAnnotations;

namespace Forms.ktech.Shared.Validation
{
    /// <summary>
    /// Validation attribute to check allowed file extensions for IFormFile properties
    /// </summary>
    public class AllowedFileExtensionsAttribute : ValidationAttribute
    {
        private readonly string[] _allowedExtensions;

        /// <summary>
        /// Initializes a new instance of AllowedFileExtensionsAttribute
        /// </summary>
        /// <param name="allowedExtensions">Array of allowed file extensions (e.g., ".pdf", ".jpg", ".png")</param>
        public AllowedFileExtensionsAttribute(params string[] allowedExtensions)
        {
            _allowedExtensions = allowedExtensions.Select(ext => ext.ToLowerInvariant()).ToArray();
            ErrorMessage = $"Only the following file types are allowed: {string.Join(", ", _allowedExtensions)}";
        }

        /// <summary>
        /// Validates the file extension
        /// </summary>
        /// <param name="value">The IFormFile to validate</param>
        /// <param name="validationContext">Validation context</param>
        /// <returns>Validation result</returns>
        protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
        {
            if (value == null)
            {
                return ValidationResult.Success; // Let [Required] handle null validation
            }

            if (value is IFormFile file)
            {
                return ValidateFile(file, validationContext.MemberName);
            }
            else if (value is IEnumerable<IFormFile> files)
            {
                foreach (var f in files)
                {
                    var result = ValidateFile(f, validationContext.MemberName);
                    if (result != ValidationResult.Success)
                    {
                        return result;
                    }
                }
            }

            return ValidationResult.Success;
        }

        private ValidationResult? ValidateFile(IFormFile file, string? memberName)
        {
            if (file.Length == 0)
            {
                return ValidationResult.Success; // Empty files are handled elsewhere
            }

            var extension = Path.GetExtension(file.FileName)?.ToLowerInvariant();
            
            if (string.IsNullOrEmpty(extension) || !_allowedExtensions.Contains(extension))
            {
                return new ValidationResult(ErrorMessage, new[] { memberName });
            }

            return ValidationResult.Success;
        }
    }
}
