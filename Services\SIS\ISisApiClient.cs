using Forms.ktech.Data;

namespace Forms.ktech.Services.SIS
{
    /// <summary>
    /// Interface for SIS (Student Information System) API client
    /// Provides methods for communicating with the external SIS API
    /// </summary>
    public interface ISisApiClient
    {
        #region Student Data Retrieval

        /// <summary>
        /// Fetches all students from the SIS API
        /// </summary>
        /// <param name="cancellationToken">Cancellation token for the operation</param>
        /// <returns>List of students from SIS API</returns>
        Task<IEnumerable<SisStudentApiResponse>> GetAllStudentsAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Fetches students with filtering criteria from the SIS API
        /// </summary>
        /// <param name="filterCriteria">Filter criteria for student search</param>
        /// <param name="cancellationToken">Cancellation token for the operation</param>
        /// <returns>Filtered list of students from SIS API</returns>
        Task<IEnumerable<SisStudentApiResponse>> GetFilteredStudentsAsync(
            SisStudentFilterCriteria filterCriteria, 
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Fetches a specific student by their student ID
        /// </summary>
        /// <param name="studentId">The student ID to search for</param>
        /// <param name="cancellationToken">Cancellation token for the operation</param>
        /// <returns>Student data if found, null otherwise</returns>
        Task<SisStudentApiResponse?> GetStudentByIdAsync(string studentId, CancellationToken cancellationToken = default);

        /// <summary>
        /// Fetches students by email address
        /// </summary>
        /// <param name="email">Email address to search for</param>
        /// <param name="cancellationToken">Cancellation token for the operation</param>
        /// <returns>Student data if found, null otherwise</returns>
        Task<SisStudentApiResponse?> GetStudentByEmailAsync(string email, CancellationToken cancellationToken = default);

        #endregion

        #region Enrolled Users

        /// <summary>
        /// Fetches all enrolled users from the SIS API
        /// </summary>
        /// <param name="cancellationToken">Cancellation token for the operation</param>
        /// <returns>List of enrolled users from SIS API</returns>
        Task<IEnumerable<SisEnrolledUserApiResponse>> GetAllEnrolledUsersAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Fetches enrolled users with filtering criteria from the SIS API
        /// </summary>
        /// <param name="filterCriteria">Filter criteria for enrolled user search</param>
        /// <param name="cancellationToken">Cancellation token for the operation</param>
        /// <returns>Filtered list of enrolled users from SIS API</returns>
        Task<IEnumerable<SisEnrolledUserApiResponse>> GetFilteredEnrolledUsersAsync(
            SisEnrolledUserFilterCriteria filterCriteria, 
            CancellationToken cancellationToken = default);

        #endregion

        #region Health and Connectivity

        /// <summary>
        /// Tests connectivity to the SIS API
        /// </summary>
        /// <param name="cancellationToken">Cancellation token for the operation</param>
        /// <returns>True if API is accessible, false otherwise</returns>
        Task<bool> TestConnectivityAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// Gets the current health status of the SIS API connection
        /// </summary>
        /// <returns>Health status information</returns>
        Task<SisApiHealthStatus> GetHealthStatusAsync();

        #endregion
    }

    #region API Response Models

    /// <summary>
    /// Wrapper response model for SIS API responses
    /// All SIS API endpoints return data in this wrapped format
    /// </summary>
    public class SisApiWrapperResponse<T>
    {
        public bool Status { get; set; }
        public string Message { get; set; } = string.Empty;
        public T Data { get; set; } = default!;
    }

    /// <summary>
    /// Response model for student data from SIS API
    /// Matches the actual SIS API response structure
    /// </summary>
    public class SisStudentApiResponse
    {
        public string StudentID { get; set; } = string.Empty;
        public string FullNameAR { get; set; } = string.Empty;
        public string FullNameEN { get; set; } = string.Empty;
        public string Gender { get; set; } = string.Empty;
        public string Nationality { get; set; } = string.Empty;
        public string NationalID { get; set; } = string.Empty;
        public string BirthDate { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string MobileNo { get; set; } = string.Empty;
        public string EnrollmentStatus { get; set; } = string.Empty;
        public string Major { get; set; } = string.Empty;
        public string Level { get; set; } = string.Empty;
    }

    /// <summary>
    /// Response model for enrolled user data from SIS API
    /// </summary>
    public class SisEnrolledUserApiResponse
    {
        public string UserId { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string FullName { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
        public DateTime EnrollmentDate { get; set; }
        public bool IsActive { get; set; }
    }

    #endregion

    #region Filter Criteria Models

    /// <summary>
    /// Filter criteria for student search
    /// </summary>
    public class SisStudentFilterCriteria
    {
        public string? StudentId { get; set; }
        public string? Email { get; set; }
        public string? NationalId { get; set; }
        public string? EnrollmentStatus { get; set; }
        public string? Major { get; set; }
        public string? Level { get; set; }
        public DateTime? EnrollmentDateFrom { get; set; }
        public DateTime? EnrollmentDateTo { get; set; }
        public int? PageSize { get; set; }
        public int? PageNumber { get; set; }
    }

    /// <summary>
    /// Filter criteria for enrolled user search
    /// </summary>
    public class SisEnrolledUserFilterCriteria
    {
        public string? Email { get; set; }
        public string? Role { get; set; }
        public bool? IsActive { get; set; }
        public DateTime? EnrollmentDateFrom { get; set; }
        public DateTime? EnrollmentDateTo { get; set; }
        public int? PageSize { get; set; }
        public int? PageNumber { get; set; }
    }

    #endregion

    #region Health Status Models

    /// <summary>
    /// Health status information for SIS API
    /// </summary>
    public class SisApiHealthStatus
    {
        public bool IsHealthy { get; set; }
        public string Status { get; set; } = string.Empty;
        public TimeSpan ResponseTime { get; set; }
        public DateTime LastChecked { get; set; }
        public string? ErrorMessage { get; set; }
        public Dictionary<string, object> AdditionalInfo { get; set; } = new();
    }

    #endregion
}
