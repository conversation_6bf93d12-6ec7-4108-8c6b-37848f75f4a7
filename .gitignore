# =============================================================================
# .NET / ASP.NET Core Project .gitignore
# =============================================================================

# =============================================================================
# Build Results and Artifacts
# =============================================================================

# Build directories
bin/
obj/
out/

# Build artifacts
*.dll
*.exe
*.pdb
*.cache
*.lib
*.exp
*.ilk
*.map
*.pch
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
*.tmp
*.tmp_proj
*.log
*.vspscc
*.vssscc
.builds
*.pidb
*.svclog
*.scc

# =============================================================================
# .NET Core / ASP.NET Core Specific
# =============================================================================

# ASP.NET Scaffolding
ScaffoldingReadMe.txt

# .NET Core
project.lock.json
project.fragment.lock.json
artifacts/

# ASP.NET Core
**/Properties/launchSettings.json

# Publish profiles and publish output
**/Properties/PublishProfiles/
PublishScripts/
*.Publish.xml
*.pubxml
*.pubxml.user
*.publishproj

# Microsoft Azure Web App publish settings
*.PublishSettings

# =============================================================================
# Package Management
# =============================================================================

# NuGet
*.nupkg
*.snupkg
**/packages/*
!**/packages/build/
*.nuget.props
*.nuget.targets
.nuget/

# Package restore
**/[Pp]ackages/*
!**/[Pp]ackages/build/

# NPM (if used for frontend dependencies)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Bower (if used)
bower_components/

# =============================================================================
# Visual Studio / IDE Files
# =============================================================================

# Visual Studio
.vs/
*.suo
*.user
*.userosscache
*.sln.docstates
*.userprefs

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# JetBrains IDEs
.idea/
*.sln.iml

# MonoDevelop/Xamarin Studio
*.userprefs

# =============================================================================
# Operating System Files
# =============================================================================

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Environment and Configuration Files
# =============================================================================

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Local configuration files
appsettings.local.json
appsettings.*.local.json
# Note: appsettings.Development.json is included for SQLite database configuration
# **/appsettings.Development.json
**/appsettings.Staging.json
**/appsettings.Production.json

# Connection strings and secrets
connectionstrings.json
secrets.json

# User secrets (though ID is in project file, actual secrets should be ignored)
**/secrets.json

# =============================================================================
# Logs and Temporary Files
# =============================================================================

# Log files
*.log
logs/
Logs/

# Temporary files
*.tmp
*.temp
*.swp
*.swo
*~

# =============================================================================
# Database Files
# =============================================================================

# Local database files
*.mdf
*.ldf
*.ndf
*.db
*.sqlite
*.sqlite3

# SQLite temporary and journal files
*.sqlite-shm
*.sqlite-wal
*.sqlite-journal

# =============================================================================
# Entity Framework Migrations
# =============================================================================

# Migration files should be included in version control for team collaboration
# They represent the database schema evolution and are essential for deployments
# Only exclude migration files if you have a specific deployment strategy that doesn't use them

# Uncomment the following lines if you want to exclude migration files:
# Migrations/
# !Migrations/.gitkeep

# Note: Best practice is to INCLUDE migration files in version control because:
# 1. They ensure consistent database schema across environments
# 2. They enable automatic database updates during deployment
# 3. They provide a history of database changes
# 4. They are essential for team collaboration and CI/CD pipelines

# =============================================================================
# Security and Sensitive Files
# =============================================================================

# Certificates
*.pfx
*.p12
*.cer
*.crt
*.pem
*.key

# API keys and secrets
apikeys.json
*.secrets

# =============================================================================
# Testing and Coverage
# =============================================================================

# Test results
TestResults/
[Tt]est[Rr]esult*/
*.trx
*.coverage
*.coveragexml
coverage/
*.lcov

# =============================================================================
# Documentation and Reports
# =============================================================================

# DocFX
_site/

# BenchmarkDotNet
BenchmarkDotNet.Artifacts/

# =============================================================================
# Miscellaneous
# =============================================================================

# Backup files
*.bak
*.backup
*.orig

# Compressed files (if not part of the project)
*.zip
*.rar
*.7z
*.tar
*.gz

# Office temporary files
~$*

# Windows Installer files
*.msi
*.msm
*.msp

# Windows shortcuts
*.lnk

/publish