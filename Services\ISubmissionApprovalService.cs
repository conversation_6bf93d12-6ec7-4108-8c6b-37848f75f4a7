using Forms.ktech.Features.KuwaitiStudentInfo.Models;
using Forms.ktech.ViewModels.Admin;

namespace Forms.ktech.Services
{
    /// <summary>
    /// Interface for submission approval service operations
    /// </summary>
    public interface ISubmissionApprovalService
    {
        /// <summary>
        /// Approves a submission
        /// </summary>
        /// <param name="submissionId">The submission ID to approve</param>
        /// <param name="approvedByUserId">The admin user ID who approved the submission</param>
        /// <returns>Result indicating success or failure</returns>
        Task<SubmissionApprovalResult> ApproveSubmissionAsync(int submissionId, string? approvedByUserId = null);

        /// <summary>
        /// Rejects a submission
        /// </summary>
        /// <param name="submissionId">The submission ID to reject</param>
        /// <param name="reason">The reason for rejection</param>
        /// <param name="rejectedByUserId">The admin user ID who rejected the submission</param>
        /// <returns>Result indicating success or failure</returns>
        Task<SubmissionApprovalResult> RejectSubmissionAsync(int submissionId, string reason, string? rejectedByUserId = null);

        /// <summary>
        /// Flags a submission for review
        /// </summary>
        /// <param name="submissionId">The submission ID to flag</param>
        /// <param name="notes">Optional notes for the flag</param>
        /// <param name="flaggedByUserId">The admin user ID who flagged the submission</param>
        /// <returns>Result indicating success or failure</returns>
        Task<SubmissionApprovalResult> FlagSubmissionAsync(int submissionId, string? notes = null, string? flaggedByUserId = null);

        /// <summary>
        /// Gets submission statistics by status
        /// </summary>
        /// <returns>Statistics for each submission status</returns>
        Task<SubmissionStatusStatistics> GetSubmissionStatisticsAsync();

        /// <summary>
        /// Validates if a submission can be approved
        /// </summary>
        /// <param name="submissionId">The submission ID to validate</param>
        /// <returns>Validation result with any issues</returns>
        Task<SubmissionValidationResult> ValidateSubmissionForApprovalAsync(int submissionId);

        /// <summary>
        /// Gets submission history for audit purposes
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <returns>List of status changes</returns>
        Task<List<SubmissionStatusHistory>> GetSubmissionHistoryAsync(int submissionId);

        /// <summary>
        /// Bulk approve multiple submissions
        /// </summary>
        /// <param name="submissionIds">List of submission IDs to approve</param>
        /// <param name="approvedByUserId">The admin user ID who approved the submissions</param>
        /// <returns>Results for each submission</returns>
        Task<List<SubmissionApprovalResult>> BulkApproveSubmissionsAsync(List<int> submissionIds, string? approvedByUserId = null);

        /// <summary>
        /// Bulk reject multiple submissions
        /// </summary>
        /// <param name="submissionIds">List of submission IDs to reject</param>
        /// <param name="reason">The reason for rejection</param>
        /// <param name="rejectedByUserId">The admin user ID who rejected the submissions</param>
        /// <returns>Results for each submission</returns>
        Task<List<SubmissionApprovalResult>> BulkRejectSubmissionsAsync(List<int> submissionIds, string reason, string? rejectedByUserId = null);
    }

    /// <summary>
    /// Result of a submission approval operation
    /// </summary>
    public class SubmissionApprovalResult
    {
        /// <summary>
        /// Whether the operation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Error message if the operation failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// The submission ID that was processed
        /// </summary>
        public int SubmissionId { get; set; }

        /// <summary>
        /// The previous status before the operation
        /// </summary>
        public SubmissionStatus? PreviousStatus { get; set; }

        /// <summary>
        /// The new status after the operation
        /// </summary>
        public SubmissionStatus? NewStatus { get; set; }

        /// <summary>
        /// Additional details about the operation
        /// </summary>
        public string? Details { get; set; }

        /// <summary>
        /// Creates a successful result
        /// </summary>
        public static SubmissionApprovalResult CreateSuccess(int submissionId, SubmissionStatus? previousStatus, SubmissionStatus newStatus, string? details = null)
        {
            return new SubmissionApprovalResult
            {
                Success = true,
                SubmissionId = submissionId,
                PreviousStatus = previousStatus,
                NewStatus = newStatus,
                Details = details
            };
        }

        /// <summary>
        /// Creates a failure result
        /// </summary>
        public static SubmissionApprovalResult CreateFailure(int submissionId, string errorMessage)
        {
            return new SubmissionApprovalResult
            {
                Success = false,
                SubmissionId = submissionId,
                ErrorMessage = errorMessage
            };
        }
    }

    /// <summary>
    /// Statistics for submission statuses
    /// </summary>
    public class SubmissionStatusStatistics
    {
        /// <summary>
        /// Number of pending submissions
        /// </summary>
        public int PendingCount { get; set; }

        /// <summary>
        /// Number of approved submissions
        /// </summary>
        public int ApprovedCount { get; set; }

        /// <summary>
        /// Number of rejected submissions
        /// </summary>
        public int RejectedCount { get; set; }

        /// <summary>
        /// Number of flagged submissions
        /// </summary>
        public int FlaggedCount { get; set; }

        /// <summary>
        /// Total number of submissions
        /// </summary>
        public int TotalCount => PendingCount + ApprovedCount + RejectedCount + FlaggedCount;
    }

    /// <summary>
    /// Result of submission validation
    /// </summary>
    public class SubmissionValidationResult
    {
        /// <summary>
        /// Whether the submission is valid for approval
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// List of validation issues
        /// </summary>
        public List<string> Issues { get; set; } = new List<string>();

        /// <summary>
        /// List of warnings (non-blocking)
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();
    }

    /// <summary>
    /// Submission status history entry
    /// </summary>
    public class SubmissionStatusHistory
    {
        /// <summary>
        /// When the status change occurred
        /// </summary>
        public DateTime ChangedDate { get; set; }

        /// <summary>
        /// Previous status
        /// </summary>
        public SubmissionStatus? PreviousStatus { get; set; }

        /// <summary>
        /// New status
        /// </summary>
        public SubmissionStatus NewStatus { get; set; }

        /// <summary>
        /// Who made the change
        /// </summary>
        public string? ChangedByUserId { get; set; }

        /// <summary>
        /// Reason or notes for the change
        /// </summary>
        public string? Reason { get; set; }

        /// <summary>
        /// Additional details
        /// </summary>
        public string? Details { get; set; }
    }
}
