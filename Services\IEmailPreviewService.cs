using Forms.ktech.Models;
using Forms.ktech.ViewModels.Admin;

namespace Forms.ktech.Services
{
    /// <summary>
    /// Interface for email preview and batch notification service operations
    /// </summary>
    public interface IEmailPreviewService
    {
        /// <summary>
        /// Gets all submissions with pending email notifications
        /// </summary>
        /// <returns>List of submissions with disapproved documents pending email</returns>
        Task<List<EmailPreviewSubmission>> GetSubmissionsWithPendingEmailsAsync();

        /// <summary>
        /// Generates email preview for a specific submission
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <returns>Email preview data</returns>
        Task<EmailPreviewData> GenerateEmailPreviewAsync(int submissionId);

        /// <summary>
        /// Updates email content for a submission
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="customSubject">Custom email subject</param>
        /// <param name="customBody">Custom email body</param>
        /// <returns>Updated email preview</returns>
        Task<EmailPreviewData> UpdateEmailContentAsync(int submissionId, string customSubject, string customBody);

        /// <summary>
        /// Sends batch email notification for a submission
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="customSubject">Optional custom subject</param>
        /// <param name="customBody">Optional custom body</param>
        /// <returns>Email notification record</returns>
        Task<EmailNotification> SendBatchEmailNotificationAsync(int submissionId, string? customSubject = null, string? customBody = null);

        /// <summary>
        /// Sends bulk email notifications for multiple submissions
        /// </summary>
        /// <param name="submissionIds">Array of submission IDs</param>
        /// <returns>List of email notification records</returns>
        Task<List<EmailNotification>> SendBulkEmailNotificationsAsync(int[] submissionIds);

        /// <summary>
        /// Gets disapproved documents for a submission that are pending email notification
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <returns>List of disapproved documents</returns>
        Task<List<DisapprovedDocumentInfo>> GetDisapprovedDocumentsAsync(int submissionId);

        /// <summary>
        /// Marks documents as email notification sent
        /// </summary>
        /// <param name="submissionId">The submission ID</param>
        /// <param name="documentTypes">Array of document types</param>
        /// <returns>Success status</returns>
        Task<bool> MarkDocumentsAsEmailSentAsync(int submissionId, string[] documentTypes);
    }
}
