@model Forms.ktech.Features.KuwaitiStudentInfo.Models.StudentFormViewModel

@{
    ViewData["Title"] = "Application Summary / ملخص الطلب";
    Layout = "~/Views/Shared/_Layout.cshtml";

    var submissionDate = ViewBag.SubmissionDate as DateTime? ?? DateTime.Now;
    var referenceNumber = ViewBag.ReferenceNumber as string ?? Model.SubmissionGuid.ToString("N")[..8].ToUpper();
    var eligibilityCase = Model.GetEligibilityCase();

    // Simplified document data from controller
    var requiredDocuments = ViewBag.RequiredDocuments as List<string> ?? new List<string>();
    var uploadedDocuments = ViewBag.UploadedDocuments as List<(string Name, bool IsUploaded, string? FilePath, string FileType)> ?? new List<(string, bool, string?, string)>();
}

@section Breadcrumb {
    <li>
        <div class="flex items-center">
            <a href="@Url.Action("Index", "Home")" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">
                <i class="fas fa-home me-1"></i>Home / الرئيسية
            </a>
        </div>
    </li>
    <li aria-current="page">
        <div class="flex items-center">
            <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
            </svg>
            <a href="@Url.Action("CollectInfo", "KuwaitiStudentInfo")" class="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 dark:text-gray-400 dark:hover:text-white">
                <i class="fas fa-graduation-cap me-1"></i>Kuwaiti Student Information / معلومات الطالب
            </a>
        </div>
    </li>
    <li aria-current="page">
        <div class="flex items-center">
            <svg class="w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
            </svg>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2 dark:text-gray-400">
                <i class="fas fa-check-circle me-1"></i>Summary / الملخص
            </span>
        </div>
    </li>
}

@section Styles {
    <link rel="stylesheet" href="~/css/shared-forms.css" asp-append-version="true" />
}

<div class="max-w-8xl mx-auto">
    <!-- Success Header -->
    <div class="bg-white rounded-lg shadow-lg mb-6 dark:bg-gray-800">
        <div class="bg-green-600 text-white p-6 rounded-t-lg text-center">
            <div class="mb-4">
                <i class="fas fa-check-circle text-6xl opacity-75"></i>
            </div>
            <h1 class="text-3xl font-bold mb-2">Application Submitted Successfully / تم إرسال الطلب بنجاح</h1>
            <p class="text-green-100">Your Student Information form has been received and processed / تم استلام ومعالجة نموذج معلومات الطالب </p>
        </div>

        <div class="p-6">
            <!-- Submission Details -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="text-center p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
                    <i class="fas fa-hashtag text-blue-600 text-2xl mb-2 dark:text-blue-400"></i>
                    <h6 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Reference Number / رقم المرجع</h6>
                    <div class="text-lg font-bold text-blue-600 dark:text-blue-400">@referenceNumber</div>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
                    <i class="fas fa-calendar text-blue-600 text-2xl mb-2 dark:text-blue-400"></i>
                    <h6 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Submission Date / تاريخ الإرسال</h6>
                    <div class="text-lg font-bold text-blue-600 dark:text-blue-400">@submissionDate.ToString("MMM dd, yyyy")</div>
                </div>
                <div class="text-center p-4 bg-gray-50 rounded-lg dark:bg-gray-700">
                    <i class="fas fa-check-circle text-green-600 text-2xl mb-2 dark:text-green-400"></i>
                    <h6 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Status / الحالة</h6>
                    <div class="text-lg font-bold text-green-600 dark:text-green-400">Submitted / مُرسل</div>
                </div>
            </div>

            <!-- Status Information -->
            <div class="flex items-start p-4 text-sm text-green-800 border border-green-300 rounded-lg bg-green-50 dark:bg-gray-800 dark:text-green-400 dark:border-green-800" role="alert">
                <svg class="flex-shrink-0 inline w-4 h-4 me-3 mt-0.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5ZM9.5 4a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM12 15H8a1 1 0 0 1 0-2h1v-3H8a1 1 0 0 1 0-2h2a1 1 0 0 1 1 1v4h1a1 1 0 0 1 0 2Z"/>
                </svg>
                <div>
                    <span class="font-medium">Application Status / حالة الطلب</span>
                    <div class="mt-1 text-sm text-green-700 dark:text-green-400">
                        Your application has been successfully submitted and is being processed. / تم إرسال طلبك بنجاح وهو قيد المعالجة.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Submitted Information Summary -->
    <div class="bg-white rounded-lg shadow-lg mb-6 dark:bg-gray-800">
        <div class="bg-blue-600 text-white p-6 rounded-t-lg">
            <div class="flex items-center">
                <i class="fas fa-file-alt text-2xl me-3"></i>
                <h2 class="text-xl font-bold">Submitted Information / المعلومات المُرسلة</h2>
            </div>
        </div>
        <div class="p-6">
            <!-- Student Information -->
            <div class="mb-8">
                <div class="flex items-center mb-6 pb-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg dark:bg-blue-900 me-3">
                        <i class="fas fa-user text-blue-600 dark:text-blue-300"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Student Information / معلومات الطالب</h3>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Student Name / اسم الطالب</label>
                        <div class="text-lg font-semibold text-gray-900 dark:text-white">@Model.StudentName</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Civil ID / نسخة من البطاقة المدنية</label>
                        <div class="text-lg font-semibold text-gray-900 dark:text-white">@Model.StudentCivilId</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Mobile Number / رقم الهاتف المحمول</label>
                        <div class="text-lg font-semibold text-gray-900 dark:text-white">@Model.StudentMobileNumber</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Nationality Status / حالة الجنسية</label>
                        <div>
                            @if (Model.StudentIsKuwaiti)
                            {
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                    <i class="fas fa-flag me-1"></i>Kuwaiti Citizen / مواطن كويتي
                                </span>
                            }
                            else
                            {
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300">
                                    <i class="fas fa-globe me-1"></i>Non-Kuwaiti / غير كويتي
                                </span>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- Father Information -->
            <div class="mb-8">
                <div class="flex items-center mb-6 pb-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-center w-10 h-10 bg-green-100 rounded-lg dark:bg-green-900 me-3">
                        <i class="fas fa-male text-green-600 dark:text-green-300"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Father Information / معلومات الأب</h3>
                </div>
                <div class="grid grid-cols-1 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Nationality Status / حالة الجنسية</label>
                        <div>
                            @if (Model.FatherIsKuwaiti)
                            {
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                    <i class="fas fa-flag me-1"></i>Kuwaiti Citizen / مواطن كويتي
                                </span>
                            }
                            else
                            {
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300">
                                    <i class="fas fa-globe me-1"></i>Non-Kuwaiti / غير كويتي
                                </span>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mother Information -->
            <div class="mb-8">
                <div class="flex items-center mb-6 pb-4 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-center w-10 h-10 bg-purple-100 rounded-lg dark:bg-purple-900 me-3">
                        <i class="fas fa-female text-purple-600 dark:text-purple-300"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Mother Information / معلومات الأم</h3>
                </div>
                <div class="grid grid-cols-1 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">Nationality Status / حالة الجنسية</label>
                        <div>
                            @if (Model.MotherIsKuwaiti)
                            {
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                    <i class="fas fa-flag me-1"></i>Kuwaiti Citizen / مواطنة كويتية
                                </span>
                            }
                            else
                            {
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300">
                                    <i class="fas fa-globe me-1"></i>Non-Kuwaiti / غير كويتية
                                </span>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Document Summary -->
    <div class="bg-white rounded-lg shadow-lg mb-6 dark:bg-gray-800">
        <div class="bg-orange-600 text-white p-6 rounded-t-lg">
            <div class="flex items-center">
                <i class="fas fa-file-upload text-2xl me-3"></i>
                <h2 class="text-xl font-bold">Document Summary / ملخص المستندات</h2>
            </div>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Required Documents -->
                <div>
                    <div class="flex items-center mb-4">
                        <div class="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-lg dark:bg-blue-900 me-3">
                            <i class="fas fa-list-check text-blue-600 dark:text-blue-300 text-sm"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Required Documents / المستندات المطلوبة</h3>
                    </div>
                    <div class="space-y-3">
                        @foreach (var document in requiredDocuments)
                        {
                            <div class="flex items-center p-3 bg-blue-50 border border-blue-200 rounded-lg dark:bg-blue-900/20 dark:border-blue-800">
                                <i class="fas fa-file-alt text-blue-600 dark:text-blue-400 me-3"></i>
                                <span class="text-sm font-medium text-blue-800 dark:text-blue-300">@document</span>
                            </div>
                        }
                    </div>
                </div>

                <!-- Document Status -->
                <div>
                    <div class="flex items-center mb-4">
                        <div class="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-lg dark:bg-blue-900 me-3">
                            <i class="fas fa-file-alt text-blue-600 dark:text-blue-300 text-sm"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Document Status / حالة المستندات</h3>
                    </div>
                    <div class="space-y-3">
                        @if (uploadedDocuments.Any())
                        {
                            @foreach (var document in uploadedDocuments)
                            {
                                @if (document.IsUploaded)
                                {
                                    <!-- Uploaded Document -->
                                    <div class="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg dark:bg-green-900/20 dark:border-green-800">
                                        <div class="flex items-center">
                                            <i class="fas fa-check-circle text-green-600 dark:text-green-400 me-3"></i>
                                            <span class="text-sm font-medium text-green-800 dark:text-green-300">@document.Name</span>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
                                                <i class="fas fa-check me-1"></i>
                                                Uploaded / مُرفوع
                                            </span>
                                            <a href="@Url.Action("DownloadFile", new { id = ViewBag.SubmissionId, fileType = document.FileType })"
                                               class="inline-flex items-center px-3 py-1 text-xs font-medium text-blue-600 bg-blue-100 rounded-lg hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800">
                                                <i class="fas fa-download me-1"></i>
                                                Download / تحميل
                                            </a>
                                        </div>
                                    </div>
                                }
                            }
                        }
                        else
                        {
                            <div class="p-4 bg-gray-50 border border-gray-200 rounded-lg dark:bg-gray-700 dark:border-gray-600">
                                <p class="text-sm text-gray-600 dark:text-gray-400 text-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    No documents found / لم يتم العثور على مستندات
                                </p>
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Summary Status -->
            <div class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg dark:bg-green-900/20 dark:border-green-800">
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-600 dark:text-green-400 text-xl me-3"></i>
                    <div>
                        <h4 class="text-sm font-semibold text-green-800 dark:text-green-300">Document Requirements Complete / اكتملت متطلبات المستندات</h4>
                        <p class="text-xs text-green-700 dark:text-green-400 mt-1">
                            All required documents have been successfully uploaded and verified. / تم رفع والتحقق من جميع المستندات المطلوبة بنجاح.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="bg-white rounded-lg shadow-lg mb-6 dark:bg-gray-800">
        <div class="p-6 text-center">
            <div class="flex flex-col sm:flex-row sm:justify-center gap-4 mb-6">
                <button onclick="window.print()" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-6 py-3 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800">
                    <i class="fas fa-print me-2"></i>
                    Print Summary / طباعة الملخص
                </button>
                <button onclick="downloadPDF()" class="text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-6 py-3 text-center dark:border-blue-500 dark:text-blue-500 dark:hover:text-white dark:hover:bg-blue-500 dark:focus:ring-blue-800">
                    <i class="fas fa-file-pdf me-2"></i>
                    Save as PDF / حفظ كـ PDF
                </button>
                <a href="mailto:<EMAIL>?subject=Application Inquiry - @referenceNumber" class="text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-6 py-3 text-center dark:border-blue-500 dark:text-blue-500 dark:hover:text-white dark:hover:bg-blue-500 dark:focus:ring-blue-800">
                    <i class="fas fa-envelope me-2"></i>
                    Contact Support / اتصل بالدعم
                </a>
            </div>
            @* 
                <div class="text-sm text-gray-500 dark:text-gray-400">
                    <i class="fas fa-info-circle me-1"></i>
                    Reference Number: <strong class="text-gray-900 dark:text-white">@referenceNumber</strong> |
                    Submitted: <strong class="text-gray-900 dark:text-white">@submissionDate.ToString("MMM dd, yyyy 'at' h:mm tt")</strong>
                </div>
             *@
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/shared-forms.js" asp-append-version="true"></script>
    <script src="~/js/kuwaiti-student-info.js" asp-append-version="true"></script>
    <script>
        // Summary page specific functionality
        function downloadPDF() {
            SharedForms.Utils.downloadPDF('@referenceNumber');
        }

        // Log current theme for debugging
        $(document).ready(function() {
            if (typeof ThemeToggle !== 'undefined') {
                console.log('Summary page loaded with theme:', ThemeToggle.getCurrentTheme());
            }
        });
    </script>
}
