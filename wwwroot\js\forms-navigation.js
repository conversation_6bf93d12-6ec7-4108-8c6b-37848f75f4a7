/**
 * Forms.ktech - Navigation and Form Interaction JavaScript
 * Handles navigation guards, form state management, and user experience enhancements
 */

(function() {
    'use strict';

    // Track if form has been modified
    let formModified = false;
    let originalFormData = null;

    /**
     * Initialize navigation functionality when DOM is ready
     */
    document.addEventListener('DOMContentLoaded', function() {
        initializeFormTracking();
        initializeNavigationGuards();
        initializeBackButtons();
        initializeCancelButtons();
        initializeDropdownAccessibility();
    });

    /**
     * Track form modifications to warn users about unsaved changes
     */
    function initializeFormTracking() {
        const forms = document.querySelectorAll('form[data-track-changes="true"]');
        
        forms.forEach(function(form) {
            // Store original form data
            originalFormData = new FormData(form);
            
            // Track input changes
            form.addEventListener('input', function() {
                formModified = true;
            });
            
            // Track file input changes
            form.addEventListener('change', function(e) {
                if (e.target.type === 'file') {
                    formModified = true;
                }
            });
            
            // Reset tracking on successful submit
            form.addEventListener('submit', function() {
                formModified = false;
            });
        });
    }

    /**
     * Initialize navigation guards to prevent accidental navigation away from unsaved forms
     */
    function initializeNavigationGuards() {
        // Warn before page unload if form is modified
        window.addEventListener('beforeunload', function(e) {
            if (formModified) {
                const message = 'You have unsaved changes. Are you sure you want to leave this page?';
                e.preventDefault();
                e.returnValue = message;
                return message;
            }
        });

        // Warn before navigation via links
        document.addEventListener('click', function(e) {
            const link = e.target.closest('a[href]');
            if (link && formModified && !link.hasAttribute('data-ignore-changes')) {
                const href = link.getAttribute('href');
                
                // Don't warn for same-page anchors or javascript: links
                if (href.startsWith('#') || href.startsWith('javascript:')) {
                    return;
                }
                
                if (!confirm('You have unsaved changes. Are you sure you want to leave this page?')) {
                    e.preventDefault();
                }
            }
        });
    }

    /**
     * Initialize back button functionality
     */
    function initializeBackButtons() {
        const backButtons = document.querySelectorAll('[data-action="back"]');
        
        backButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                if (formModified && !confirm('You have unsaved changes. Are you sure you want to go back?')) {
                    return;
                }
                
                // Check if there's a specific back URL
                const backUrl = button.getAttribute('data-back-url');
                if (backUrl) {
                    window.location.href = backUrl;
                } else if (window.history.length > 1) {
                    window.history.back();
                } else {
                    // Fallback to home page
                    window.location.href = '/';
                }
            });
        });
    }

    /**
     * Initialize cancel button functionality
     */
    function initializeCancelButtons() {
        const cancelButtons = document.querySelectorAll('[data-action="cancel"]');
        
        cancelButtons.forEach(function(button) {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                const message = formModified 
                    ? 'Are you sure you want to cancel? All unsaved changes will be lost.'
                    : 'Are you sure you want to cancel?';
                
                if (confirm(message)) {
                    const cancelUrl = button.getAttribute('data-cancel-url') || '/';
                    window.location.href = cancelUrl;
                }
            });
        });
    }

    /**
     * Enhance dropdown accessibility
     */
    function initializeDropdownAccessibility() {
        const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
        
        dropdownToggles.forEach(function(toggle) {
            // Handle keyboard navigation
            toggle.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    toggle.click();
                }
            });
        });

        // Handle dropdown item keyboard navigation
        const dropdownItems = document.querySelectorAll('.dropdown-item');
        dropdownItems.forEach(function(item) {
            item.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    item.click();
                }
            });
        });
    }

    /**
     * Utility function to show loading state
     */
    function showLoadingState(element, text = 'Loading...') {
        if (element) {
            element.disabled = true;
            const originalText = element.textContent;
            element.setAttribute('data-original-text', originalText);
            element.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>${text}`;
        }
    }

    /**
     * Utility function to hide loading state
     */
    function hideLoadingState(element) {
        if (element) {
            element.disabled = false;
            const originalText = element.getAttribute('data-original-text');
            if (originalText) {
                element.textContent = originalText;
                element.removeAttribute('data-original-text');
            }
        }
    }

    /**
     * Utility function to update breadcrumb
     */
    function updateBreadcrumb(items) {
        const breadcrumb = document.querySelector('.breadcrumb');
        if (breadcrumb) {
            breadcrumb.innerHTML = '';
            
            items.forEach(function(item, index) {
                const li = document.createElement('li');
                li.className = 'breadcrumb-item';
                
                if (index === items.length - 1) {
                    li.className += ' active';
                    li.setAttribute('aria-current', 'page');
                    li.textContent = item.text;
                } else {
                    const link = document.createElement('a');
                    link.href = item.url;
                    link.textContent = item.text;
                    li.appendChild(link);
                }
                
                breadcrumb.appendChild(li);
            });
        }
    }

    /**
     * Utility function to highlight active navigation item
     */
    function highlightActiveNavigation() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.navbar-nav .nav-link, .dropdown-item');
        
        navLinks.forEach(function(link) {
            const href = link.getAttribute('href');
            if (href && currentPath.includes(href) && href !== '/') {
                link.classList.add('active');
                
                // If it's a dropdown item, also highlight the parent dropdown
                const dropdown = link.closest('.dropdown');
                if (dropdown) {
                    const dropdownToggle = dropdown.querySelector('.dropdown-toggle');
                    if (dropdownToggle) {
                        dropdownToggle.classList.add('active');
                    }
                }
            }
        });
    }

    // Initialize active navigation highlighting
    document.addEventListener('DOMContentLoaded', highlightActiveNavigation);

    // Expose utility functions globally for use in views
    window.FormsNavigation = {
        showLoadingState: showLoadingState,
        hideLoadingState: hideLoadingState,
        updateBreadcrumb: updateBreadcrumb,
        setFormModified: function(modified) { formModified = modified; }
    };

})();
