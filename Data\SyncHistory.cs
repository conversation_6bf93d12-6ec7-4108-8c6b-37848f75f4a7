using System.ComponentModel.DataAnnotations;

namespace Forms.ktech.Data
{
    /// <summary>
    /// Entity class for tracking SIS data synchronization operations
    /// Inherits from BaseEntity to get common properties like Id, CreatedDate, SubmissionGuid, etc.
    /// Provides comprehensive audit trail for all sync operations
    /// </summary>
    public class SyncHistory : BaseEntity
    {
        #region Sync Operation Identification

        /// <summary>
        /// Unique identifier for this specific sync operation
        /// Used to track and reference individual sync jobs
        /// </summary>
        [Required]
        public Guid SyncId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// Type of synchronization operation performed
        /// </summary>
        [Required]
        public SyncType SyncType { get; set; }

        /// <summary>
        /// Current status of the synchronization operation
        /// </summary>
        [Required]
        public SyncStatus Status { get; set; }

        #endregion

        #region Timing Information

        /// <summary>
        /// UTC timestamp when the sync operation started
        /// </summary>
        [Required]
        public DateTime StartTime { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// UTC timestamp when the sync operation completed (successfully or with error)
        /// Null if sync is still in progress
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// Duration of the sync operation in milliseconds
        /// Calculated from StartTime and EndTime
        /// </summary>
        public long? DurationMs { get; set; }

        #endregion

        #region Sync Statistics

        /// <summary>
        /// Total number of student records processed during this sync
        /// Includes both new and existing records that were evaluated
        /// </summary>
        public int RecordsProcessed { get; set; }

        /// <summary>
        /// Number of new student records added to the database
        /// </summary>
        public int RecordsAdded { get; set; }

        /// <summary>
        /// Number of existing student records updated with new data
        /// </summary>
        public int RecordsUpdated { get; set; }

        /// <summary>
        /// Number of records that were skipped (no changes detected)
        /// </summary>
        public int RecordsSkipped { get; set; }

        /// <summary>
        /// Number of records that failed to process due to errors
        /// </summary>
        public int RecordsErrored { get; set; }

        #endregion

        #region Error and Status Information

        /// <summary>
        /// Error message if the sync operation failed
        /// Contains the primary error that caused the sync to fail
        /// </summary>
        [StringLength(1000)]
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Detailed error information including stack traces and additional context
        /// Used for debugging and troubleshooting sync issues
        /// </summary>
        [StringLength(4000)]
        public string? ErrorDetails { get; set; }

        /// <summary>
        /// Additional details about the sync operation
        /// Can include configuration used, API endpoints called, etc.
        /// </summary>
        [StringLength(2000)]
        public string? AdditionalDetails { get; set; }

        /// <summary>
        /// Percentage of completion for in-progress sync operations
        /// Range: 0-100
        /// </summary>
        public int ProgressPercentage { get; set; }

        /// <summary>
        /// Current step or phase of the sync operation
        /// Example: "Fetching data from SIS", "Processing records", "Updating database"
        /// </summary>
        [StringLength(200)]
        public string? CurrentStep { get; set; }

        #endregion

        #region Configuration Information

        /// <summary>
        /// SIS API endpoint used for this sync operation
        /// </summary>
        [StringLength(500)]
        public string? ApiEndpoint { get; set; }

        /// <summary>
        /// Batch size used for processing records
        /// </summary>
        public int? BatchSize { get; set; }

        /// <summary>
        /// User ID of the administrator who triggered the sync (if manual)
        /// Null for automated/scheduled syncs
        /// </summary>
        [StringLength(450)]
        public string? TriggeredByUserId { get; set; }

        /// <summary>
        /// Indicates if this was an automated sync (scheduled) or manual sync
        /// </summary>
        public bool IsAutomated { get; set; }

        #endregion

        #region Business Logic Methods

        /// <summary>
        /// Marks the sync operation as completed successfully
        /// </summary>
        public void MarkAsCompleted()
        {
            Status = SyncStatus.Completed;
            EndTime = DateTime.UtcNow;
            ProgressPercentage = 100;
            CurrentStep = "Completed";
            CalculateDuration();
        }

        /// <summary>
        /// Marks the sync operation as failed with the specified error
        /// </summary>
        /// <param name="errorMessage">Primary error message</param>
        /// <param name="errorDetails">Detailed error information</param>
        public void MarkAsFailed(string errorMessage, string? errorDetails = null)
        {
            Status = SyncStatus.Failed;
            EndTime = DateTime.UtcNow;
            ErrorMessage = errorMessage?.Length > 1000 ? errorMessage[..1000] : errorMessage;
            ErrorDetails = errorDetails?.Length > 4000 ? errorDetails[..4000] : errorDetails;
            CurrentStep = "Failed";
            CalculateDuration();
        }

        /// <summary>
        /// Marks the sync operation as cancelled
        /// </summary>
        /// <param name="reason">Reason for cancellation</param>
        public void MarkAsCancelled(string? reason = null)
        {
            Status = SyncStatus.Cancelled;
            EndTime = DateTime.UtcNow;
            CurrentStep = "Cancelled";
            if (!string.IsNullOrWhiteSpace(reason))
            {
                AdditionalDetails = reason;
            }
            CalculateDuration();
        }

        /// <summary>
        /// Updates the progress of the sync operation
        /// </summary>
        /// <param name="percentage">Progress percentage (0-100)</param>
        /// <param name="currentStep">Current step description</param>
        public void UpdateProgress(int percentage, string? currentStep = null)
        {
            ProgressPercentage = Math.Max(0, Math.Min(100, percentage));
            if (!string.IsNullOrWhiteSpace(currentStep))
            {
                CurrentStep = currentStep.Length > 200 ? currentStep[..200] : currentStep;
            }
        }

        /// <summary>
        /// Calculates and sets the duration of the sync operation
        /// </summary>
        private void CalculateDuration()
        {
            if (EndTime.HasValue)
            {
                DurationMs = (long)(EndTime.Value - StartTime).TotalMilliseconds;
            }
        }

        /// <summary>
        /// Gets a human-readable duration string
        /// </summary>
        /// <returns>Duration in format "X minutes Y seconds" or "X seconds"</returns>
        public string GetDurationString()
        {
            if (!DurationMs.HasValue) return "In progress";

            var duration = TimeSpan.FromMilliseconds(DurationMs.Value);
            if (duration.TotalMinutes >= 1)
            {
                return $"{(int)duration.TotalMinutes} minutes {duration.Seconds} seconds";
            }
            return $"{duration.TotalSeconds:F1} seconds";
        }

        /// <summary>
        /// Gets the success rate as a percentage
        /// </summary>
        /// <returns>Success rate percentage</returns>
        public double GetSuccessRate()
        {
            if (RecordsProcessed == 0) return 0;
            var successfulRecords = RecordsAdded + RecordsUpdated + RecordsSkipped;
            return (double)successfulRecords / RecordsProcessed * 100;
        }

        /// <summary>
        /// Checks if the sync operation is currently running
        /// </summary>
        /// <returns>True if in progress, false otherwise</returns>
        public bool IsInProgress()
        {
            return Status == SyncStatus.InProgress;
        }

        /// <summary>
        /// Checks if the sync operation completed successfully
        /// </summary>
        /// <returns>True if completed successfully, false otherwise</returns>
        public bool IsSuccessful()
        {
            return Status == SyncStatus.Completed;
        }

        #endregion
    }

    /// <summary>
    /// Enumeration of synchronization operation types
    /// </summary>
    public enum SyncType
    {
        /// <summary>
        /// Full synchronization - processes all student records from SIS
        /// </summary>
        Full = 1,

        /// <summary>
        /// Incremental synchronization - processes only changed records since last sync
        /// </summary>
        Incremental = 2
    }

    /// <summary>
    /// Enumeration of synchronization operation statuses
    /// </summary>
    public enum SyncStatus
    {
        /// <summary>
        /// Sync operation is currently in progress
        /// </summary>
        InProgress = 1,

        /// <summary>
        /// Sync operation completed successfully
        /// </summary>
        Completed = 2,

        /// <summary>
        /// Sync operation failed due to an error
        /// </summary>
        Failed = 3,

        /// <summary>
        /// Sync operation was cancelled by user or system
        /// </summary>
        Cancelled = 4
    }
}
